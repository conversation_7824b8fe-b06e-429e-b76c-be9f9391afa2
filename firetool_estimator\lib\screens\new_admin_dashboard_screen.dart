import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../constants/app_constants.dart';
import 'login_screen.dart';
import 'complete_excel_grid_factory.dart';

class NewAdminDashboardScreen extends StatefulWidget {
  const NewAdminDashboardScreen({super.key});

  @override
  State<NewAdminDashboardScreen> createState() => _NewAdminDashboardScreenState();
}

class _NewAdminDashboardScreenState extends State<NewAdminDashboardScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    if (!authService.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Admin Dashboard'),
          backgroundColor: AppConstants.primaryColor,
        ),
        body: const Center(
          child: Text('You do not have permission to access this page.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await authService.logout();
              if (!context.mounted) return;
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const LoginScreen()),
              );
            },
            tooltip: 'Logout',
          ),
        ],
      ),
      body: Column(
        children: [
          // Admin header with welcome message
          Container(
            padding: const EdgeInsets.all(16.0),
            color: AppConstants.primaryColor,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome, ${authService.currentUser?.username.split('@').first ?? 'Admin'}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Manage your FireTool database with Excel-like functionality',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),

          // Tab bar for switching between views
          TabBar(
            controller: _tabController,
            labelColor: AppConstants.primaryColor,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppConstants.primaryColor,
            tabs: const [
              Tab(text: 'FIRE SYSTEMS'),
              Tab(text: 'SETTINGS'),
            ],
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFireSystemsTab(),
                _buildSettingsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFireSystemsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Fire Systems Database',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Grid of system cards
          GridView.count(
            crossAxisCount: 3,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            children: [
              _buildSystemCard(
                title: 'Fire Alarm Systems',
                icon: Icons.notifications_active,
                color: Colors.red.shade700,
                onTap: () => _navigateToExcelDatabase('alarm'),
              ),
              _buildSystemCard(
                title: 'Water Systems',
                icon: Icons.water_drop,
                color: Colors.blue.shade700,
                onTap: () => _navigateToExcelDatabase('water'),
              ),
              _buildSystemCard(
                title: 'Foam Systems',
                icon: Icons.bubble_chart,
                color: Colors.amber.shade700,
                onTap: () => _navigateToExcelDatabase('foam'),
              ),
              _buildSystemCard(
                title: 'FM200 Systems',
                icon: Icons.gas_meter,
                color: Colors.green.shade700,
                onTap: () => _navigateToExcelDatabase('fm200'),
              ),
              _buildSystemCard(
                title: 'Novec Systems',
                icon: Icons.science,
                color: Colors.teal.shade700,
                onTap: () => _navigateToExcelDatabase('novec'),
              ),
              _buildSystemCard(
                title: 'CO2 Systems',
                icon: Icons.air,
                color: Colors.purple.shade700,
                onTap: () => _navigateToExcelDatabase('co2'),
              ),
              _buildSystemCard(
                title: 'Materials',
                icon: Icons.category,
                color: Colors.brown.shade700,
                onTap: () => _navigateToExcelDatabase('materials'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Admin Settings',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Settings cards
          GridView.count(
            crossAxisCount: 3,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            children: [
              _buildSystemCard(
                title: 'User Management',
                icon: Icons.people,
                color: Colors.indigo.shade700,
                onTap: () {
                  // Navigate to user management screen
                },
              ),
              _buildSystemCard(
                title: 'App Settings',
                icon: Icons.settings,
                color: Colors.grey.shade700,
                onTap: () {
                  // Navigate to app settings screen
                },
              ),
              _buildSystemCard(
                title: 'Backup & Restore',
                icon: Icons.backup,
                color: Colors.cyan.shade700,
                onTap: () {
                  // Navigate to backup & restore screen
                },
              ),


            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSystemCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToExcelDatabase(String systemType) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CompleteExcelGridFactory.createScreen(systemType),
      ),
    );
  }
}
