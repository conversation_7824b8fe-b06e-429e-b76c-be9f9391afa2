import 'package:flutter/material.dart';

class CustomDropdown<T> extends StatefulWidget {
  final List<T> items;
  final T? value;
  final String Function(T) displayItemFn;
  final String Function(T)? displayCategoryFn;
  final String Function(T)? displayDescriptionFn;
  final Function(T) onChanged;
  final String hintText;
  final bool showCategory;
  final bool showDescription;

  const CustomDropdown({
    super.key,
    required this.items,
    required this.displayItemFn,
    required this.onChanged,
    this.value,
    this.displayCategoryFn,
    this.displayDescriptionFn,
    this.hintText = 'Select an item',
    this.showCategory = false,
    this.showDescription = false,
  });

  @override
  State<CustomDropdown<T>> createState() => _CustomDropdownState<T>();
}

class _CustomDropdownState<T> extends State<CustomDropdown<T>> {
  final TextEditingController _searchController = TextEditingController();
  bool _isDropdownOpen = false;
  late List<T> _filteredItems;
  T? _selectedItem;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _filteredItems = List<T>.from(widget.items);
    _selectedItem = widget.value;
    if (_selectedItem != null) {
      _searchController.text = widget.displayItemFn(_selectedItem as T);
    }
  }

  @override
  void didUpdateWidget(CustomDropdown<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _selectedItem = widget.value;
      if (_selectedItem != null) {
        _searchController.text = widget.displayItemFn(_selectedItem as T);
      }
    }

    if (oldWidget.items != widget.items) {
      _filteredItems = List<T>.from(widget.items);
    }
  }

  @override
  void dispose() {
    _closeDropdown();
    _searchController.dispose();
    super.dispose();
  }

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    _filteredItems = List<T>.from(widget.items); // Reset filtered items to show all

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);

    setState(() {
      _isDropdownOpen = true;
    });
  }

  void _closeDropdown() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }

    setState(() {
      _isDropdownOpen = false;
    });
  }

  void _filterItems(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredItems = List<T>.from(widget.items);
      } else {
        _filteredItems = widget.items.where((item) {
          final itemText = widget.displayItemFn(item).toLowerCase();
          final searchText = query.toLowerCase();

          // Also search in category if available
          String category = '';
          if (widget.displayCategoryFn != null) {
            category = widget.displayCategoryFn!(item).toLowerCase();
          }

          return itemText.contains(searchText) || category.contains(searchText);
        }).toList();
      }
    });

    // Rebuild the overlay with new filtered items
    if (_isDropdownOpen) {
      _closeDropdown();
      _openDropdown();
    }
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, size.height + 5),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              constraints: BoxConstraints(
                maxHeight: 300,
                minWidth: size.width,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      onChanged: _filterItems,
                    ),
                  ),
                  Flexible(
                    child: _buildItemsList(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildItemsList() {
    if (_filteredItems.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Text('No items found'),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        final isSelected = _selectedItem == item;

        return InkWell(
          onTap: () => _selectItem(item),
          child: Container(
            color: isSelected ? const Color.fromRGBO(33, 150, 243, 0.1) : null,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.displayItemFn(item),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                if (widget.showCategory && widget.displayCategoryFn != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      widget.displayCategoryFn!(item),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                if (widget.showDescription && widget.displayDescriptionFn != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      widget.displayDescriptionFn!(item),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _selectItem(T item) {
    // Close dropdown first to prevent Flutter material dropdown assertion error
    _closeDropdown();

    // Then update state and call onChanged
    setState(() {
      _selectedItem = item;
      _searchController.text = ''; // Clear search text
    });

    // Call onChanged with the selected item after a short delay
    Future.delayed(const Duration(milliseconds: 50), () {
      widget.onChanged(item);
    });
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey.shade50,
        ),
        child: InkWell(
          onTap: _toggleDropdown,
          child: InputDecorator(
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              suffixIcon: Icon(
                _isDropdownOpen ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                color: Colors.grey,
              ),
            ),
            child: Text(
              _selectedItem != null ? widget.displayItemFn(_selectedItem as T) : widget.hintText,
              style: TextStyle(
                color: _selectedItem != null ? Colors.black : Colors.grey,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
