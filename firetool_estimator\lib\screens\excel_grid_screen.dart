import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../widgets/mock_complete_excel_grid.dart';

class ExcelGridScreen extends StatefulWidget {
  const ExcelGridScreen({super.key});

  @override
  State<ExcelGridScreen> createState() => _ExcelGridScreenState();
}

class _ExcelGridScreenState extends State<ExcelGridScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _systemTypes = [
    'alarm',
    'water',
    'foam',
    'fm200',
    'novec',
    'co2',
    'materials',
  ];
  
  final List<String> _systemNames = [
    'Fire Alarm Systems',
    'Water Systems',
    'Foam Systems',
    'FM200 Systems',
    'Novec Systems',
    'CO2 Systems',
    'Materials',
  ];
  
  final List<Color> _systemColors = [
    Colors.red.shade700,
    Colors.blue.shade700,
    Colors.amber.shade700,
    Colors.green.shade700,
    Colors.teal.shade700,
    Colors.purple.shade700,
    Colors.brown.shade700,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _systemTypes.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Excel Grid'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: _systemNames.map((name) => Tab(text: name)).toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: List.generate(
          _systemTypes.length,
          (index) => MockCompleteExcelGrid(
            collectionPath: _systemTypes[index],
            title: _systemNames[index],
            themeColor: _systemColors[index],
            predefinedColumns: [
              'model',
              'description',
              'manufacturer',
              'approval',
              'ex_works_price',
              'local_price',
              'installation_price',
            ],
          ),
        ),
      ),
    );
  }
}
