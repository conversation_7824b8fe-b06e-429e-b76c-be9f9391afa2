import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../constants/app_constants.dart';

class FlexibleDataGrid extends StatefulWidget {
  final String collectionPath;
  final String title;

  const FlexibleDataGrid({
    super.key,
    required this.collectionPath,
    required this.title,
  });

  @override
  State<FlexibleDataGrid> createState() => _FlexibleDataGridState();
}

class _FlexibleDataGridState extends State<FlexibleDataGrid> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  List<Map<String, dynamic>> _items = [];
  bool _isLoading = true;
  String? _error;

  // Default column headers
  final List<String> _columnHeaders = [
    'model',
    'description',
    'manufacturer',
    'approval',
    'ex_works_price',
    'local_price',
    'installation_price',
  ];

  // For column header editing
  final TextEditingController _headerEditController = TextEditingController();
  int? _editingHeaderIndex;

  // For cell selection
  final Map<String, Set<String>> _selectedCells = {}; // itemId -> Set of column names
  bool _isSelecting = false;
  String? _selectionStartItemId;
  String? _selectionStartColumn;

  // For bulk editing
  final TextEditingController _bulkEditController = TextEditingController();
  bool _isBulkEditing = false;

  // For cell editing
  final TextEditingController _cellEditController = TextEditingController();
  String? _editingItemId;
  String? _editingColumn;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _headerEditController.dispose();
    _bulkEditController.dispose();
    _cellEditController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final items = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      setState(() {
        _items = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _startHeaderEdit(int index) {
    setState(() {
      _editingHeaderIndex = index;
      _headerEditController.text = _columnHeaders[index];
    });
  }

  void _saveHeaderEdit() {
    if (_editingHeaderIndex != null) {
      setState(() {
        _columnHeaders[_editingHeaderIndex!] = _headerEditController.text;
        _editingHeaderIndex = null;
      });
    }
  }

  void _cancelHeaderEdit() {
    setState(() {
      _editingHeaderIndex = null;
    });
  }

  void _startCellEdit(String itemId, String column, dynamic value) {
    setState(() {
      _editingItemId = itemId;
      _editingColumn = column;
      _cellEditController.text = value?.toString() ?? '';
    });
  }

  Future<void> _saveCellEdit() async {
    if (_editingItemId != null && _editingColumn != null) {
      try {
        final value = _cellEditController.text;

        // Update in Firestore
        await FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(_editingItemId)
            .update({_editingColumn!: value});

        // Update local data
        setState(() {
          final index = _items.indexWhere((item) => item['id'] == _editingItemId);
          if (index >= 0) {
            _items[index][_editingColumn!] = value;
          }
          _editingItemId = null;
          _editingColumn = null;
        });
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error updating cell: $e')),
          );
        }
      }
    }
  }

  void _cancelCellEdit() {
    setState(() {
      _editingItemId = null;
      _editingColumn = null;
    });
  }

  void _handleCellTap(String itemId, String column) {
    // If we're already editing a cell, save it first
    if (_editingItemId != null && _editingColumn != null) {
      _saveCellEdit();
      return;
    }

    // If we're in selection mode, handle selection
    if (_isSelecting) {
      _toggleCellSelection(itemId, column);
      return;
    }

    // Otherwise, start editing this cell
    final item = _items.firstWhere((item) => item['id'] == itemId);
    _startCellEdit(itemId, column, item[column]);
  }

  void _handleCellMouseDown(String itemId, String column) {
    setState(() {
      _isSelecting = true;
      _selectionStartItemId = itemId;
      _selectionStartColumn = column;

      // Clear previous selection
      _selectedCells.clear();

      // Add this cell to selection
      if (!_selectedCells.containsKey(itemId)) {
        _selectedCells[itemId] = {};
      }
      _selectedCells[itemId]!.add(column);
    });
  }

  void _handleCellMouseEnter(String itemId, String column) {
    if (_isSelecting && _selectionStartItemId != null && _selectionStartColumn != null) {
      // Calculate the range of cells to select
      final startItemIndex = _items.indexWhere((item) => item['id'] == _selectionStartItemId);
      final currentItemIndex = _items.indexWhere((item) => item['id'] == itemId);

      final startColumnIndex = _columnHeaders.indexOf(_selectionStartColumn!);
      final currentColumnIndex = _columnHeaders.indexOf(column);

      if (startItemIndex >= 0 && currentItemIndex >= 0 &&
          startColumnIndex >= 0 && currentColumnIndex >= 0) {

        final minItemIndex = startItemIndex < currentItemIndex ? startItemIndex : currentItemIndex;
        final maxItemIndex = startItemIndex > currentItemIndex ? startItemIndex : currentItemIndex;

        final minColumnIndex = startColumnIndex < currentColumnIndex ? startColumnIndex : currentColumnIndex;
        final maxColumnIndex = startColumnIndex > currentColumnIndex ? startColumnIndex : currentColumnIndex;

        // Clear previous selection
        setState(() {
          _selectedCells.clear();

          // Select all cells in the range
          for (int i = minItemIndex; i <= maxItemIndex; i++) {
            final itemId = _items[i]['id'] as String;
            if (!_selectedCells.containsKey(itemId)) {
              _selectedCells[itemId] = {};
            }

            for (int j = minColumnIndex; j <= maxColumnIndex; j++) {
              _selectedCells[itemId]!.add(_columnHeaders[j]);
            }
          }
        });
      }
    }
  }

  void _handleCellMouseUp() {
    setState(() {
      _isSelecting = false;
    });
  }

  void _toggleCellSelection(String itemId, String column) {
    setState(() {
      if (!_selectedCells.containsKey(itemId)) {
        _selectedCells[itemId] = {};
      }

      if (_selectedCells[itemId]!.contains(column)) {
        _selectedCells[itemId]!.remove(column);
        if (_selectedCells[itemId]!.isEmpty) {
          _selectedCells.remove(itemId);
        }
      } else {
        _selectedCells[itemId]!.add(column);
      }
    });
  }

  void _startBulkEdit() {
    setState(() {
      _isBulkEditing = true;
      _bulkEditController.clear();
    });
  }

  Future<void> _applyBulkEdit() async {
    final newValue = _bulkEditController.text;

    // Apply to all selected cells
    for (var itemId in _selectedCells.keys) {
      final Map<String, dynamic> updates = {};

      for (var column in _selectedCells[itemId]!) {
        updates[column] = newValue;
      }

      if (updates.isNotEmpty) {
        try {
          await FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(itemId)
              .update(updates);

          // Update local data
          final index = _items.indexWhere((item) => item['id'] == itemId);
          if (index >= 0) {
            for (var column in updates.keys) {
              _items[index][column] = newValue;
            }
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error updating item: $e')),
            );
          }
        }
      }
    }

    setState(() {
      _selectedCells.clear();
      _isBulkEditing = false;
    });
  }

  void _cancelBulkEdit() {
    setState(() {
      _isBulkEditing = false;
    });
  }

  Future<void> _addNewRow() async {
    final Map<String, dynamic> newItem = {};

    // Add default empty values for all columns
    for (var column in _columnHeaders) {
      newItem[column] = '';
    }

    newItem['createdAt'] = FieldValue.serverTimestamp();

    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('New row added')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding row: $e')),
        );
      }
    }
  }

  Future<void> _deleteRow(String itemId) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(itemId)
          .delete();

      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Row deleted')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting row: $e')),
        );
      }
    }
  }

  Future<void> _importFromClipboard() async {
    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data == null || data.text == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No data in clipboard')),
          );
        }
        return;
      }

      // Parse clipboard data (assuming tab-separated values from Excel)
      final rows = data.text!.split('\n');
      if (rows.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No rows found in clipboard data')),
          );
        }
        return;
      }

      // Check if first row contains headers
      final firstRow = rows[0].split('\t');
      bool hasHeaders = true;

      // If the number of columns in the first row matches our column headers,
      // we'll assume it contains headers and update our column headers
      if (firstRow.length <= _columnHeaders.length) {
        setState(() {
          for (int i = 0; i < firstRow.length; i++) {
            if (firstRow[i].trim().isNotEmpty) {
              _columnHeaders[i] = firstRow[i].trim();
            }
          }
        });
      } else {
        // If there are more columns than we have, we'll assume it's data
        hasHeaders = false;
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      int count = 0;

      // Start from index 1 if we have headers, otherwise from 0
      final startIndex = hasHeaders ? 1 : 0;

      for (int i = startIndex; i < rows.length; i++) {
        if (rows[i].trim().isEmpty) continue;

        final values = rows[i].split('\t');
        if (values.isEmpty) continue;

        final Map<String, dynamic> item = {};

        // Map values to our column headers
        for (int j = 0; j < values.length && j < _columnHeaders.length; j++) {
          if (values[j].trim().isNotEmpty) {
            item[_columnHeaders[j]] = values[j].trim();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance.collection(widget.collectionPath).doc();
          batch.set(docRef, item);
          count++;
        }
      }

      await batch.commit();

      // Reload data
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Imported $count items from clipboard')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error importing from clipboard: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    // Check if user is admin
    if (!authService.isAdmin) {
      return const Center(child: Text('Admin access required'));
    }

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Toolbar
        Container(
          padding: const EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_selectedCells.isNotEmpty)
                Row(
                  children: [
                    Text('${_countSelectedCells()} cells selected'),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _startBulkEdit,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Edit Selected'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _selectedCells.clear();
                        });
                      },
                      child: const Text('Clear Selection'),
                    ),
                  ],
                ),
              if (_selectedCells.isEmpty) ...[
                ElevatedButton.icon(
                  onPressed: _loadData,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: _importFromClipboard,
                  icon: const Icon(Icons.paste),
                  label: const Text('Import from Excel'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.accentColor,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: _addNewRow,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Row'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ],
          ),
        ),

        // Bulk edit dialog
        if (_isBulkEditing)
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.yellow.shade100,
            child: Row(
              children: [
                const Text('Enter value for selected cells:'),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _bulkEditController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _applyBulkEdit,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Apply'),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: _cancelBulkEdit,
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ),

        // Data grid
        Expanded(
          child: _items.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.info_outline, size: 48, color: Colors.grey),
                      const SizedBox(height: 16),
                      const Text(
                        'No items found',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _addNewRow,
                        icon: const Icon(Icons.add),
                        label: const Text('Add First Row'),
                      ),
                    ],
                  ),
                )
              : _buildDataGrid(),
        ),
      ],
    );
  }

  int _countSelectedCells() {
    int count = 0;
    for (var columns in _selectedCells.values) {
      count += columns.length;
    }
    return count;
  }

  Widget _buildDataGrid() {
    return Scrollbar(
      controller: _verticalController,
      thumbVisibility: true,
      child: Scrollbar(
        controller: _horizontalController,
        thumbVisibility: true,
        notificationPredicate: (notification) => notification.depth == 1,
        child: SingleChildScrollView(
          controller: _verticalController,
          child: SingleChildScrollView(
            controller: _horizontalController,
            scrollDirection: Axis.horizontal,
            child: DataTable(
              // Use default colors for simplicity
              // headingRowColor and dataRowColor properties are deprecated
              columns: [
                const DataColumn(
                  label: Text(
                    'ID',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                ..._columnHeaders.asMap().entries.map((entry) {
                  final index = entry.key;
                  final header = entry.value;

                  return DataColumn(
                    label: _editingHeaderIndex == index
                        ? SizedBox(
                            width: 150,
                            child: TextField(
                              controller: _headerEditController,
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                border: OutlineInputBorder(),
                              ),
                              onSubmitted: (_) => _saveHeaderEdit(),
                              onEditingComplete: _saveHeaderEdit,
                            ),
                          )
                        : GestureDetector(
                            onDoubleTap: () => _startHeaderEdit(index),
                            child: Text(
                              header,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                  );
                }),
                const DataColumn(
                  label: Text(
                    'Actions',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
              rows: _items.map((item) {
                final itemId = item['id'] as String;

                return DataRow(
                  cells: [
                    DataCell(
                      Text(
                        itemId,
                        style: const TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ),
                    ..._columnHeaders.map((column) {
                      final isSelected = _selectedCells[itemId]?.contains(column) ?? false;
                      final isEditing = _editingItemId == itemId && _editingColumn == column;

                      return DataCell(
                        Container(
                          color: isSelected ? const Color.fromRGBO(33, 150, 243, 0.2) : null,
                          child: isEditing
                              ? SizedBox(
                                  width: 150,
                                  child: TextField(
                                    controller: _cellEditController,
                                    decoration: const InputDecoration(
                                      contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      border: OutlineInputBorder(),
                                    ),
                                    onSubmitted: (_) => _saveCellEdit(),
                                    onEditingComplete: _saveCellEdit,
                                  ),
                                )
                              : GestureDetector(
                                  onTap: () => _handleCellTap(itemId, column),
                                  onTapDown: (_) => _handleCellMouseDown(itemId, column),
                                  onTapUp: (_) => _handleCellMouseUp(),
                                  child: MouseRegion(
                                    onEnter: (_) => _handleCellMouseEnter(itemId, column),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Text(item[column]?.toString() ?? ''),
                                    ),
                                  ),
                                ),
                        ),
                        onTap: () => _handleCellTap(itemId, column),
                      );
                    }),
                    DataCell(
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                            onPressed: () => _deleteRow(itemId),
                            tooltip: 'Delete Row',
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }
}
