import 'package:flutter/material.dart';
import '../models/super_database_models.dart';

class CreateSubsectionDialog extends StatefulWidget {
  final String sectionId;

  const CreateSubsectionDialog({
    super.key,
    required this.sectionId,
  });

  @override
  State<CreateSubsectionDialog> createState() => _CreateSubsectionDialogState();
}

class _CreateSubsectionDialogState extends State<CreateSubsectionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _descriptionController = TextEditingController();

  final List<ColumnDefinition> _columns = [];

  @override
  void initState() {
    super.initState();
    _displayNameController.addListener(_updateNameFromDisplayName);

    // Add some default columns to get started
    _columns.addAll([
      ColumnDefinition(
        name: 'name',
        displayName: 'Name',
        type: ColumnType.text,
        isRequired: true,
      ),
      ColumnDefinition(
        name: 'description',
        displayName: 'Description',
        type: ColumnType.text,
      ),
    ]);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _displayNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _updateNameFromDisplayName() {
    final displayName = _displayNameController.text;
    final sanitizedName = displayName
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .replaceAll(RegExp(r'\s+'), '_');
    _nameController.text = sanitizedName;
  }

  void _addColumn() {
    setState(() {
      _columns.add(ColumnDefinition(
        name: 'column_${_columns.length}',
        displayName: 'Column ${_columns.length}',
        type: ColumnType.text,
      ));
    });
  }

  void _removeColumn(int index) {
    if (_columns.length <= 1) return; // Don't allow removing the last column
    setState(() {
      _columns.removeAt(index);
    });
  }

  void _editColumn(int index) {
    showDialog(
      context: context,
      builder: (context) => _ColumnEditDialog(
        column: _columns[index],
        onSave: (updatedColumn) {
          setState(() {
            _columns[index] = updatedColumn;
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New Subsection'),
      content: SizedBox(
        width: 600,
        height: 500,
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Display name
              TextFormField(
                controller: _displayNameController,
                decoration: const InputDecoration(
                  labelText: 'Display Name *',
                  hintText: 'e.g., Fire Alarm Devices',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Display name is required';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Internal name (auto-generated)
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Internal Name',
                  hintText: 'Auto-generated from display name',
                  border: OutlineInputBorder(),
                ),
                readOnly: true,
                style: TextStyle(color: Colors.grey.shade600),
              ),

              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Optional description for this subsection',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),

              const SizedBox(height: 24),

              // Columns section
              Row(
                children: [
                  Text(
                    'Columns',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: _addColumn,
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Add Column'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Columns list
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListView.builder(
                    itemCount: _columns.length,
                    itemBuilder: (context, index) {
                      final column = _columns[index];
                      final isLastColumn = _columns.length == 1;

                      return ListTile(
                        leading: Icon(
                          _getColumnTypeIcon(column.type),
                          color: Theme.of(context).primaryColor,
                        ),
                        title: Text(
                          column.displayName,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        subtitle: Text(
                          '${column.type.displayName}${column.isRequired ? ' • Required' : ''}${column.isUnique ? ' • Unique' : ''}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                          ),
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit, size: 18),
                              onPressed: () => _editColumn(index),
                              tooltip: 'Edit column',
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.delete,
                                size: 18,
                                color: isLastColumn ? Colors.grey : Colors.red,
                              ),
                              onPressed: isLastColumn ? null : () => _removeColumn(index),
                              tooltip: isLastColumn ? 'Cannot delete last column' : 'Delete column',
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _createSubsection,
          child: const Text('Create'),
        ),
      ],
    );
  }

  IconData _getColumnTypeIcon(ColumnType type) {
    switch (type) {
      case ColumnType.text:
        return Icons.text_fields;
      case ColumnType.integer:
        return Icons.numbers;
      case ColumnType.real:
        return Icons.numbers;
      case ColumnType.boolean:
        return Icons.check_box;
      case ColumnType.date:
        return Icons.calendar_today;
      case ColumnType.datetime:
        return Icons.access_time;
      case ColumnType.currency:
        return Icons.attach_money;
      case ColumnType.email:
        return Icons.email;
      case ColumnType.url:
        return Icons.link;
      case ColumnType.phone:
        return Icons.phone;
      case ColumnType.json:
        return Icons.code;
    }
  }

  void _createSubsection() {
    if (_formKey.currentState!.validate()) {
      if (_columns.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please add at least one column'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final result = {
        'name': _nameController.text.trim(),
        'displayName': _displayNameController.text.trim(),
        'description': _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        'columns': _columns,
      };

      Navigator.of(context).pop(result);
    }
  }
}

class _ColumnEditDialog extends StatefulWidget {
  final ColumnDefinition column;
  final Function(ColumnDefinition) onSave;

  const _ColumnEditDialog({
    required this.column,
    required this.onSave,
  });

  @override
  State<_ColumnEditDialog> createState() => _ColumnEditDialogState();
}

class _ColumnEditDialogState extends State<_ColumnEditDialog> {
  late TextEditingController _nameController;
  late TextEditingController _displayNameController;
  late TextEditingController _descriptionController;
  late ColumnType _selectedType;
  late bool _isRequired;
  late bool _isUnique;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.column.name);
    _displayNameController = TextEditingController(text: widget.column.displayName);
    _descriptionController = TextEditingController(text: widget.column.description ?? '');
    _selectedType = widget.column.type;
    _isRequired = widget.column.isRequired;
    _isUnique = widget.column.isUnique;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _displayNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Column'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'Display Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<ColumnType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Type',
                border: OutlineInputBorder(),
              ),
              items: ColumnType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: const Text('Required'),
              value: _isRequired,
              onChanged: (bool? value) {
                setState(() {
                  _isRequired = value ?? false;
                });
              },
            ),
            CheckboxListTile(
              title: const Text('Unique'),
              value: _isUnique,
              onChanged: (bool? value) {
                setState(() {
                  _isUnique = value ?? false;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final updatedColumn = widget.column.copyWith(
              displayName: _displayNameController.text.trim(),
              type: _selectedType,
              description: _descriptionController.text.trim().isEmpty
                  ? null
                  : _descriptionController.text.trim(),
              isRequired: _isRequired,
              isUnique: _isUnique,
            );
            widget.onSave(updatedColumn);
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
