import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../constants/app_constants.dart';

class SpreadsheetGrid extends StatefulWidget {
  final String collectionPath;

  const SpreadsheetGrid({
    super.key,
    required this.collectionPath,
  });

  @override
  State<SpreadsheetGrid> createState() => _SpreadsheetGridState();
}

class _SpreadsheetGridState extends State<SpreadsheetGrid> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  // Data state
  List<Map<String, dynamic>> _items = [];
  Set<String> _allColumns = {};
  List<String> _visibleColumns = [];
  bool _isLoading = true;
  String? _error;

  // Cell selection state
  int? _selectedRowIndex;
  int? _selectedColIndex;
  int? _selectionStartRowIndex;
  int? _selectionStartColIndex;
  int? _selectionEndRowIndex;
  int? _selectionEndColIndex;

  // Editing state
  final FocusNode _gridFocusNode = FocusNode();
  final TextEditingController _editingController = TextEditingController();
  bool _isEditing = false;
  String? _editingItemId;
  String? _editingColumn;

  // Column width state
  final Map<String, double> _columnWidths = {};
  final double _defaultColumnWidth = 150.0;
  final double _minColumnWidth = 80.0;
  final double _maxColumnWidth = 400.0;

  // New row state
  final Map<String, TextEditingController> _newRowControllers = {};

  // New column state
  final TextEditingController _newColumnController = TextEditingController();

  // Clipboard state
  List<List<String>> _clipboardData = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _gridFocusNode.dispose();
    _editingController.dispose();
    _newColumnController.dispose();

    for (var controller in _newRowControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final items = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Extract all possible columns from all items
      final Set<String> allColumns = {'id'};
      for (var item in items) {
        allColumns.addAll(item.keys);
      }

      // Initialize column widths
      for (var column in allColumns) {
        if (!_columnWidths.containsKey(column)) {
          _columnWidths[column] = _defaultColumnWidth;
        }
      }

      // Initialize controllers for new row
      for (var column in allColumns) {
        if (column != 'id' && !_newRowControllers.containsKey(column)) {
          _newRowControllers[column] = TextEditingController();
        }
      }

      setState(() {
        _items = items;
        _allColumns = allColumns;
        _visibleColumns = allColumns.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  // Selection methods
  void _selectCell(int rowIndex, int colIndex, {bool isShiftSelect = false}) {
    setState(() {
      if (isShiftSelect && _selectedRowIndex != null && _selectedColIndex != null) {
        // Extend selection
        _selectionEndRowIndex = rowIndex;
        _selectionEndColIndex = colIndex;
      } else {
        // Start new selection
        _selectedRowIndex = rowIndex;
        _selectedColIndex = colIndex;
        _selectionStartRowIndex = rowIndex;
        _selectionStartColIndex = colIndex;
        _selectionEndRowIndex = rowIndex;
        _selectionEndColIndex = colIndex;
      }
    });
  }

  bool _isCellSelected(int rowIndex, int colIndex) {
    if (_selectionStartRowIndex == null || _selectionEndRowIndex == null ||
        _selectionStartColIndex == null || _selectionEndColIndex == null) {
      return false;
    }

    final minRow = _selectionStartRowIndex! < _selectionEndRowIndex!
        ? _selectionStartRowIndex! : _selectionEndRowIndex!;
    final maxRow = _selectionStartRowIndex! < _selectionEndRowIndex!
        ? _selectionEndRowIndex! : _selectionStartRowIndex!;
    final minCol = _selectionStartColIndex! < _selectionEndColIndex!
        ? _selectionStartColIndex! : _selectionEndColIndex!;
    final maxCol = _selectionStartColIndex! < _selectionEndColIndex!
        ? _selectionEndColIndex! : _selectionStartColIndex!;

    return rowIndex >= minRow && rowIndex <= maxRow &&
           colIndex >= minCol && colIndex <= maxCol;
  }

  // Editing methods
  void _startEditing(int rowIndex, int colIndex) {
    if (rowIndex < 0 || rowIndex >= _items.length) return;
    if (colIndex < 0 || colIndex >= _visibleColumns.length) return;

    final column = _visibleColumns[colIndex];
    if (column == 'id') return; // Can't edit ID column

    final item = _items[rowIndex];
    final value = item[column]?.toString() ?? '';

    setState(() {
      _isEditing = true;
      _editingItemId = item['id'];
      _editingColumn = column;
      _editingController.text = value;
    });

    // Focus will be requested in the build method
  }

  void _finishEditing() {
    if (!_isEditing || _editingItemId == null || _editingColumn == null) return;

    final newValue = _editingController.text;
    final itemIndex = _items.indexWhere((item) => item['id'] == _editingItemId);

    if (itemIndex >= 0) {
      final currentValue = _items[itemIndex][_editingColumn]?.toString() ?? '';

      if (newValue != currentValue) {
        _updateCell(_editingItemId!, _editingColumn!, newValue);
      }
    }

    setState(() {
      _isEditing = false;
      _editingItemId = null;
      _editingColumn = null;
    });
  }

  // Data manipulation methods
  Future<void> _updateCell(String id, String column, String value) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .update({column: value});

      // Update local data
      setState(() {
        final itemIndex = _items.indexWhere((item) => item['id'] == id);
        if (itemIndex >= 0) {
          _items[itemIndex][column] = value;
        }
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating cell: $e')),
      );
    }
  }

  Future<void> _addRow() async {
    final Map<String, dynamic> newItem = {};

    // Collect values from controllers
    for (var entry in _newRowControllers.entries) {
      if (entry.value.text.isNotEmpty) {
        newItem[entry.key] = entry.value.text;
      }
    }

    if (newItem.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill at least one field')),
      );
      return;
    }

    // Add timestamp
    newItem['createdAt'] = FieldValue.serverTimestamp();

    try {
      // Add to Firestore
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Clear controllers
      for (var controller in _newRowControllers.values) {
        controller.clear();
      }

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Row added successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding row: $e')),
      );
    }
  }

  Future<void> _deleteRow(String id) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .delete();

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Row deleted')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting row: $e')),
      );
    }
  }

  void _addColumn() {
    final newColumnName = _newColumnController.text.trim();
    if (newColumnName.isEmpty) return;

    setState(() {
      _allColumns.add(newColumnName);
      _visibleColumns.add(newColumnName);
      _columnWidths[newColumnName] = _defaultColumnWidth;
      _newColumnController.clear();

      // Add controller for new column in new row form
      _newRowControllers[newColumnName] = TextEditingController();
    });
  }

  void _removeColumn(String column) {
    if (column == 'id') return; // Can't remove ID column

    setState(() {
      _visibleColumns.remove(column);
    });
  }

  // Clipboard methods
  void _copySelection() {
    if (_selectionStartRowIndex == null || _selectionEndRowIndex == null ||
        _selectionStartColIndex == null || _selectionEndColIndex == null) {
      return;
    }

    final minRow = _selectionStartRowIndex! < _selectionEndRowIndex!
        ? _selectionStartRowIndex! : _selectionEndRowIndex!;
    final maxRow = _selectionStartRowIndex! < _selectionEndRowIndex!
        ? _selectionEndRowIndex! : _selectionStartRowIndex!;
    final minCol = _selectionStartColIndex! < _selectionEndColIndex!
        ? _selectionStartColIndex! : _selectionEndColIndex!;
    final maxCol = _selectionStartColIndex! < _selectionEndColIndex!
        ? _selectionEndColIndex! : _selectionStartColIndex!;

    final List<List<String>> data = [];

    for (int i = minRow; i <= maxRow; i++) {
      if (i >= _items.length) continue;

      final List<String> rowData = [];
      for (int j = minCol; j <= maxCol; j++) {
        if (j >= _visibleColumns.length) continue;

        final column = _visibleColumns[j];
        final value = _items[i][column]?.toString() ?? '';
        rowData.add(value);
      }

      data.add(rowData);
    }

    _clipboardData = data;

    // Format for system clipboard
    final String clipboardText = data.map((row) => row.join('\t')).join('\n');
    Clipboard.setData(ClipboardData(text: clipboardText));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Selection copied to clipboard')),
    );
  }

  Future<void> _pasteAtSelection() async {
    if (_selectedRowIndex == null || _selectedColIndex == null) return;

    try {
      final ClipboardData? clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData == null || clipboardData.text == null) return;

      final rows = clipboardData.text!.split('\n');
      final List<List<String>> data = [];

      for (var row in rows) {
        if (row.trim().isEmpty) continue;
        data.add(row.split('\t'));
      }

      if (data.isEmpty) return;

      // Apply paste data
      for (int i = 0; i < data.length; i++) {
        final rowIndex = _selectedRowIndex! + i;
        if (rowIndex >= _items.length) continue;

        for (int j = 0; j < data[i].length; j++) {
          final colIndex = _selectedColIndex! + j;
          if (colIndex >= _visibleColumns.length) continue;

          final column = _visibleColumns[colIndex];
          if (column == 'id') continue; // Skip ID column

          final value = data[i][j];
          final itemId = _items[rowIndex]['id'];

          _updateCell(itemId, column, value);
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error pasting data: $e')),
      );
    }
  }

  Future<void> _importFromClipboard() async {
    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data == null || data.text == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No data in clipboard')),
        );
        return;
      }

      // Parse clipboard data (assuming tab-separated values from Excel)
      final rows = data.text!.split('\n');
      if (rows.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No rows found in clipboard data')),
        );
        return;
      }

      // Parse header row
      final headers = rows[0].split('\t');

      // Add new columns if needed
      for (var header in headers) {
        final trimmedHeader = header.trim();
        if (trimmedHeader.isNotEmpty && !_allColumns.contains(trimmedHeader)) {
          _allColumns.add(trimmedHeader);
          _visibleColumns.add(trimmedHeader);
          _columnWidths[trimmedHeader] = _defaultColumnWidth;
          _newRowControllers[trimmedHeader] = TextEditingController();
        }
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      int count = 0;

      for (int i = 1; i < rows.length; i++) {
        if (rows[i].trim().isEmpty) continue;

        final values = rows[i].split('\t');
        if (values.length != headers.length) continue;

        final Map<String, dynamic> item = {};
        for (int j = 0; j < headers.length; j++) {
          if (headers[j].trim().isNotEmpty && values[j].trim().isNotEmpty) {
            item[headers[j].trim()] = values[j].trim();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance.collection(widget.collectionPath).doc();
          batch.set(docRef, item);
          count++;
        }
      }

      await batch.commit();

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Imported $count items from clipboard')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error importing from clipboard: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    // Check if user is admin
    if (!authService.isAdmin) {
      return const Center(child: Text('Admin access required'));
    }

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      // Check if it's a permission error
      if (_error!.contains('permission-denied')) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.security, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'Firebase Permission Error',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Your Firebase security rules need to be updated to allow database access.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('Try Again'),
                ),
              ],
            ),
          ),
        );
      }
      return Center(child: Text(_error!, style: const TextStyle(color: Colors.red)));
    }

    return Column(
      children: [
        // Toolbar
        Container(
          padding: const EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Main toolbar
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: _loadData,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Refresh'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _importFromClipboard,
                    icon: const Icon(Icons.paste),
                    label: const Text('Import from Excel'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _addRow,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Row'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _copySelection,
                    icon: const Icon(Icons.content_copy),
                    label: const Text('Copy'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _pasteAtSelection,
                    icon: const Icon(Icons.content_paste),
                    label: const Text('Paste'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_items.length} rows',
                      style: TextStyle(
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              // Column management
              const SizedBox(height: 12),
              Row(
                children: [
                  const Text('Add Column:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(width: 8),
                  SizedBox(
                    width: 200,
                    child: TextField(
                      controller: _newColumnController,
                      decoration: const InputDecoration(
                        hintText: 'New column name',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      onSubmitted: (_) => _addColumn(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: _addColumn,
                    icon: const Icon(Icons.add_circle, color: Colors.green),
                    tooltip: 'Add Column',
                  ),
                  const SizedBox(width: 16),
                  const Text('Visible Columns:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(width: 8),
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: _allColumns.map((column) {
                          final isVisible = _visibleColumns.contains(column);
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text(column),
                              selected: isVisible,
                              onSelected: (selected) {
                                setState(() {
                                  if (selected) {
                                    _visibleColumns.add(column);
                                  } else if (column != 'id') { // Can't hide ID column
                                    _visibleColumns.remove(column);
                                  }
                                });
                              },
                              backgroundColor: Colors.grey.shade200,
                              selectedColor: AppConstants.primaryColor.withAlpha(100),
                              checkmarkColor: AppConstants.primaryColor,
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Help text for new users
        if (_items.isEmpty)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue),
                    SizedBox(width: 8),
                    Text(
                      'Spreadsheet Grid',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'How to use:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                const Text('• Click on a cell to select it'),
                const Text('• Double-click on a cell to edit it'),
                const Text('• Use Shift+Click to select multiple cells'),
                const Text('• Use Copy/Paste buttons for clipboard operations'),
                const Text('• Add/remove columns using the controls above'),
                const Text('• Import data directly from Excel using the "Import from Excel" button'),
              ],
            ),
          ),

        // Spreadsheet grid
        Expanded(
          child: Card(
            margin: const EdgeInsets.all(8.0),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Focus(
              focusNode: _gridFocusNode,
              onKeyEvent: (node, event) {
                // Handle keyboard shortcuts
                if (event is KeyDownEvent) {
                  if (HardwareKeyboard.instance.isControlPressed && event.logicalKey == LogicalKeyboardKey.keyC) {
                    _copySelection();
                    return KeyEventResult.handled;
                  } else if (HardwareKeyboard.instance.isControlPressed && event.logicalKey == LogicalKeyboardKey.keyV) {
                    _pasteAtSelection();
                    return KeyEventResult.handled;
                  } else if (event.logicalKey == LogicalKeyboardKey.enter) {
                    if (_selectedRowIndex != null && _selectedColIndex != null && !_isEditing) {
                      _startEditing(_selectedRowIndex!, _selectedColIndex!);
                      return KeyEventResult.handled;
                    } else if (_isEditing) {
                      _finishEditing();
                      return KeyEventResult.handled;
                    }
                  } else if (event.logicalKey == LogicalKeyboardKey.escape) {
                    if (_isEditing) {
                      setState(() {
                        _isEditing = false;
                        _editingItemId = null;
                        _editingColumn = null;
                      });
                      return KeyEventResult.handled;
                    }
                  }
                }
                return KeyEventResult.ignored;
              },
              child: Column(
                children: [
                  // Table header
                  Container(
                    color: const Color(0xFFE3F2FD),
                    child: Row(
                      children: [
                        // Row number column
                        Container(
                          width: 50,
                          padding: const EdgeInsets.all(8),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: const Text(
                            '#',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        // Data columns
                        ...List.generate(_visibleColumns.length, (index) {
                          final column = _visibleColumns[index];
                          return GestureDetector(
                            onDoubleTap: () {
                              // TODO: Implement column width adjustment
                            },
                            child: Container(
                              width: _columnWidths[column] ?? _defaultColumnWidth,
                              padding: const EdgeInsets.all(8),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Text(
                                      column,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  if (column != 'id')
                                    IconButton(
                                      icon: const Icon(Icons.close, size: 16),
                                      onPressed: () => _removeColumn(column),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                      tooltip: 'Remove Column',
                                    ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),

                  // Table body
                  Expanded(
                    child: Scrollbar(
                      controller: _verticalController,
                      thumbVisibility: true,
                      child: Scrollbar(
                        controller: _horizontalController,
                        thumbVisibility: true,
                        notificationPredicate: (notification) => notification.depth == 1,
                        child: SingleChildScrollView(
                          controller: _verticalController,
                          child: SingleChildScrollView(
                            controller: _horizontalController,
                            scrollDirection: Axis.horizontal,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(_items.length, (rowIndex) {
                                final item = _items[rowIndex];
                                return Row(
                                  children: [
                                    // Row number and delete button
                                    Container(
                                      width: 50,
                                      height: 40,
                                      padding: const EdgeInsets.all(4),
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey.shade300),
                                        color: Colors.grey.shade50,
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            '${rowIndex + 1}',
                                            style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey,
                                            ),
                                          ),
                                          IconButton(
                                            icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                                            onPressed: () => _deleteRow(item['id']),
                                            tooltip: 'Delete Row',
                                            padding: EdgeInsets.zero,
                                            constraints: const BoxConstraints(),
                                          ),
                                        ],
                                      ),
                                    ),
                                    // Data cells
                                    ...List.generate(_visibleColumns.length, (colIndex) {
                                      final column = _visibleColumns[colIndex];
                                      final value = item[column]?.toString() ?? '';
                                      final isSelected = _isCellSelected(rowIndex, colIndex);
                                      final isEditing = _isEditing &&
                                                       _editingItemId == item['id'] &&
                                                       _editingColumn == column;

                                      return GestureDetector(
                                        onTap: () {
                                          if (isEditing) return;

                                          final isShiftSelect = HardwareKeyboard.instance.isShiftPressed;

                                          _selectCell(rowIndex, colIndex, isShiftSelect: isShiftSelect);
                                          _gridFocusNode.requestFocus();
                                        },
                                        onDoubleTap: () {
                                          if (column != 'id') {
                                            _startEditing(rowIndex, colIndex);
                                          }
                                        },
                                        child: Container(
                                          width: _columnWidths[column] ?? _defaultColumnWidth,
                                          height: 40,
                                          padding: const EdgeInsets.symmetric(horizontal: 8),
                                          alignment: Alignment.centerLeft,
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                              color: isSelected
                                                  ? Colors.blue
                                                  : Colors.grey.shade300,
                                              width: isSelected ? 2 : 1,
                                            ),
                                            color: isSelected
                                                ? Colors.blue.shade50
                                                : Colors.white,
                                          ),
                                          child: isEditing
                                              ? TextField(
                                                  controller: _editingController,
                                                  decoration: const InputDecoration(
                                                    border: InputBorder.none,
                                                    contentPadding: EdgeInsets.zero,
                                                  ),
                                                  autofocus: true,
                                                  onSubmitted: (_) => _finishEditing(),
                                                  onEditingComplete: _finishEditing,
                                                )
                                              : Text(
                                                  value,
                                                  overflow: TextOverflow.ellipsis,
                                                  style: TextStyle(
                                                    color: column == 'id'
                                                        ? Colors.grey
                                                        : Colors.black,
                                                    fontStyle: column == 'id'
                                                        ? FontStyle.italic
                                                        : FontStyle.normal,
                                                  ),
                                                ),
                                        ),
                                      );
                                    }),
                                  ],
                                );
                              }),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // New row form
                  Container(
                    padding: const EdgeInsets.all(8.0),
                    color: Colors.grey.shade100,
                    child: Row(
                      children: [
                        Container(
                          width: 50,
                          alignment: Alignment.center,
                          child: IconButton(
                            icon: const Icon(Icons.add_circle, color: Colors.green),
                            onPressed: _addRow,
                            tooltip: 'Add Row',
                          ),
                        ),
                        Expanded(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: _visibleColumns
                                  .where((col) => col != 'id')
                                  .map((column) {
                                return Container(
                                  width: _columnWidths[column] ?? _defaultColumnWidth,
                                  padding: const EdgeInsets.symmetric(horizontal: 4),
                                  child: TextField(
                                    controller: _newRowControllers[column],
                                    decoration: InputDecoration(
                                      labelText: column,
                                      border: const OutlineInputBorder(),
                                      contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 8,
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}