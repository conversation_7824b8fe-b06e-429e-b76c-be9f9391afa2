^D:\FIRETOOL\FIRETOOL_ESTIMATOR\BUILD\WINDOWS\X64\CMAKEFILES\30334C838702E664B3E4E8C2633EF293\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=D:\Firetool\firetool_estimator FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=D:\Firetool\firetool_estimator\windows\flutter\ephemeral PROJECT_DIR=D:\Firetool\firetool_estimator FLUTTER_TARGET=D:\Firetool\firetool_estimator\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\Firetool\firetool_estimator\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\FIRETOOL\FIRETOOL_ESTIMATOR\BUILD\WINDOWS\X64\CMAKEFILES\BE111C11B368B2726BF66D4452F602F4\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\FIRETOOL\FIRETOOL_ESTIMATOR\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Firetool/firetool_estimator/windows -BD:/Firetool/firetool_estimator/build/windows/x64 --check-stamp-file D:/Firetool/firetool_estimator/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
