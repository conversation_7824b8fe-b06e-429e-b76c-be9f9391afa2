import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Simple user model
class User {
  final String id;
  final String username;
  final bool isAdmin;

  User({
    required this.id,
    required this.username,
    this.isAdmin = false,
  });
}

class AuthService with ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _error;

  // Getters
  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _currentUser != null;
  bool get isAdmin => _currentUser?.isAdmin ?? false;
  bool get isAuthenticated => _currentUser != null;

  // Constructor - automatically logs in as admin
  AuthService() {
    // Auto-login as admin
    _autoLogin();
  }

  // Auto login as admin
  Future<void> _autoLogin() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Create admin user
      _currentUser = User(
        id: 'admin-user',
        username: '<EMAIL>',
        isAdmin: true,
      );

      // Save login state to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isLoggedIn', true);
      await prefs.setString('userId', 'admin-user');
      await prefs.setString('username', '<EMAIL>');
      await prefs.setBool('isAdmin', true);

      _error = null;
    } catch (e) {
      _error = 'Auto-login failed: $e';
      print('Auto-login error: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  // Initialize the auth service - for compatibility
  Future<void> initialize() async {
    // Already initialized in constructor
    return;
  }

  // Login method - always succeeds and logs in as admin
  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Create admin user
      _currentUser = User(
        id: 'admin-user',
        username: email.isNotEmpty ? email : '<EMAIL>',
        isAdmin: true,
      );

      // Save login state to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isLoggedIn', true);
      await prefs.setString('userId', 'admin-user');
      await prefs.setString('username', email.isNotEmpty ? email : '<EMAIL>');
      await prefs.setBool('isAdmin', true);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Login failed: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Logout method
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      _currentUser = null;

      // Clear login state from shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isLoggedIn', false);
      await prefs.remove('userId');
      await prefs.remove('username');
      await prefs.remove('isAdmin');
    } catch (e) {
      _error = 'Logout failed: $e';
    }

    _isLoading = false;
    notifyListeners();
  }

  // Check if user is logged in from shared preferences
  Future<bool> checkLoginStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool('isLoggedIn') ?? false;

      if (isLoggedIn) {
        final userId = prefs.getString('userId') ?? 'admin-user';
        final username = prefs.getString('username') ?? '<EMAIL>';
        final isAdmin = prefs.getBool('isAdmin') ?? true;

        _currentUser = User(
          id: userId,
          username: username,
          isAdmin: isAdmin,
        );

        notifyListeners();
      }

      return isLoggedIn;
    } catch (e) {
      print('Error checking login status: $e');
      return false;
    }
  }

  // For compatibility with existing code
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
