import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';

/// A utility class for managing SQLite database schema at runtime
class SQLiteRuntimeSchema {
  /// Adds a new column to an existing table
  /// 
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to modify
  /// - [columnName]: The name of the column to add
  /// - [columnType]: The SQLite data type (TEXT, INTEGER, REAL, etc.)
  /// 
  /// Returns: true if successful, false otherwise
  static Future<bool> addColumn(
    Database db,
    String tableName,
    String columnName,
    String columnType,
  ) async {
    try {
      // Validate inputs to prevent SQL injection
      _validateIdentifier(tableName);
      _validateIdentifier(columnName);
      _validateColumnType(columnType);
      
      // Execute the ALTER TABLE statement
      await db.execute(
        'ALTER TABLE "$tableName" ADD COLUMN "$columnName" $columnType',
      );
      
      return true;
    } catch (e) {
      debugPrint('Error adding column: $e');
      return false;
    }
  }

  /// Updates a single cell value in the database
  /// 
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to update
  /// - [columnName]: The name of the column to update
  /// - [value]: The new value for the column
  /// - [where]: The WHERE clause to identify rows to update
  /// - [whereArgs]: Arguments for the WHERE clause
  /// 
  /// Returns: The number of rows updated
  static Future<int> updateCell(
    Database db,
    String tableName,
    String columnName,
    dynamic value,
    String where,
    List<dynamic> whereArgs,
  ) async {
    try {
      // Validate inputs to prevent SQL injection
      _validateIdentifier(tableName);
      _validateIdentifier(columnName);
      
      // Create a map with just the one column to update
      final Map<String, dynamic> values = {columnName: value};
      
      // Execute the update
      return await db.update(
        tableName,
        values,
        where: where,
        whereArgs: whereArgs,
      );
    } catch (e) {
      debugPrint('Error updating cell: $e');
      return 0;
    }
  }

  /// Drops a column from a table by recreating the table without that column
  /// 
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to modify
  /// - [columnToRemove]: The name of the column to remove
  /// 
  /// Returns: true if successful, false otherwise
  static Future<bool> dropColumn(
    Database db,
    String tableName,
    String columnToRemove,
  ) async {
    try {
      // Validate inputs to prevent SQL injection
      _validateIdentifier(tableName);
      _validateIdentifier(columnToRemove);
      
      // Start a transaction for atomicity
      return await db.transaction((txn) async {
        // 1. Get table info
        final List<Map<String, dynamic>> tableInfo = await txn.rawQuery(
          'PRAGMA table_info("$tableName")',
        );
        
        // 2. Check if the column exists
        final columnExists = tableInfo.any(
          (column) => column['name'] == columnToRemove,
        );
        
        if (!columnExists) {
          debugPrint('Column $columnToRemove does not exist in table $tableName');
          return false;
        }
        
        // 3. Create a list of columns excluding the one to remove
        final List<Map<String, dynamic>> columnsToKeep = tableInfo
            .where((column) => column['name'] != columnToRemove)
            .toList();
        
        if (columnsToKeep.isEmpty) {
          debugPrint('Cannot remove the only column from the table');
          return false;
        }
        
        // 4. Generate column definitions for the new table
        final List<String> columnDefs = columnsToKeep.map((column) {
          final name = column['name'];
          final type = column['type'];
          final notNull = column['notnull'] == 1 ? 'NOT NULL' : '';
          final defaultValue = column['dflt_value'] != null 
              ? "DEFAULT '${column['dflt_value']}'" 
              : '';
          final primaryKey = column['pk'] == 1 ? 'PRIMARY KEY' : '';
          
          return '"$name" $type $notNull $defaultValue $primaryKey'.trim();
        }).toList();
        
        // 5. Generate column names for data transfer
        final List<String> columnNames = columnsToKeep
            .map((column) => '"${column['name']}"')
            .toList();
        
        // 6. Create a temporary table with the new schema
        final String tempTableName = '${tableName}_temp';
        await txn.execute(
          'CREATE TABLE "$tempTableName" (${columnDefs.join(', ')})',
        );
        
        // 7. Copy data from the original table to the temporary table
        await txn.execute(
          'INSERT INTO "$tempTableName" (${columnNames.join(', ')}) '
          'SELECT ${columnNames.join(', ')} FROM "$tableName"',
        );
        
        // 8. Drop the original table
        await txn.execute('DROP TABLE "$tableName"');
        
        // 9. Rename the temporary table to the original table name
        await txn.execute(
          'ALTER TABLE "$tempTableName" RENAME TO "$tableName"',
        );
        
        return true;
      });
    } catch (e) {
      debugPrint('Error dropping column: $e');
      return false;
    }
  }

  // Helper methods for validation

  /// Validates an SQL identifier (table or column name)
  static void _validateIdentifier(String identifier) {
    if (identifier.isEmpty) {
      throw ArgumentError('Identifier cannot be empty');
    }
    
    if (!RegExp(r'^[a-zA-Z_][a-zA-Z0-9_]*$').hasMatch(identifier)) {
      throw ArgumentError('Invalid identifier: $identifier');
    }
  }

  /// Validates a column type
  static void _validateColumnType(String columnType) {
    final validTypes = [
      'TEXT', 'INTEGER', 'REAL', 'BLOB', 'NUMERIC',
      'BOOLEAN', 'DATE', 'DATETIME', 'TIME',
    ];
    
    final upperType = columnType.toUpperCase();
    
    if (!validTypes.contains(upperType) && 
        !upperType.startsWith('VARCHAR') &&
        !upperType.startsWith('CHAR')) {
      throw ArgumentError('Invalid column type: $columnType');
    }
  }
}
