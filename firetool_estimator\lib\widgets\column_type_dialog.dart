import 'package:flutter/material.dart';
import '../models/flexible_table_models.dart';

class ColumnTypeDialog extends StatefulWidget {
  final ColumnDataType initialType;
  final String columnName;
  final double? width;
  final String? format;
  final String? prefix;
  final String? suffix;
  final int? decimalPlaces;

  const ColumnTypeDialog({
    super.key,
    required this.initialType,
    required this.columnName,
    this.width,
    this.format,
    this.prefix,
    this.suffix,
    this.decimalPlaces,
  });

  @override
  State<ColumnTypeDialog> createState() => _ColumnTypeDialogState();
}

class _ColumnTypeDialogState extends State<ColumnTypeDialog> {
  late ColumnDataType _selectedType;
  final TextEditingController _widthController = TextEditingController();
  final TextEditingController _formatController = TextEditingController();
  final TextEditingController _prefixController = TextEditingController();
  final TextEditingController _suffixController = TextEditingController();
  final TextEditingController _decimalPlacesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedType = widget.initialType;
    _widthController.text = widget.width?.toString() ?? '150';
    _formatController.text = widget.format ?? '';
    _prefixController.text = widget.prefix ?? '';
    _suffixController.text = widget.suffix ?? '';
    _decimalPlacesController.text = widget.decimalPlaces?.toString() ?? '2';
  }

  @override
  void dispose() {
    _widthController.dispose();
    _formatController.dispose();
    _prefixController.dispose();
    _suffixController.dispose();
    _decimalPlacesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Change Type for "${widget.columnName}"'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Column Type:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildTypeSelector(),
            const SizedBox(height: 16),
            _buildColumnWidth(),
            const SizedBox(height: 16),
            _buildTypeSpecificOptions(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            // Validate and return result
            final width = double.tryParse(_widthController.text) ?? 150.0;
            
            final result = {
              'type': _selectedType,
              'width': width,
            };
            
            // Add type-specific options
            switch (_selectedType) {
              case ColumnDataType.currency:
                result['prefix'] = _prefixController.text;
                result['suffix'] = _suffixController.text;
                result['decimalPlaces'] = int.tryParse(_decimalPlacesController.text) ?? 2;
                break;
              case ColumnDataType.number:
              case ColumnDataType.percentage:
                result['decimalPlaces'] = int.tryParse(_decimalPlacesController.text) ?? 2;
                if (_selectedType == ColumnDataType.percentage) {
                  result['suffix'] = '%';
                }
                break;
              case ColumnDataType.date:
              case ColumnDataType.time:
              case ColumnDataType.datetime:
                result['format'] = _formatController.text;
                break;
              default:
                break;
            }
            
            Navigator.of(context).pop(result);
          },
          child: const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildTypeSelector() {
    return GridView.count(
      crossAxisCount: 3,
      shrinkWrap: true,
      childAspectRatio: 2.5,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      children: ColumnDataType.values.map((type) {
        final isSelected = type == _selectedType;
        return InkWell(
          onTap: () {
            setState(() {
              _selectedType = type;
            });
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue.withOpacity(0.2) : Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
                width: isSelected ? 2 : 1,
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  type.getIcon(),
                  color: isSelected ? Colors.blue : Colors.grey.shade700,
                  size: 16,
                ),
                const SizedBox(height: 4),
                Text(
                  type.toDisplayString(),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? Colors.blue : Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildColumnWidth() {
    return Row(
      children: [
        const Text(
          'Column Width:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: TextField(
            controller: _widthController,
            decoration: const InputDecoration(
              hintText: '150',
              suffixText: 'px',
              isDense: true,
            ),
            keyboardType: TextInputType.number,
          ),
        ),
      ],
    );
  }

  Widget _buildTypeSpecificOptions() {
    switch (_selectedType) {
      case ColumnDataType.currency:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Currency Options:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _prefixController,
              decoration: const InputDecoration(
                labelText: 'Currency Symbol',
                hintText: '\$',
                isDense: true,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _suffixController,
              decoration: const InputDecoration(
                labelText: 'Suffix (optional)',
                hintText: 'USD',
                isDense: true,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _decimalPlacesController,
              decoration: const InputDecoration(
                labelText: 'Decimal Places',
                hintText: '2',
                isDense: true,
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        );
      case ColumnDataType.number:
      case ColumnDataType.percentage:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${_selectedType == ColumnDataType.number ? 'Number' : 'Percentage'} Options:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _decimalPlacesController,
              decoration: const InputDecoration(
                labelText: 'Decimal Places',
                hintText: '2',
                isDense: true,
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        );
      case ColumnDataType.date:
      case ColumnDataType.time:
      case ColumnDataType.datetime:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${_selectedType.toDisplayString()} Format:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _formatController,
              decoration: InputDecoration(
                labelText: 'Format String',
                hintText: _getFormatHint(),
                isDense: true,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Example: ${_getFormatExample()}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  String _getFormatHint() {
    switch (_selectedType) {
      case ColumnDataType.date:
        return 'yyyy-MM-dd';
      case ColumnDataType.time:
        return 'HH:mm:ss';
      case ColumnDataType.datetime:
        return 'yyyy-MM-dd HH:mm';
      default:
        return '';
    }
  }

  String _getFormatExample() {
    switch (_selectedType) {
      case ColumnDataType.date:
        return '2023-05-15';
      case ColumnDataType.time:
        return '14:30:00';
      case ColumnDataType.datetime:
        return '2023-05-15 14:30';
      default:
        return '';
    }
  }
}
