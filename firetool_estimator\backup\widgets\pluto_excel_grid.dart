import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:excel/excel.dart' as excel_lib;

class PlutoExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;
  final Map<String, List<String>>? dropdownOptions;

  const PlutoExcelGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
    this.dropdownOptions,
  });

  @override
  State<PlutoExcelGrid> createState() => _PlutoExcelGridState();
}

class _PlutoExcelGridState extends State<PlutoExcelGrid> {
  late PlutoGridStateManager stateManager;
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  bool _isLoading = true;
  String? _error;

  // For adding new columns
  final TextEditingController _newColumnController = TextEditingController();
  bool _isAddingColumn = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _newColumnController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final items = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Determine columns
      final Set<String> columnSet = {'id'};

      // Add predefined columns if provided
      if (widget.predefinedColumns != null) {
        columnSet.addAll(widget.predefinedColumns!);
      }

      // Add any additional columns from the data
      for (var item in items) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Create PlutoColumns
      final plutoColumns = columnSet.map((column) {
        return PlutoColumn(
          title: _getDisplayName(column),
          field: column,
          type: PlutoColumnType.text(),
          // frozen: column == 'id',
          width: column == 'id' ? 120 : 150,
          backgroundColor: widget.themeColor.withAlpha(20),
          titleTextAlign: PlutoColumnTextAlign.left,
          textAlign: PlutoColumnTextAlign.left,
          enableDropToResize: true,
          enableContextMenu: true,
          enableRowDrag: false,
          enableColumnDrag: true,
          enableSorting: true,
          enableFilterMenuItem: true,
          enableHideColumnMenuItem: true,
          hide: false,
          readOnly: column == 'id',
        );
      }).toList();

      // Create PlutoRows
      final plutoRows = items.map((item) {
        final Map<String, PlutoCell> cells = {};

        for (var column in columnSet) {
          cells[column] = PlutoCell(value: item[column] ?? '');
        }

        return PlutoRow(cells: cells);
      }).toList();

      setState(() {
        columns = plutoColumns;
        rows = plutoRows;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // Helper methods
  String _getDisplayName(String column) {
    // Convert snake_case to Title Case
    return column.split('_').map((word) =>
      word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  String _getColumnKey(String displayName) {
    // Convert Title Case to snake_case
    return displayName.toLowerCase().replaceAll(' ', '_');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // CRUD operations
  Future<void> _addNewRow() async {
    try {
      // Create an empty row with default values
      final Map<String, dynamic> newItem = {};

      // Add default empty values for each column
      for (var column in columns) {
        if (column.field != 'id') {
          newItem[column.field] = '';
        }
      }

      newItem['createdAt'] = FieldValue.serverTimestamp();

      // Add to Firestore
      final docRef = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Create a new PlutoRow
      final Map<String, PlutoCell> cells = {};
      for (var column in columns) {
        if (column.field == 'id') {
          cells[column.field] = PlutoCell(value: docRef.id);
        } else {
          cells[column.field] = PlutoCell(value: '');
        }
      }

      final newRow = PlutoRow(cells: cells);

      // Add the row to the grid
      stateManager.appendRows([newRow]);

      // Select the new row and start editing the first editable cell
      stateManager.setCurrentCell(
        newRow.cells[columns.firstWhere((col) => col.field != 'id').field],
        columns.indexWhere((col) => col.field != 'id'),
      );
      // Focus on the new row
      stateManager.moveScrollByRow(PlutoMoveDirection.down, rows.length - 1);

      _showSnackBar('Row added successfully');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  Future<void> _addNewColumn() async {
    if (_newColumnController.text.isEmpty) {
      _showSnackBar('Column name cannot be empty', isError: true);
      return;
    }

    try {
      final String columnKey = _getColumnKey(_newColumnController.text);
      final String displayName = _newColumnController.text.trim();

      // Check if column already exists
      if (columns.any((col) => col.field == columnKey)) {
        _showSnackBar('Column already exists', isError: true);
        return;
      }

      // Add the column to all existing items
      final batch = FirebaseFirestore.instance.batch();

      for (var i = 0; i < rows.length; i++) {
        final id = rows[i].cells['id']!.value as String;
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(id);

        batch.update(docRef, {columnKey: ''});
      }

      await batch.commit();

      // Create a new PlutoColumn
      final newColumn = PlutoColumn(
        title: displayName,
        field: columnKey,
        type: PlutoColumnType.text(),
        enableEditingMode: true,
        width: 150,
        backgroundColor: widget.themeColor.withAlpha(20),
        titleTextAlign: PlutoColumnTextAlign.left,
        textAlign: PlutoColumnTextAlign.left,
        enableDropToResize: true,
        enableContextMenu: true,
        enableRowDrag: false,
        enableColumnDrag: true,
        enableSorting: true,
        enableFilterMenuItem: true,
        enableHideColumnMenuItem: true,
      );

      // Add the column to the grid
      stateManager.insertColumns(columns.length, [newColumn]);

      // Add the cell to each row
      for (var i = 0; i < rows.length; i++) {
        rows[i].cells[columnKey] = PlutoCell(value: '');
      }

      _newColumnController.clear();
      setState(() {
        _isAddingColumn = false;
      });

      _showSnackBar('Column added successfully');
    } catch (e) {
      _showSnackBar('Error adding column: $e', isError: true);
    }
  }

  Future<void> _deleteSelectedRows() async {
    final selectedRows = stateManager.currentSelectingRows;
    if (selectedRows.isEmpty) {
      _showSnackBar('No rows selected', isError: true);
      return;
    }

    try {
      final batch = FirebaseFirestore.instance.batch();

      for (var row in selectedRows) {
        final id = row.cells['id']!.value as String;
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(id);

        batch.delete(docRef);
      }

      await batch.commit();

      // Remove rows from the grid
      stateManager.removeRows(selectedRows);

      _showSnackBar('${selectedRows.length} rows deleted');
    } catch (e) {
      _showSnackBar('Error deleting rows: $e', isError: true);
    }
  }

  // Import/Export functions
  Future<void> _importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
      );

      if (result == null || result.files.isEmpty) return;

      final file = File(result.files.first.path!);

      // Check if it's a CSV file
      if (result.files.first.extension?.toLowerCase() == 'csv') {
        await _importFromCSV(file);
        return;
      }

      // For Excel files
      try {
        final bytes = await file.readAsBytes();
        final excel = excel_lib.Excel.decodeBytes(bytes);
        final sheet = excel.tables.keys.first;
        final table = excel.tables[sheet]!;

        if (table.rows.isEmpty) {
          _showSnackBar('Excel file is empty', isError: true);
          return;
        }

        // Get headers from first row
        final headers = <String>[];
        for (var cell in table.rows[0]) {
          if (cell?.value != null) {
            headers.add(_getColumnKey(cell!.value.toString()));
          }
        }

        if (headers.isEmpty) {
          _showSnackBar('No headers found in Excel file', isError: true);
          return;
        }

        // Process data rows
        final batch = FirebaseFirestore.instance.batch();
        final newRows = <PlutoRow>[];
        int count = 0;

        for (var i = 1; i < table.rows.length; i++) {
          final row = table.rows[i];
          final Map<String, dynamic> item = {};
          final Map<String, PlutoCell> cells = {};

          // Add ID cell
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc();
          cells['id'] = PlutoCell(value: docRef.id);

          for (var j = 0; j < row.length && j < headers.length; j++) {
            if (row[j]?.value != null) {
              final value = row[j]!.value.toString();
              item[headers[j]] = value;
              cells[headers[j]] = PlutoCell(value: value);
            }
          }

          if (item.isNotEmpty) {
            item['createdAt'] = FieldValue.serverTimestamp();
            batch.set(docRef, item);

            // Add cells for existing columns that weren't in the import
            for (var column in columns) {
              if (!cells.containsKey(column.field)) {
                cells[column.field] = PlutoCell(value: '');
              }
            }

            newRows.add(PlutoRow(cells: cells));
            count++;
          }
        }

        await batch.commit();

        // Add new rows to the grid
        stateManager.appendRows(newRows);

        _showSnackBar('Imported $count items from Excel');
      } catch (e) {
        _showSnackBar('Error processing Excel file: $e', isError: true);
        _showSnackBar('Please try using "Import from Clipboard" instead', isError: false);
      }
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
    }
  }

  Future<void> _importFromCSV(File file) async {
    try {
      final content = await file.readAsString();
      final lines = content.split('\n');

      if (lines.isEmpty) {
        _showSnackBar('CSV file is empty', isError: true);
        return;
      }

      // Get headers from first line
      final headers = lines[0].split(',').map((h) => _getColumnKey(h.trim().replaceAll('"', ''))).toList();

      if (headers.isEmpty) {
        _showSnackBar('No headers found in CSV file', isError: true);
        return;
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      final newRows = <PlutoRow>[];
      int count = 0;

      for (var i = 1; i < lines.length; i++) {
        if (lines[i].trim().isEmpty) continue;

        final values = lines[i].split(',').map((v) => v.trim().replaceAll('"', '')).toList();
        final Map<String, dynamic> item = {};
        final Map<String, PlutoCell> cells = {};

        // Add ID cell
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc();
        cells['id'] = PlutoCell(value: docRef.id);

        for (var j = 0; j < values.length && j < headers.length; j++) {
          if (values[j].isNotEmpty) {
            item[headers[j]] = values[j];
            cells[headers[j]] = PlutoCell(value: values[j]);
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          batch.set(docRef, item);

          // Add cells for existing columns that weren't in the import
          for (var column in columns) {
            if (!cells.containsKey(column.field)) {
              cells[column.field] = PlutoCell(value: '');
            }
          }

          newRows.add(PlutoRow(cells: cells));
          count++;
        }
      }

      await batch.commit();

      // Add new rows to the grid
      stateManager.appendRows(newRows);

      _showSnackBar('Imported $count items from CSV');
    } catch (e) {
      _showSnackBar('Error importing from CSV: $e', isError: true);
    }
  }

  Future<void> _importFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final text = clipboardData?.text;

      if (text == null || text.isEmpty) {
        _showSnackBar('Clipboard is empty', isError: true);
        return;
      }

      final lines = text.split('\n');
      if (lines.isEmpty) {
        _showSnackBar('No data found in clipboard', isError: true);
        return;
      }

      // Get headers from first row
      final headers = lines[0].split('\t').map((h) => _getColumnKey(h.trim())).toList();

      if (headers.isEmpty) {
        _showSnackBar('No headers found in clipboard data', isError: true);
        return;
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      final newRows = <PlutoRow>[];
      int count = 0;

      for (var i = 1; i < lines.length; i++) {
        if (lines[i].trim().isEmpty) continue;

        final values = lines[i].split('\t');
        final Map<String, dynamic> item = {};
        final Map<String, PlutoCell> cells = {};

        // Add ID cell
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc();
        cells['id'] = PlutoCell(value: docRef.id);

        for (var j = 0; j < values.length && j < headers.length; j++) {
          if (values[j].trim().isNotEmpty) {
            item[headers[j]] = values[j].trim();
            cells[headers[j]] = PlutoCell(value: values[j].trim());
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          batch.set(docRef, item);

          // Add cells for existing columns that weren't in the import
          for (var column in columns) {
            if (!cells.containsKey(column.field)) {
              cells[column.field] = PlutoCell(value: '');
            }
          }

          newRows.add(PlutoRow(cells: cells));
          count++;
        }
      }

      await batch.commit();

      // Add new rows to the grid
      stateManager.appendRows(newRows);

      _showSnackBar('Imported $count items from clipboard');
    } catch (e) {
      _showSnackBar('Error importing from clipboard: $e', isError: true);
    }
  }

  Future<void> _exportToExcel() async {
    try {
      // Create a new Excel workbook
      final xlsio.Workbook workbook = xlsio.Workbook();
      final xlsio.Worksheet sheet = workbook.worksheets[0];
      sheet.name = widget.title;

      // Add headers
      for (var i = 0; i < columns.length; i++) {
        sheet.getRangeByIndex(1, i + 1).setText(_getDisplayName(columns[i].field));

        // Apply header style
        sheet.getRangeByIndex(1, i + 1).cellStyle.bold = true;
        sheet.getRangeByIndex(1, i + 1).cellStyle.backColor = '#D9EAD3';
      }

      // Add data
      for (var i = 0; i < rows.length; i++) {
        for (var j = 0; j < columns.length; j++) {
          final value = rows[i].cells[columns[j].field]?.value?.toString() ?? '';
          sheet.getRangeByIndex(i + 2, j + 1).setText(value);
        }
      }

      // Auto-fit columns
      for (var i = 1; i <= columns.length; i++) {
        sheet.autoFitColumn(i);
      }

      // Save file
      final List<int> bytes = workbook.saveAsStream();
      workbook.dispose();

      // Get file path
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/${widget.title.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final file = File(path);
      await file.writeAsBytes(bytes);

      _showSnackBar('Exported to $path');
    } catch (e) {
      _showSnackBar('Error exporting to Excel: $e', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: widget.themeColor,
        ),
      );
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    return Column(
      children: [
        _buildToolbar(),
        Expanded(
          child: PlutoGrid(
            columns: columns,
            rows: rows,
            onLoaded: (PlutoGridOnLoadedEvent event) {
              stateManager = event.stateManager;
              stateManager.setSelectingMode(PlutoGridSelectingMode.cell);
            },
            onChanged: (PlutoGridOnChangedEvent event) {
              _updateCell(event.row, event.column, event.value);
            },
            configuration: PlutoGridConfiguration(
              style: PlutoGridStyleConfig(
                borderColor: Colors.grey.shade300,
                gridBackgroundColor: Colors.white,
                cellTextStyle: const TextStyle(fontSize: 14),
                columnTextStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                rowHeight: 45,
                columnHeight: 45,
                cellColorInEditState: widget.themeColor.withAlpha(30),
                activatedColor: widget.themeColor.withAlpha(50),
                activatedBorderColor: widget.themeColor,
                inactivatedBorderColor: Colors.grey.shade300,
              ),
              columnFilter: PlutoGridColumnFilterConfig(
                filters: const [
                  ...FilterHelper.defaultFilters,
                ],
              ),
              columnSize: const PlutoGridColumnSizeConfig(
                autoSizeMode: PlutoAutoSizeMode.scale,
                resizeMode: PlutoResizeMode.normal,
              ),
              scrollbar: const PlutoGridScrollbarConfig(
                isAlwaysShown: true,
                scrollbarThickness: 8,
                scrollbarRadius: Radius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.add),
                label: const Text('Add Row'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _addNewRow,
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.add_box),
                label: const Text('Add Column'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    _isAddingColumn = true;
                  });
                },
              ),
              if (_isAddingColumn) ...[
                const SizedBox(width: 8),
                SizedBox(
                  width: 200,
                  child: TextField(
                    controller: _newColumnController,
                    decoration: const InputDecoration(
                      hintText: 'Column Name',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onSubmitted: (_) => _addNewColumn(),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.check),
                  onPressed: _addNewColumn,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _isAddingColumn = false;
                      _newColumnController.clear();
                    });
                  },
                ),
              ],
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.delete),
                label: const Text('Delete Selected'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                onPressed: _deleteSelectedRows,
              ),
              const Spacer(),
              ElevatedButton.icon(
                icon: const Icon(Icons.upload_file),
                label: const Text('Import Excel'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _importFromExcel,
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.paste),
                label: const Text('Import from Clipboard'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _importFromClipboard,
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.download),
                label: const Text('Export Excel'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _exportToExcel,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _updateCell(PlutoRow row, PlutoColumn column, dynamic value) async {
    try {
      final id = row.cells['id']!.value as String;

      // Don't update ID column
      if (column.field == 'id') return;

      // Update in Firestore
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .update({column.field: value});
    } catch (e) {
      debugPrint('Error updating cell: $e');
      _showSnackBar('Error updating cell: $e', isError: true);
    }
  }
}
