import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../models/super_database_models.dart';

/// Central service for managing SuperDatabase sections and subsections
class SuperDatabaseService {
  static final SuperDatabaseService _instance = SuperDatabaseService._internal();
  factory SuperDatabaseService() => _instance;
  SuperDatabaseService._internal();

  final Map<String, Database> _sectionDatabases = {};
  Database? _metadataDatabase;
  final _uuid = const Uuid();

  /// Initialize the SuperDatabase system
  Future<void> initialize() async {
    await _initializeMetadataDatabase();
  }

  /// Initialize the metadata database that stores section and subsection information
  Future<void> _initializeMetadataDatabase() async {
    final dbPath = await _getMetadataDbPath();

    _metadataDatabase = await openDatabase(
      dbPath,
      version: 1,
      onCreate: (db, version) async {
        // Create sections table
        await db.execute('''
          CREATE TABLE sections (
            id TEXT PRIMARY KEY,
            name TEXT UNIQUE NOT NULL,
            display_name TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            description TEXT,
            icon_name TEXT,
            color TEXT
          )
        ''');

        // Create subsections table
        await db.execute('''
          CREATE TABLE subsections (
            id TEXT PRIMARY KEY,
            section_id TEXT NOT NULL,
            name TEXT NOT NULL,
            display_name TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            description TEXT,
            columns_json TEXT NOT NULL,
            row_count INTEGER DEFAULT 0,
            FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE CASCADE,
            UNIQUE(section_id, name)
          )
        ''');

        // Create import/export operations table
        await db.execute('''
          CREATE TABLE import_export_operations (
            id TEXT PRIMARY KEY,
            type TEXT NOT NULL,
            section_id TEXT NOT NULL,
            subsection_id TEXT,
            file_path TEXT NOT NULL,
            start_time TEXT NOT NULL,
            end_time TEXT,
            status TEXT NOT NULL,
            total_rows INTEGER,
            processed_rows INTEGER,
            error_message TEXT,
            metadata_json TEXT,
            FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE CASCADE
          )
        ''');

        if (kDebugMode) {
          print('SuperDatabase metadata database created');
        }
      },
    );
  }

  Future<String> _getMetadataDbPath() async {
    final appDocDir = await getApplicationDocumentsDirectory();
    final dbDir = Directory(p.join(appDocDir.path, 'super_database'));
    if (!await dbDir.exists()) {
      await dbDir.create(recursive: true);
    }
    return p.join(dbDir.path, 'metadata.db');
  }

  Future<String> _getSectionDbPath(String sectionName) async {
    final appDocDir = await getApplicationDocumentsDirectory();
    final dbDir = Directory(p.join(appDocDir.path, 'super_database', 'sections'));
    if (!await dbDir.exists()) {
      await dbDir.create(recursive: true);
    }
    final sanitizedName = _sanitizeFileName(sectionName);
    return p.join(dbDir.path, '$sanitizedName.db');
  }

  String _sanitizeFileName(String input) {
    return input
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  /// Get all sections
  Future<List<Section>> getSections() async {
    if (_metadataDatabase == null) await initialize();

    final result = await _metadataDatabase!.query('sections', orderBy: 'display_name ASC');
    return result.map((row) => Section.fromJson({
      'id': row['id'],
      'name': row['name'],
      'displayName': row['display_name'],
      'createdAt': row['created_at'],
      'updatedAt': row['updated_at'],
      'description': row['description'],
      'iconName': row['icon_name'],
      'color': row['color'],
    })).toList();
  }

  /// Create a new section
  Future<Section> createSection({
    required String name,
    required String displayName,
    String? description,
    String? iconName,
    String? color,
  }) async {
    if (_metadataDatabase == null) await initialize();

    final now = DateTime.now();
    final section = Section(
      id: _uuid.v4(),
      name: _sanitizeFileName(name),
      displayName: displayName,
      createdAt: now,
      updatedAt: now,
      description: description,
      iconName: iconName,
      color: color,
    );

    await _metadataDatabase!.insert('sections', {
      'id': section.id,
      'name': section.name,
      'display_name': section.displayName,
      'created_at': section.createdAt.toIso8601String(),
      'updated_at': section.updatedAt.toIso8601String(),
      'description': section.description,
      'icon_name': section.iconName,
      'color': section.color,
    });

    // Create the section database
    await _createSectionDatabase(section.name);

    if (kDebugMode) {
      print('Created section: ${section.displayName}');
    }

    return section;
  }

  /// Create the database file for a section
  Future<Database> _createSectionDatabase(String sectionName) async {
    final dbPath = await _getSectionDbPath(sectionName);

    final db = await openDatabase(
      dbPath,
      version: 1,
      onCreate: (db, version) async {
        if (kDebugMode) {
          print('Created section database for: $sectionName');
        }
      },
    );

    _sectionDatabases[sectionName] = db;
    return db;
  }

  /// Get database for a section
  Future<Database> getSectionDatabase(String sectionName) async {
    if (_sectionDatabases.containsKey(sectionName)) {
      return _sectionDatabases[sectionName]!;
    }

    final dbPath = await _getSectionDbPath(sectionName);
    final db = await openDatabase(dbPath);
    _sectionDatabases[sectionName] = db;
    return db;
  }

  /// Get subsections for a section
  Future<List<Subsection>> getSubsections(String sectionId) async {
    if (_metadataDatabase == null) await initialize();

    final result = await _metadataDatabase!.query(
      'subsections',
      where: 'section_id = ?',
      whereArgs: [sectionId],
      orderBy: 'display_name ASC',
    );

    return result.map((row) {
      final columnsJson = row['columns_json'] as String;
      List<ColumnDefinition> columns = [];

      if (columnsJson.isNotEmpty) {
        try {
          final decoded = jsonDecode(columnsJson);
          if (decoded is Map && decoded.containsKey('columns')) {
            final columnsList = decoded['columns'] as List;
            columns = columnsList.map((c) => ColumnDefinition.fromJson(Map<String, dynamic>.from(c))).toList();
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing columns JSON: $e');
            print('Columns JSON: $columnsJson');
          }
        }
      }

      return Subsection(
        id: row['id'] as String,
        sectionId: row['section_id'] as String,
        name: row['name'] as String,
        displayName: row['display_name'] as String,
        createdAt: DateTime.parse(row['created_at'] as String),
        updatedAt: DateTime.parse(row['updated_at'] as String),
        description: row['description'] as String?,
        columns: columns,
        rowCount: row['row_count'] as int? ?? 0,
      );
    }).toList();
  }

  /// Create a new subsection (table)
  Future<Subsection> createSubsection({
    required String sectionId,
    required String name,
    required String displayName,
    required List<ColumnDefinition> columns,
    String? description,
  }) async {
    if (_metadataDatabase == null) await initialize();

    if (kDebugMode) {
      print('Creating subsection: $displayName with ${columns.length} columns');
      for (final col in columns) {
        print('  Column: ${col.name} (${col.displayName}) - ${col.type}');
      }
    }

    final now = DateTime.now();
    final subsection = Subsection(
      id: _uuid.v4(),
      sectionId: sectionId,
      name: _sanitizeFileName(name),
      displayName: displayName,
      createdAt: now,
      updatedAt: now,
      description: description,
      columns: columns,
    );

    try {
      // Store subsection metadata
      final columnsJson = jsonEncode({"columns": columns.map((c) => c.toJson()).toList()});

      if (kDebugMode) {
        print('Columns JSON: $columnsJson');
      }

      await _metadataDatabase!.insert('subsections', {
        'id': subsection.id,
        'section_id': subsection.sectionId,
        'name': subsection.name,
        'display_name': subsection.displayName,
        'created_at': subsection.createdAt.toIso8601String(),
        'updated_at': subsection.updatedAt.toIso8601String(),
        'description': subsection.description,
        'columns_json': columnsJson,
        'row_count': 0,
      });

      // Create the actual table in the section database
      final section = await getSection(sectionId);
      if (section != null) {
        await _createTableInSectionDatabase(section.name, subsection.name, columns);
      }

      if (kDebugMode) {
        print('Created subsection: ${subsection.displayName}');
      }

      return subsection;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating subsection: $e');
      }
      rethrow;
    }
  }

  /// Get a section by ID
  Future<Section?> getSection(String sectionId) async {
    if (_metadataDatabase == null) await initialize();

    final result = await _metadataDatabase!.query(
      'sections',
      where: 'id = ?',
      whereArgs: [sectionId],
    );

    if (result.isEmpty) return null;

    final row = result.first;
    return Section.fromJson({
      'id': row['id'],
      'name': row['name'],
      'displayName': row['display_name'],
      'createdAt': row['created_at'],
      'updatedAt': row['updated_at'],
      'description': row['description'],
      'iconName': row['icon_name'],
      'color': row['color'],
    });
  }

  /// Create a table in the section database
  Future<void> _createTableInSectionDatabase(
    String sectionName,
    String tableName,
    List<ColumnDefinition> columns,
  ) async {
    final db = await getSectionDatabase(sectionName);

    final columnDefs = columns.map((col) =>
      '"${col.name}" ${col.type.sqliteType}${col.isRequired ? ' NOT NULL' : ''}'
    ).join(', ');

    final sql = '''
      CREATE TABLE IF NOT EXISTS "$tableName" (
        _id INTEGER PRIMARY KEY AUTOINCREMENT,
        $columnDefs
      )
    ''';

    if (kDebugMode) {
      print('Creating table with SQL: $sql');
    }

    try {
      await db.execute(sql);

      if (kDebugMode) {
        print('Created table "$tableName" in section "$sectionName"');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating table "$tableName": $e');
      }
      rethrow;
    }
  }

  /// Get rows from a subsection table
  Future<List<Map<String, dynamic>>> getRows({
    required String sectionName,
    required String tableName,
    int? limit,
    int? offset,
    String? sortByColumn,
    bool sortAscending = true,
    String? filterColumn,
    String? filterValue,
  }) async {
    final db = await getSectionDatabase(sectionName);

    StringBuffer query = StringBuffer('SELECT * FROM "$tableName"');
    List<dynamic> queryArgs = [];

    if (filterColumn != null && filterValue != null && filterValue.isNotEmpty) {
      query.write(' WHERE "$filterColumn" LIKE ?');
      queryArgs.add('%$filterValue%');
    }

    if (sortByColumn != null) {
      query.write(' ORDER BY "$sortByColumn" ${sortAscending ? "ASC" : "DESC"}');
    }

    if (limit != null) {
      query.write(' LIMIT ?');
      queryArgs.add(limit);
    }

    if (offset != null) {
      query.write(' OFFSET ?');
      queryArgs.add(offset);
    }

    try {
      final result = await db.rawQuery(query.toString(), queryArgs);
      return result;
    } catch (e) {
      if (kDebugMode) {
        print("Error getting rows from '$tableName': $e");
      }
      return [];
    }
  }

  /// Insert a row into a subsection table
  Future<int> insertRow({
    required String sectionName,
    required String tableName,
    required Map<String, dynamic> data,
  }) async {
    final db = await getSectionDatabase(sectionName);

    // Remove _id if present (auto-increment)
    final sanitizedData = Map<String, dynamic>.from(data);
    sanitizedData.remove('_id');

    try {
      final id = await db.insert(tableName, sanitizedData);
      if (kDebugMode) {
        print("Row inserted with ID: $id into '$tableName'");
      }
      return id;
    } catch (e) {
      if (kDebugMode) {
        print("Error inserting row into '$tableName': $e");
      }
      rethrow;
    }
  }

  /// Update a row in a subsection table
  Future<int> updateRow({
    required String sectionName,
    required String tableName,
    required int id,
    required Map<String, dynamic> data,
  }) async {
    final db = await getSectionDatabase(sectionName);

    // Remove _id from data to prevent updating it
    final sanitizedData = Map<String, dynamic>.from(data);
    sanitizedData.remove('_id');

    try {
      final count = await db.update(
        tableName,
        sanitizedData,
        where: '_id = ?',
        whereArgs: [id],
      );
      if (kDebugMode) {
        print("Updated $count row(s) in '$tableName'");
      }
      return count;
    } catch (e) {
      if (kDebugMode) {
        print("Error updating row in '$tableName': $e");
      }
      rethrow;
    }
  }

  /// Delete a row from a subsection table
  Future<int> deleteRow({
    required String sectionName,
    required String tableName,
    required int id,
  }) async {
    final db = await getSectionDatabase(sectionName);

    try {
      final count = await db.delete(
        tableName,
        where: '_id = ?',
        whereArgs: [id],
      );
      if (kDebugMode) {
        print("Deleted $count row(s) from '$tableName'");
      }
      return count;
    } catch (e) {
      if (kDebugMode) {
        print("Error deleting row from '$tableName': $e");
      }
      rethrow;
    }
  }

  /// Update subsection columns (dynamic schema changes)
  Future<void> updateSubsectionColumns({
    required String sectionName,
    required String subsectionId,
    required List<ColumnDefinition> newColumns,
  }) async {
    if (_metadataDatabase == null) await initialize();

    try {
      // Get current subsection info
      final subsections = await getSubsections(subsectionId);
      final subsection = subsections.firstOrNull;
      if (subsection == null) {
        throw Exception('Subsection not found');
      }

      // Get section database
      final db = await getSectionDatabase(sectionName);

      // Create new table with updated schema
      final tempTableName = '${subsection.name}_temp';
      await _createTableInSectionDatabase(sectionName, tempTableName, newColumns);

      // Copy existing data to new table (matching columns only)
      final existingColumns = subsection.columns;
      final commonColumns = <String>[];

      for (final newCol in newColumns) {
        for (final existingCol in existingColumns) {
          if (newCol.name == existingCol.name) {
            commonColumns.add(newCol.name);
            break;
          }
        }
      }

      if (commonColumns.isNotEmpty) {
        final columnList = commonColumns.map((col) => '"$col"').join(', ');
        await db.execute('''
          INSERT INTO "$tempTableName" ($columnList)
          SELECT $columnList FROM "${subsection.name}"
        ''');
      }

      // Drop old table and rename new table
      await db.execute('DROP TABLE IF EXISTS "${subsection.name}"');
      await db.execute('ALTER TABLE "$tempTableName" RENAME TO "${subsection.name}"');

      // Update metadata
      final columnsJson = jsonEncode({"columns": newColumns.map((c) => c.toJson()).toList()});
      await _metadataDatabase!.update(
        'subsections',
        {
          'columns_json': columnsJson,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [subsectionId],
      );

      if (kDebugMode) {
        print('Updated columns for subsection: ${subsection.displayName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating subsection columns: $e');
      }
      rethrow;
    }
  }

  /// Delete a section and all its data
  Future<void> deleteSection(String sectionId) async {
    if (_metadataDatabase == null) await initialize();

    try {
      // Get section info
      final section = await getSection(sectionId);
      if (section == null) {
        throw Exception('Section not found');
      }

      // Close and delete section database
      if (_sectionDatabases.containsKey(section.name)) {
        await _sectionDatabases[section.name]!.close();
        _sectionDatabases.remove(section.name);
      }

      // Delete section database file
      final dbPath = await _getSectionDbPath(section.name);
      final dbFile = File(dbPath);
      if (await dbFile.exists()) {
        await dbFile.delete();
      }

      // Delete subsections from metadata
      await _metadataDatabase!.delete(
        'subsections',
        where: 'section_id = ?',
        whereArgs: [sectionId],
      );

      // Delete section from metadata
      await _metadataDatabase!.delete(
        'sections',
        where: 'id = ?',
        whereArgs: [sectionId],
      );

      if (kDebugMode) {
        print('Deleted section: ${section.displayName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting section: $e');
      }
      rethrow;
    }
  }

  /// Delete a subsection and its data
  Future<void> deleteSubsection(String sectionId, String subsectionId) async {
    if (_metadataDatabase == null) await initialize();

    try {
      // Get section and subsection info
      final section = await getSection(sectionId);
      if (section == null) {
        throw Exception('Section not found');
      }

      final subsections = await getSubsections(sectionId);
      final subsection = subsections.where((s) => s.id == subsectionId).firstOrNull;
      if (subsection == null) {
        throw Exception('Subsection not found');
      }

      // Drop table from section database
      final db = await getSectionDatabase(section.name);
      await db.execute('DROP TABLE IF EXISTS "${subsection.name}"');

      // Delete subsection from metadata
      await _metadataDatabase!.delete(
        'subsections',
        where: 'id = ?',
        whereArgs: [subsectionId],
      );

      if (kDebugMode) {
        print('Deleted subsection: ${subsection.displayName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting subsection: $e');
      }
      rethrow;
    }
  }

  /// Close all databases
  Future<void> dispose() async {
    for (final db in _sectionDatabases.values) {
      await db.close();
    }
    _sectionDatabases.clear();

    if (_metadataDatabase != null) {
      await _metadataDatabase!.close();
      _metadataDatabase = null;
    }
  }
}
