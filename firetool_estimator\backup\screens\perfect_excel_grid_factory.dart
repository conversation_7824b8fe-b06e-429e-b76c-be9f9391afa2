import 'package:flutter/material.dart';
import '../widgets/perfect_excel_grid.dart';

class PerfectExcelGridFactory {
  static Widget createScreen(String systemType) {
    switch (systemType) {
      case 'alarm':
        return PerfectExcelGrid(
          collectionPath: 'fire_alarm',
          title: 'Fire Alarm Systems',
          themeColor: Colors.red.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'water':
        return PerfectExcelGrid(
          collectionPath: 'water_systems',
          title: 'Water Systems',
          themeColor: Colors.blue.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'foam':
        return PerfectExcelGrid(
          collectionPath: 'foam_systems',
          title: 'Foam Systems',
          themeColor: Colors.amber.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'fm200':
        return PerfectExcelGrid(
          collectionPath: 'clean_agent/fm200',
          title: 'FM200 Systems',
          themeColor: Colors.green.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'novec':
        return PerfectExcelGrid(
          collectionPath: 'clean_agent/novec',
          title: 'Novec Systems',
          themeColor: Colors.teal.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'co2':
        return PerfectExcelGrid(
          collectionPath: 'co2_systems',
          title: 'CO2 Systems',
          themeColor: Colors.purple.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      default:
        return PerfectExcelGrid(
          collectionPath: 'materials',
          title: 'Materials',
          themeColor: Colors.brown.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
    }
  }
}
