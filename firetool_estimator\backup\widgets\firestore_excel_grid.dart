import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Required for Clipboard
import 'package:cloud_firestore/cloud_firestore.dart';
// Use FilePicker for non-web platforms, dart:html for web
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart' hide Border, BorderStyle; // Hide conflicting Border classes
import 'dart:io' if (dart.library.html) 'dart:html' as io; // Conditional import for File and html

import 'package:pluto_grid/pluto_grid.dart';

void main() {
  // Ensure Flutter binding is initialized before using Firebase
  WidgetsFlutterBinding.ensureInitialized();
  // Initialize Firebase (assuming Firebase is already set up in your project)
  // await Firebase.initializeApp(); // Uncomment and configure if not already done
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Firestore Excel Grid',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const DataGridPage(),
    );
  }
}

class DataGridPage extends StatefulWidget {
  // Example usage:
  // const DataGridPage({
  //   super.key,
  //   collectionPath: 'your_collection_name',
  //   title: 'Your Grid Title',
  //   themeColor: Colors.blue,
  //   predefinedColumns: const ['column1', 'column2'], // Optional
  // });
  // Note: For this example, hardcoding values for simplicity.
  // You would pass these in from your navigation or parent widget.

  const DataGridPage({
    super.key,
    this.collectionPath = 'items', // Default collection path
    this.title = 'Firestore Data Grid', // Default title
    this.themeColor = Colors.blue, // Default theme color
    this.predefinedColumns, // Optional predefined columns
  });

  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  @override
  State<DataGridPage> createState() => _DataGridPageState();
}

class _DataGridPageState extends State<DataGridPage> {
  // PlutoGrid state manager
  PlutoGridStateManager? _stateManager;

  // Data state
  List<PlutoColumn> _columns = [];
  List<PlutoRow> _rows = [];
  bool _isLoading = true;
  String? _error;

  // Store original Firestore document IDs mapped to PlutoRow index
  final Map<PlutoRow, String> _rowToDocId = {};
  // Store original Firestore document data mapped to PlutoRow index
  // This helps track changes and merge with Firestore data
  final Map<PlutoRow, Map<String, dynamic>> _originalRowData = {};


  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _stateManager?.dispose(); // Dispose the state manager
    super.dispose();
  }

  // Data loading from Firestore
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _columns.clear();
      _rows.clear();
      _rowToDocId.clear();
      _originalRowData.clear();
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      // Determine columns: Start with predefined, then add from data
      final Set<String> columnSet = {};
      if (widget.predefinedColumns != null) {
        columnSet.addAll(widget.predefinedColumns!);
      }

      final List<Map<String, dynamic>> loadedItems = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        // Add 'id' as a column if not predefined
        if (!columnSet.contains('id')) {
           columnSet.add('id');
        }
        // Add all keys from the document data as potential columns
        columnSet.addAll(data.keys);
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Remove internal Firestore fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Convert column set to a sorted list for consistent order
      _columns = columnSet.toList().map((colName) {
        // Define PlutoColumns based on discovered columns
        return PlutoColumn(
          title: _getDisplayName(colName), // Use display name for header
          field: colName, // Use original field name for data access
          type: PlutoColumnType.text(), // Default to text type
          enableColumnDrag: true,
          enableEditingMode: colName != 'id', // Prevent editing the ID column
          // Add more type handling if needed based on data
        );
      }).toList();

      // Convert Firestore documents to PlutoRows
      _rows = loadedItems.map((item) {
        final cells = <String, PlutoCell>{};
        for (var col in _columns) {
          // Populate cells with data from the item, defaulting to empty string
          cells[col.field] = PlutoCell(value: item[col.field]?.toString() ?? '');
        }
        final row = PlutoRow(cells: cells);
        // Store the original document ID and data for saving
        _rowToDocId[row] = item['id'] as String;
        _originalRowData[row] = Map.from(item); // Store a copy
        return row;
      }).toList();

      setState(() {
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
      print('Error loading data: $e'); // Log the error
    }
  }

  // Callback when PlutoGrid is loaded
  void _onLoaded(PlutoGridOnLoadedEvent event) {
    _stateManager = event.stateManager;
    // Enable Excel-style cell-range selecting & clipboard
    _stateManager!.setSelectingMode(PlutoGridSelectingMode.cell);

    // Listen for cell value changes to save to Firestore
    _stateManager!.addListener(() {
      if (_stateManager!.currentCell != null && _stateManager!.currentCell!.isEditingMode) {
        // When a cell finishes editing, save the change
        final cell = _stateManager!.currentCell!;
        final row = cell.row;
        final column = cell.column;
        final newValue = cell.value.toString();

        // Find the original row index and document ID
        final docId = _rowToDocId[row];

        if (docId != null && column.field != 'id') { // Don't update the ID field
           // Check if the value actually changed before saving
           final originalValue = _originalRowData[row]?[column.field]?.toString() ?? '';
           if (newValue != originalValue) {
             _updateCellValue(docId, column.field, newValue);
           }
        }
      }
    });
  }

  // Update a cell value in Firestore
  Future<void> _updateCellValue(String docId, String field, dynamic newValue) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(docId)
          .update({field: newValue});

      // Update the original data map after successful save
      final row = _rowToDocId.entries.firstWhere((entry) => entry.value == docId).key;
      _originalRowData[row]?[field] = newValue;

      _showSnackBar('Cell updated');
    } catch (e) {
      _showSnackBar('Error updating cell: $e', isError: true);
      print('Error updating cell $docId/$field: $e'); // Log the error
      // Optionally, revert the cell value in the grid if the update fails
      // _stateManager?.changeCellValue(cell, originalValue, force: true);
    }
  }

  // Add a new row
  Future<void> _addNewRow() async {
    // Create an empty map for the new item
    final Map<String, dynamic> newItemData = {};
    // Add default empty values for all existing columns (except 'id')
    for (var col in _columns) {
      if (col.field != 'id') {
        newItemData[col.field] = '';
      }
    }

    // Add server timestamp
    newItemData['createdAt'] = FieldValue.serverTimestamp();

    try {
      // Add the new document to Firestore
      final docRef = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItemData);

      // Create a new PlutoRow for the added document
      final cells = <String, PlutoCell>{};
      // Populate cells with data from the new document (including the generated ID)
      for (var col in _columns) {
         if (col.field == 'id') {
            cells[col.field] = PlutoCell(value: docRef.id);
         } else {
            cells[col.field] = PlutoCell(value: newItemData[col.field]?.toString() ?? '');
         }
      }
      final newRow = PlutoRow(cells: cells);

      // Add the new row to the grid's state manager
      _stateManager?.insertRows(_stateManager!.rows.length, [newRow]);

      // Store the document ID and original data for the new row
      _rowToDocId[newRow] = docRef.id;
      _originalRowData[newRow] = Map.from(newItemData)..['id'] = docRef.id; // Include ID in original data map

      _showSnackBar('New row added');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
      print('Error adding row: $e'); // Log the error
    }
  }

  // Delete selected rows
  Future<void> _deleteSelectedRows() async {
    if (_stateManager == null || _stateManager!.currentSelectingRows.isEmpty) {
      _showSnackBar('No rows selected', isError: true);
      return;
    }

    // Get the list of selected rows
    final selectedRows = List<PlutoRow>.from(_stateManager!.currentSelectingRows);

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Rows'),
        content: Text('Are you sure you want to delete ${selectedRows.length} selected row(s)?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    ) ?? false;

    if (!confirmed || !mounted) return;

    // Prepare batch delete for Firestore
    final batch = FirebaseFirestore.instance.batch();
    final List<String> docIdsToDelete = [];

    // Collect document IDs of selected rows
    for (var row in selectedRows) {
      final docId = _rowToDocId[row];
      if (docId != null) {
        docIdsToDelete.add(docId);
        // Add delete operation to batch
        batch.delete(FirebaseFirestore.instance.collection(widget.collectionPath).doc(docId));
      }
    }

    try {
      // Commit the batch delete to Firestore
      await batch.commit();

      // Remove rows from the grid's state manager
      _stateManager?.removeRows(selectedRows);

      // Remove deleted rows from our local maps
      for (var row in selectedRows) {
         _rowToDocId.remove(row);
         _originalRowData.remove(row);
      }

      _showSnackBar('${selectedRows.length} row(s) deleted');
    } catch (e) {
      _showSnackBar('Error deleting rows: $e', isError: true);
      print('Error deleting rows: $e'); // Log the error
    }
  }

  // Add a new column
  void _showAddColumnDialog() {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Column'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Column Name (field_name)',
            hintText: 'Enter column name (e.g., new_field)',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final columnName = controller.text.trim();
              if (columnName.isNotEmpty) {
                _addColumn(columnName);
                Navigator.of(context).pop();
              }
            },
            style: TextButton.styleFrom(foregroundColor: widget.themeColor),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _addColumn(String columnName) async {
    // Ensure column name is a valid Firestore field name (no spaces, special chars)
    final validColumnName = columnName.replaceAll(RegExp(r'[^\w_]'), '_').toLowerCase();

    if (validColumnName.isEmpty || validColumnName == 'id') {
      _showSnackBar('Invalid column name', isError: true);
      return;
    }

    // Check if column already exists
    if (_columns.any((col) => col.field == validColumnName)) {
      _showSnackBar('Column "$columnName" already exists', isError: true);
      return;
    }

    // Add the new column to PlutoGrid
    final newColumn = PlutoColumn(
      title: _getDisplayName(validColumnName),
      field: validColumnName,
      type: PlutoColumnType.text(),
      enableColumnDrag: true,
      enableEditingMode: true,
    );

    _stateManager?.insertColumns(_columns.length, [newColumn]);

    // Update local column list
    setState(() {
      _columns.add(newColumn);
    });

    // Note: Adding a column locally in PlutoGrid doesn't automatically add the field
    // to existing Firestore documents. The field will be added to a document
    // the first time a cell in that column for that row is edited and saved.
    // If you need to add the field with a default value to all existing documents,
    // you would need to perform a batch update here.

    _showSnackBar('Column "$columnName" added');
  }

  // Delete a column
  Future<void> _deleteColumn(String columnField) async {
    // Don't allow deleting the ID column
    if (columnField == 'id') {
      _showSnackBar('Cannot delete ID column', isError: true);
      return;
    }

    // Find the column index
    final colIndex = _columns.indexWhere((col) => col.field == columnField);
    if (colIndex == -1) return; // Column not found

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Column'),
        content: Text('Are you sure you want to delete the "${_getDisplayName(columnField)}" column? This will remove this data from all items in Firestore.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    ) ?? false;

    if (!confirmed || !mounted) return;

    try {
      // Prepare batch update to remove the field from all documents
      final batch = FirebaseFirestore.instance.batch();
      for (var row in _stateManager!.refRows) {
        final docId = _rowToDocId[row];
        if (docId != null) {
          batch.update(FirebaseFirestore.instance.collection(widget.collectionPath).doc(docId), {
            columnField: FieldValue.delete(), // Remove the field
          });
        }
      }
      await batch.commit();

      // Remove the column from PlutoGrid's state manager
      _stateManager?.removeColumns([_columns[colIndex]]);

      // Update local column list
      setState(() {
        _columns.removeAt(colIndex);
      });

      // Update local original data maps
      for (var rowData in _originalRowData.values) {
         rowData.remove(columnField);
      }


      _showSnackBar('Column "${_getDisplayName(columnField)}" deleted');
    } catch (e) {
      _showSnackBar('Error deleting column: $e', isError: true);
      print('Error deleting column $columnField: $e'); // Log the error
    }
  }


  // Import from Excel (Web and Non-Web)
  Future<void> _importFromExcel() async {
    try {
      // Use FilePicker for picking the file
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result == null || result.files.isEmpty) return;

      Uint8List? bytes;
      String? fileName = result.files.first.name;

      // Read file bytes based on platform
      if (io.Platform.isWindows || io.Platform.isMacOS || io.Platform.isLinux) {
        // Desktop platforms
        if (result.files.first.path != null) {
          bytes = await io.File(result.files.first.path!).readAsBytes();
        }
      } else if (io.dart.library.html) {
        // Web platform
        bytes = result.files.first.bytes;
      } else {
        // Other platforms (e.g., mobile) - FilePicker should handle this
        if (result.files.first.path != null) {
           bytes = await io.File(result.files.first.path!).readAsBytes();
        }
      }

      if (bytes == null) {
        _showSnackBar('Could not read file bytes', isError: true);
        return;
      }

      final excel = Excel.decodeBytes(bytes);

      if (excel.tables.isEmpty) {
        _showSnackBar('No data found in Excel file', isError: true);
        return;
      }

      // Use the first sheet
      final sheet = excel.tables.entries.first.value;

      // Get headers from first row
      final headers = <String>[];
      final headerRow = sheet.rows.first;

      for (var cell in headerRow) {
        if (cell?.value != null) {
          headers.add(cell!.value.toString().trim());
        }
      }

      if (headers.isEmpty) {
        _showSnackBar('No headers found in Excel file', isError: true);
        return;
      }

      // Option to clear existing data
      final clearExisting = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Import Options'),
          content: const Text('Do you want to clear existing data before importing?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('No, Add to Existing'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Yes, Clear Existing'),
            ),
          ],
        ),
      ) ?? false;

      if (!mounted) return;

      // Clear existing data in Firestore and local state if requested
      if (clearExisting) {
        setState(() {
           _isLoading = true; // Show loading indicator during deletion
        });
        final batch = FirebaseFirestore.instance.batch();
        for (var row in _stateManager!.refRows) {
          final docId = _rowToDocId[row];
          if (docId != null) {
            batch.delete(FirebaseFirestore.instance.collection(widget.collectionPath).doc(docId));
          }
        }
        await batch.commit();

        // Clear local data and state manager
        _stateManager?.removeRows(_stateManager!.refRows.toList());
        _rowToDocId.clear();
        _originalRowData.clear();
        _columns.clear(); // Clear columns as they will be redefined by import
      }

      // Process data rows and prepare for batch write
      final batch = FirebaseFirestore.instance.batch();
      final List<PlutoRow> newPlutoRows = [];
      final List<Map<String, dynamic>> newOriginalData = [];
      final Set<String> importedColumns = {'id'}; // Start with 'id'
      importedColumns.addAll(headers); // Add columns from Excel headers

      int count = 0;

      // Iterate over data rows (skip header row)
      for (var i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final Map<String, dynamic> itemData = {};
        final Map<String, PlutoCell> cells = {};

        // Populate item data and cells based on headers
        for (var j = 0; j < headers.length && j < row.length; j++) {
          final header = headers[j];
          final cellValue = row[j]?.value?.toString() ?? '';
          itemData[header] = cellValue;
          cells[header] = PlutoCell(value: cellValue);
        }

        // Add 'id' cell if it's a column
        if (importedColumns.contains('id') && !cells.containsKey('id')) {
           // For new rows from import, Firestore will generate the ID
           // We'll set a temporary ID or handle it after batch commit
           // For now, just ensure the cell exists if 'id' is a header
            cells['id'] = PlutoCell(value: ''); // Placeholder
        }


        if (itemData.isNotEmpty) {
          itemData['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(); // Let Firestore generate a new ID

          batch.set(docRef, itemData);

          // Create a new PlutoRow with the generated ID
          final newRow = PlutoRow(cells: cells);
          newRow.cells['id']!.value = docRef.id; // Update the ID cell

          newPlutoRows.add(newRow);
          _rowToDocId[newRow] = docRef.id; // Store the generated ID
          _originalRowData[newRow] = Map.from(itemData)..['id'] = docRef.id; // Store original data

          count++;
        }
      }

      // Commit the batch write to Firestore
      await batch.commit();

      if (!mounted) return;

      // Update columns in state based on imported headers and existing columns
      final Set<String> finalColumnSet = {};
      finalColumnSet.addAll(importedColumns);
      // Add any columns from existing data that weren't in the import headers
      for (var existingRow in _stateManager!.refRows) {
         for (var col in existingRow.cells.keys) {
            finalColumnSet.add(col);
         }
      }
       finalColumnSet.remove('createdAt');
       finalColumnSet.remove('updatedAt');


      // Rebuild columns for PlutoGrid
      final List<PlutoColumn> finalColumns = finalColumnSet.toList().map((colName) {
         return PlutoColumn(
            title: _getDisplayName(colName),
            field: colName,
            type: PlutoColumnType.text(),
            enableColumnDrag: true,
            enableEditingMode: colName != 'id',
         );
      }).toList();

      // Update state with new columns and rows
      setState(() {
        _columns = finalColumns;
        // Add new rows to the state manager
        _stateManager?.insertRows(_stateManager!.rows.length, newPlutoRows);
      });


      _showSnackBar('Imported $count items from Excel');
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
      print('Error importing from Excel: $e'); // Log the error
    } finally {
       // Ensure loading indicator is hidden
       if (mounted) {
          setState(() {
             _isLoading = false;
          });
       }
    }
  }


  // Export to Excel (Web and Non-Web)
  Future<void> _exportToExcel() async {
    if (_stateManager == null) return;

    try {
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1']; // Get the default sheet

      final currentRows = _stateManager!.refRows;
      final currentColumns = _stateManager!.refColumns;

      // Add headers (using display titles)
      for (var i = 0; i < currentColumns.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value =
            TextCellValue(currentColumns[i].title);
      }

      // Add data
      for (var i = 0; i < currentRows.length; i++) {
        final row = currentRows[i];

        for (var j = 0; j < currentColumns.length; j++) {
          final column = currentColumns[j];
          final cellValue = row.cells[column.field]?.value?.toString() ?? '';
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1)).value =
              TextCellValue(cellValue);
        }
      }

      // Save file based on platform
      final fileName = '${widget.title.replaceAll(' ', '_')}_export.xlsx';
      final bytes = excel.encode(); // Get file bytes

      if (bytes == null) {
         _showSnackBar('Error encoding Excel file', isError: true);
         return;
      }

      if (io.dart.library.html) {
        // Web platform using dart:html
        final blob = io.Blob([bytes], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        final url = io.Url.createObjectUrlFromBlob(blob);
        final anchor = io.document.createElement('a') as io.AnchorElement
          ..href = url
          ..style.display = 'none' // Hide the anchor
          ..download = fileName; // Set the file name
        io.document.body!.children.add(anchor); // Add to body
        anchor.click(); // Trigger download
        io.document.body!.children.remove(anchor); // Clean up
        io.Url.revokeObjectUrl(url); // Revoke the URL
      } else {
        // Non-web platforms using FilePicker
        final result = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Excel File',
          fileName: fileName,
          type: FileType.custom,
          allowedExtensions: ['xlsx'],
          bytes: bytes, // Provide bytes directly
        );

        // FilePicker.platform.saveFile handles saving to disk, no need for dart:io.File.writeAsBytes here
        if (result != null) {
           // FilePicker returns the path where the file was saved
           print('File saved to: $result'); // Optional: log the save path
        }
      }

      _showSnackBar('Data exported to Excel');
    } catch (e) {
      _showSnackBar('Error exporting to Excel: $e', isError: true);
      print('Error exporting to Excel: $e'); // Log the error
    }
  }


  // Search/Filter data (PlutoGrid handles filtering internally if you provide a filter)
  // For simplicity here, we'll rely on PlutoGrid's built-in filtering or implement external filtering if needed.
  // PlutoGrid has filtering capabilities built-in, which is more efficient than manual filtering.
  // You can enable filtering UI in PlutoGridConfiguration.

  // Helper to convert snake_case to Title Case
  String _getDisplayName(String column) {
    if (column == 'id') return 'ID'; // Special case for ID
    return column.split('_').map((word) =>
        word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        actions: [
          // Import/Export buttons
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: 'Import Excel',
            onPressed: _importFromExcel,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: 'Export Excel',
            onPressed: _exportToExcel,
          ),
          // Add Column button
          IconButton(
            icon: const Icon(Icons.add_chart),
            tooltip: 'Add Column',
            onPressed: _showAddColumnDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search field (PlutoGrid has built-in filtering, this can be removed
          // if you enable filtering in the configuration)
          // For demonstration, keeping a simple search bar here
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              // No controller needed if using PlutoGrid's internal filtering
              // If you need external filtering, you'd use a controller and filter the _rows list
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (query) {
                 // Example of triggering PlutoGrid's built-in filtering
                 // Requires enabling filtering in PlutoGridConfiguration
                 _stateManager?.setTextFilter(PlutoGridFilterType.contains, query);
              },
            ),
          ),

          // Data grid
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator(color: widget.themeColor))
                : _error != null
                    ? _buildErrorWidget()
                    : PlutoGrid(
                        columns: _columns,
                        rows: _rows,
                        onLoaded: _onLoaded,
                        onChanged: (PlutoGridOnChangedEvent event) {
                          // This callback is triggered when a cell value changes.
                          // The saving to Firestore is handled in the _onLoaded listener
                          // when a cell exits editing mode.
                          // You could also handle saving here if preferred, but need to
                          // debounce or batch updates to avoid excessive writes.
                        },
                         configuration: PlutoGridConfiguration(
                           style: PlutoGridStyleConfig(
                             cellColorInReadOnlyState: Colors.grey.shade200, // Color for non-editable cells (like ID)
                             activatedColor: widget.themeColor.withAlpha(50), // Selection color
                             checkedColor: widget.themeColor.withAlpha(30), // Checkbox color
                             gridBorderColor: Colors.grey.shade400,
                             borderColor: Colors.grey.shade400,
                             enableCellBorderHorizontal: true,
                             enableCellBorderVertical: true,
                             rowHeight: 40, // Default row height
                             columnFilterHeight: 40, // Height for filter row if enabled
                             columnContextIcon: Icons.more_vert, // Icon for column options
                           ),
                           // Enable built-in features
                           enableColumnContextMenuItem: true, // Enable column right-click menu (for delete, etc.)
                           enableRowContextMenuItem: true, // Enable row right-click menu
                           // enableFiltering: true, // Enable built-in filtering UI
                           // enableSorting: true, // Enable built-in sorting
                           // enableEditingMode: true, // Enable editing (default)
                           // enableAutoPagination: true, // Enable pagination for large datasets
                           // pagination: PlutoPagination(), // Configure pagination
                         ),
                         // Provide custom context menus if needed
                         // createHeaderContextMenuItem: (column, context) { ... },
                         // createRowContextMenuItem: (row, context) { ... },
                      ),
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Floating action button to add a new row
          FloatingActionButton(
            heroTag: 'addRow',
            tooltip: 'Add Row',
            onPressed: _addNewRow,
            child: const Icon(Icons.add),
          ),
          const SizedBox(height: 8),
          // Floating action button to delete selected rows
          FloatingActionButton(
            heroTag: 'delRow',
            tooltip: 'Delete Selected Rows',
            onPressed: _deleteSelectedRows,
            child: const Icon(Icons.delete),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
