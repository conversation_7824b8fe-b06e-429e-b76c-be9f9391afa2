import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart' hide Border, BorderStyle;
import 'dart:io';
import '../constants/app_constants.dart';
import 'card_view.dart';

class AdvancedDataGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const AdvancedDataGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  });

  @override
  State<AdvancedDataGrid> createState() => _AdvancedDataGridState();
}

class _AdvancedDataGridState extends State<AdvancedDataGrid> {
  // Controllers
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _cellEditController = TextEditingController();
  final TextEditingController _bulkEditController = TextEditingController();
  final TextEditingController _columnNameController = TextEditingController();
  final FocusNode _gridFocusNode = FocusNode();

  // Data state
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _filteredItems = [];
  List<String> _columns = [];
  bool _isLoading = true;
  String? _error;
  bool _isCardView = false;

  // Selection state
  final Map<String, Set<String>> _selectedCells = {}; // itemId -> Set of column names
  bool _isSelecting = false;
  String? _selectionStartItemId;
  String? _selectionStartColumn;
  int? _selectionStartRowIndex;
  int? _selectionStartColIndex;
  int? _selectionEndRowIndex;
  int? _selectionEndColIndex;

  // Editing state
  String? _editingItemId;
  String? _editingColumn;
  int? _editingRowIndex;
  int? _editingColIndex;
  bool _isBulkEditing = false;
  bool _isAddingColumn = false;

  // Column sorting
  String? _sortColumn;
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();

    // Initialize columns with predefined columns if provided
    if (widget.predefinedColumns != null) {
      _columns = List.from(widget.predefinedColumns!);
    } else {
      _columns = [
        'model',
        'description',
        'manufacturer',
        'approval',
        'ex_works_price',
        'local_price',
        'installation_price',
      ];
    }

    _loadData();

    // Add listener to focus node for keyboard events
    _gridFocusNode.addListener(() {
      if (_gridFocusNode.hasFocus) {
        // This ensures the grid can receive keyboard events
      }
    });
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _searchController.dispose();
    _cellEditController.dispose();
    _bulkEditController.dispose();
    _columnNameController.dispose();
    _gridFocusNode.dispose();
    super.dispose();
  }

  // Data loading and filtering
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final loadedItems = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Discover all columns from data
      final Set<String> columnSet = {'id'};

      // Add predefined columns
      columnSet.addAll(_columns);

      // Add any additional columns from the data
      for (var item in loadedItems) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      setState(() {
        _items = loadedItems;
        _filteredItems = List.from(loadedItems);
        _columns = columnSet.toList();
        _isLoading = false;
      });

      // Apply sorting if active
      if (_sortColumn != null) {
        _sortData();
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(_items);
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    setState(() {
      _filteredItems = _items.where((item) {
        // Search in all columns
        for (var column in _columns) {
          final value = item[column]?.toString().toLowerCase() ?? '';
          if (value.contains(lowercaseQuery)) {
            return true;
          }
        }
        return false;
      }).toList();
    });
  }

  void _sortData() {
    if (_sortColumn == null) return;

    setState(() {
      _filteredItems.sort((a, b) {
        final aValue = a[_sortColumn]?.toString() ?? '';
        final bValue = b[_sortColumn]?.toString() ?? '';

        // Try to parse as numbers if possible
        final aNum = double.tryParse(aValue);
        final bNum = double.tryParse(bValue);

        if (aNum != null && bNum != null) {
          return _sortAscending ? aNum.compareTo(bNum) : bNum.compareTo(aNum);
        }

        return _sortAscending ? aValue.compareTo(bValue) : bValue.compareTo(aValue);
      });
    });
  }

  // Cell editing
  void _startCellEdit(String itemId, String column, dynamic value, int rowIndex, int colIndex) {
    setState(() {
      _editingItemId = itemId;
      _editingColumn = column;
      _editingRowIndex = rowIndex;
      _editingColIndex = colIndex;
      _cellEditController.text = value?.toString() ?? '';
    });

    // Request focus to the text field
    FocusScope.of(context).requestFocus(FocusNode());
  }

  Future<void> _saveCellEdit() async {
    if (_editingItemId != null && _editingColumn != null) {
      try {
        final value = _cellEditController.text;

        // Update in Firestore
        await FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(_editingItemId)
            .update({_editingColumn!: value});

        // Update local data
        setState(() {
          final index = _items.indexWhere((item) => item['id'] == _editingItemId);
          if (index >= 0) {
            _items[index][_editingColumn!] = value;

            // Also update in filtered items
            final filteredIndex = _filteredItems.indexWhere((item) => item['id'] == _editingItemId);
            if (filteredIndex >= 0) {
              _filteredItems[filteredIndex][_editingColumn!] = value;
            }
          }
          _editingItemId = null;
          _editingColumn = null;
          _editingRowIndex = null;
          _editingColIndex = null;
        });

        // Re-sort if needed
        if (_sortColumn != null) {
          _sortData();
        }
      } catch (e) {
        _showSnackBar('Error updating cell: $e', isError: true);
      }
    }
  }

  void _cancelCellEdit() {
    setState(() {
      _editingItemId = null;
      _editingColumn = null;
      _editingRowIndex = null;
      _editingColIndex = null;
    });
  }

  // Cell selection
  void _handleCellTap(String itemId, String column, int rowIndex, int colIndex) {
    // If we're already editing a cell, save it first
    if (_editingItemId != null && _editingColumn != null) {
      _saveCellEdit();
      return;
    }

    // If we're in selection mode, handle selection
    if (_isSelecting) {
      _toggleCellSelection(itemId, column);
      return;
    }

    // Otherwise, select this cell but don't start editing yet
    // This allows for direct typing to start editing
    setState(() {
      _selectedCells.clear();
      if (!_selectedCells.containsKey(itemId)) {
        _selectedCells[itemId] = {};
      }
      _selectedCells[itemId]!.add(column);

      // Store the current cell for keyboard navigation
      _selectionStartItemId = itemId;
      _selectionStartColumn = column;
      _selectionStartRowIndex = rowIndex;
      _selectionStartColIndex = colIndex;

      // Request focus to the grid to receive keyboard events
      _gridFocusNode.requestFocus();
    });
  }

  void _handleCellMouseDown(String itemId, String column, int rowIndex, int colIndex) {
    setState(() {
      _isSelecting = true;
      _selectionStartItemId = itemId;
      _selectionStartColumn = column;
      _selectionStartRowIndex = rowIndex;
      _selectionStartColIndex = colIndex;

      // Clear previous selection
      _selectedCells.clear();

      // Add this cell to selection
      if (!_selectedCells.containsKey(itemId)) {
        _selectedCells[itemId] = {};
      }
      _selectedCells[itemId]!.add(column);
    });
  }

  void _handleCellMouseEnter(String itemId, String column, int rowIndex, int colIndex) {
    if (_isSelecting && _selectionStartRowIndex != null && _selectionStartColIndex != null) {
      // Calculate the range of cells to select
      final startRowIndex = _selectionStartRowIndex!;
      final startColIndex = _selectionStartColIndex!;

      final minRowIndex = startRowIndex < rowIndex ? startRowIndex : rowIndex;
      final maxRowIndex = startRowIndex > rowIndex ? startRowIndex : rowIndex;
      final minColIndex = startColIndex < colIndex ? startColIndex : colIndex;
      final maxColIndex = startColIndex > colIndex ? startColIndex : colIndex;

      // Clear previous selection
      setState(() {
        _selectedCells.clear();
        _selectionEndRowIndex = rowIndex;
        _selectionEndColIndex = colIndex;

        // Select all cells in the range
        for (int i = minRowIndex; i <= maxRowIndex; i++) {
          if (i >= _filteredItems.length) continue;

          final itemId = _filteredItems[i]['id'] as String;
          if (!_selectedCells.containsKey(itemId)) {
            _selectedCells[itemId] = {};
          }

          for (int j = minColIndex; j <= maxColIndex; j++) {
            if (j >= _columns.length) continue;
            _selectedCells[itemId]!.add(_columns[j]);
          }
        }
      });
    }
  }

  void _handleCellMouseUp() {
    setState(() {
      _isSelecting = false;
    });
  }

  void _toggleCellSelection(String itemId, String column) {
    setState(() {
      if (!_selectedCells.containsKey(itemId)) {
        _selectedCells[itemId] = {};
      }

      if (_selectedCells[itemId]!.contains(column)) {
        _selectedCells[itemId]!.remove(column);
        if (_selectedCells[itemId]!.isEmpty) {
          _selectedCells.remove(itemId);
        }
      } else {
        _selectedCells[itemId]!.add(column);
      }
    });
  }

  // Bulk editing
  void _startBulkEdit() {
    setState(() {
      _isBulkEditing = true;
      _bulkEditController.clear();
    });
  }

  Future<void> _applyBulkEdit() async {
    final newValue = _bulkEditController.text;

    // Apply to all selected cells
    for (var itemId in _selectedCells.keys) {
      final Map<String, dynamic> updates = {};

      for (var column in _selectedCells[itemId]!) {
        updates[column] = newValue;
      }

      if (updates.isNotEmpty) {
        try {
          await FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(itemId)
              .update(updates);

          // Update local data
          final index = _items.indexWhere((item) => item['id'] == itemId);
          if (index >= 0) {
            for (var column in updates.keys) {
              _items[index][column] = newValue;
            }
          }

          // Update filtered items
          final filteredIndex = _filteredItems.indexWhere((item) => item['id'] == itemId);
          if (filteredIndex >= 0) {
            for (var column in updates.keys) {
              _filteredItems[filteredIndex][column] = newValue;
            }
          }
        } catch (e) {
          _showSnackBar('Error updating item: $e', isError: true);
        }
      }
    }

    setState(() {
      _selectedCells.clear();
      _isBulkEditing = false;
    });

    // Re-sort if needed
    if (_sortColumn != null) {
      _sortData();
    }
  }

  void _cancelBulkEdit() {
    setState(() {
      _isBulkEditing = false;
    });
  }

  // Row operations
  Future<void> _addNewRow() async {
    final Map<String, dynamic> newItem = {};

    // Add default empty values for all columns
    for (var column in _columns) {
      if (column != 'id') {
        newItem[column] = '';
      }
    }

    newItem['createdAt'] = FieldValue.serverTimestamp();

    try {
      final docRef = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Add to local data with the new ID
      final newItemWithId = {
        'id': docRef.id,
        ...newItem,
      };

      setState(() {
        _items.add(newItemWithId);
        _filteredItems.add(newItemWithId);
      });

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }

      _showSnackBar('New row added successfully');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  Future<void> _deleteRow(String itemId) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(itemId)
          .delete();

      setState(() {
        _items.removeWhere((item) => item['id'] == itemId);
        _filteredItems.removeWhere((item) => item['id'] == itemId);
      });

      _showSnackBar('Row deleted successfully');
    } catch (e) {
      _showSnackBar('Error deleting row: $e', isError: true);
    }
  }

  // Column operations
  void _startAddColumn() {
    setState(() {
      _isAddingColumn = true;
      _columnNameController.clear();
    });
  }

  void _addColumn() {
    final columnName = _columnNameController.text.trim();
    if (columnName.isEmpty) {
      _showSnackBar('Column name cannot be empty', isError: true);
      return;
    }

    if (_columns.contains(columnName)) {
      _showSnackBar('Column already exists', isError: true);
      return;
    }

    setState(() {
      _columns.add(columnName);
      _isAddingColumn = false;
    });

    _showSnackBar('Column "$columnName" added successfully');
  }

  void _cancelAddColumn() {
    setState(() {
      _isAddingColumn = false;
    });
  }

  Future<void> _deleteColumn(String column) async {
    // Don't allow deleting the ID column
    if (column == 'id') {
      _showSnackBar('Cannot delete ID column', isError: true);
      return;
    }

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Column'),
        content: Text('Are you sure you want to delete the "$column" column? This will remove this data from all items.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // We need to update all documents to remove this field
      final batch = FirebaseFirestore.instance.batch();

      for (var item in _items) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(item['id'] as String);

        // Firebase doesn't have a direct "remove field" operation,
        // so we set it to FieldValue.delete()
        batch.update(docRef, {column: FieldValue.delete()});
      }

      await batch.commit();

      // Update local data
      setState(() {
        _columns.remove(column);

        for (var item in _items) {
          item.remove(column);
        }

        for (var item in _filteredItems) {
          item.remove(column);
        }
      });

      _showSnackBar('Column "$column" deleted successfully');
    } catch (e) {
      _showSnackBar('Error deleting column: $e', isError: true);
    }
  }

  // Import/Export
  Future<void> _importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result == null || result.files.isEmpty) return;

      final file = File(result.files.first.path!);
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      if (excel.tables.isEmpty) {
        _showSnackBar('No data found in Excel file', isError: true);
        return;
      }

      // Use the first sheet
      final sheet = excel.tables.entries.first.value;

      // Get headers from first row
      final headers = <String>[];
      final headerRow = sheet.rows.first;

      for (var cell in headerRow) {
        if (cell?.value != null) {
          headers.add(cell!.value.toString());
        }
      }

      if (headers.isEmpty) {
        _showSnackBar('No headers found in Excel file', isError: true);
        return;
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      final newItems = <Map<String, dynamic>>[];
      int count = 0;

      for (var i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final Map<String, dynamic> item = {};

        for (var j = 0; j < row.length && j < headers.length; j++) {
          final cell = row[j];
          if (cell?.value != null) {
            item[headers[j]] = cell!.value.toString();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc();
          batch.set(docRef, item);

          newItems.add({
            'id': docRef.id,
            ...item,
          });

          count++;
        }
      }

      await batch.commit();

      // Update columns if needed
      final Set<String> columnSet = {'id'};
      columnSet.addAll(_columns);

      for (var item in [..._items, ...newItems]) {
        columnSet.addAll(item.keys);
      }

      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Update local data
      setState(() {
        _columns = columnSet.toList();
        _items.addAll(newItems);
        _filteredItems.addAll(newItems);
      });

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }

      _showSnackBar('Imported $count items from Excel');
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
    }
  }

  Future<void> _exportToExcel() async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      // Add headers
      for (var i = 0; i < _columns.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value =
            TextCellValue(_columns[i]);
      }

      // Add data
      for (var i = 0; i < _filteredItems.length; i++) {
        final item = _filteredItems[i];

        for (var j = 0; j < _columns.length; j++) {
          final column = _columns[j];
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1)).value =
              TextCellValue(item[column]?.toString() ?? '');
        }
      }

      // Save file
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Excel File',
        fileName: '${widget.title.replaceAll(' ', '_')}_export.xlsx',
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
      );

      if (result != null) {
        final file = File(result);
        await file.writeAsBytes(excel.encode()!);
        _showSnackBar('Data exported to ${file.path}');
      }
    } catch (e) {
      _showSnackBar('Error exporting to Excel: $e', isError: true);
    }
  }

  Future<void> _importFromClipboard() async {
    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data == null || data.text == null) {
        _showSnackBar('No data in clipboard', isError: true);
        return;
      }

      // Parse clipboard data (assuming tab-separated values from Excel)
      final rows = data.text!.split('\n');
      if (rows.isEmpty) {
        _showSnackBar('No rows found in clipboard data', isError: true);
        return;
      }

      // Check if first row contains headers
      final firstRow = rows[0].split('\t');
      bool hasHeaders = true;
      final List<String> headers = [];

      // If the number of columns in the first row is reasonable,
      // we'll assume it contains headers
      if (firstRow.isNotEmpty && firstRow.length <= 20) {
        for (var header in firstRow) {
          if (header.trim().isNotEmpty) {
            headers.add(header.trim());
          }
        }
      } else {
        // If there are too many columns or none, we'll assume it's data
        hasHeaders = false;

        // Use existing columns as headers
        headers.addAll(_columns.where((col) => col != 'id'));
      }

      if (headers.isEmpty) {
        _showSnackBar('No valid headers found', isError: true);
        return;
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      final newItems = <Map<String, dynamic>>[];
      int count = 0;

      // Start from index 1 if we have headers, otherwise from 0
      final startIndex = hasHeaders ? 1 : 0;

      for (int i = startIndex; i < rows.length; i++) {
        if (rows[i].trim().isEmpty) continue;

        final values = rows[i].split('\t');
        if (values.isEmpty) continue;

        final Map<String, dynamic> item = {};

        // Map values to headers
        for (int j = 0; j < values.length && j < headers.length; j++) {
          if (values[j].trim().isNotEmpty) {
            item[headers[j]] = values[j].trim();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance.collection(widget.collectionPath).doc();
          batch.set(docRef, item);

          newItems.add({
            'id': docRef.id,
            ...item,
          });

          count++;
        }
      }

      await batch.commit();

      // Update columns if needed
      final Set<String> columnSet = {'id'};
      columnSet.addAll(_columns);

      for (var item in [..._items, ...newItems]) {
        columnSet.addAll(item.keys);
      }

      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Update local data
      setState(() {
        _columns = columnSet.toList();
        _items.addAll(newItems);
        _filteredItems.addAll(newItems);
      });

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }

      _showSnackBar('Imported $count items from clipboard');
    } catch (e) {
      _showSnackBar('Error importing from clipboard: $e', isError: true);
    }
  }

  // Clipboard operations for selected cells
  Future<void> _copySelectedCells() async {
    if (_selectedCells.isEmpty) {
      _showSnackBar('No cells selected', isError: true);
      return;
    }

    // Determine the range of the selection
    int minRowIndex = _filteredItems.length;
    int maxRowIndex = 0;
    int minColIndex = _columns.length;
    int maxColIndex = 0;

    for (var itemId in _selectedCells.keys) {
      final rowIndex = _filteredItems.indexWhere((item) => item['id'] == itemId);
      if (rowIndex < 0) continue;

      minRowIndex = rowIndex < minRowIndex ? rowIndex : minRowIndex;
      maxRowIndex = rowIndex > maxRowIndex ? rowIndex : maxRowIndex;

      for (var column in _selectedCells[itemId]!) {
        final colIndex = _columns.indexOf(column);
        if (colIndex < 0) continue;

        minColIndex = colIndex < minColIndex ? colIndex : minColIndex;
        maxColIndex = colIndex > maxColIndex ? colIndex : maxColIndex;
      }
    }

    // Build a 2D array of the selected cells
    final List<List<String>> data = [];

    for (int i = minRowIndex; i <= maxRowIndex; i++) {
      if (i >= _filteredItems.length) continue;

      final itemId = _filteredItems[i]['id'] as String;
      final row = <String>[];

      for (int j = minColIndex; j <= maxColIndex; j++) {
        if (j >= _columns.length) continue;

        final column = _columns[j];
        final isSelected = _selectedCells[itemId]?.contains(column) ?? false;

        if (isSelected) {
          row.add(_filteredItems[i][column]?.toString() ?? '');
        } else {
          row.add('');
        }
      }

      data.add(row);
    }

    // Convert to tab-separated values
    final clipboard = data.map((row) => row.join('\t')).join('\n');

    // Copy to clipboard
    await Clipboard.setData(ClipboardData(text: clipboard));

    _showSnackBar('Copied ${_countSelectedCells()} cells to clipboard');
  }

  Future<void> _pasteToSelectedCells() async {
    if (_selectedCells.isEmpty) {
      _showSnackBar('No cells selected', isError: true);
      return;
    }

    final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data == null || data.text == null) {
      _showSnackBar('No data in clipboard', isError: true);
      return;
    }

    // Parse clipboard data
    final rows = data.text!.split('\n');
    if (rows.isEmpty) {
      _showSnackBar('No rows found in clipboard data', isError: true);
      return;
    }

    // Get the starting cell for pasting
    final firstCell = _selectedCells.entries.first;
    final startRowIndex = _filteredItems.indexWhere((item) => item['id'] == firstCell.key);
    if (startRowIndex < 0) return;

    final startColIndex = _columns.indexOf(firstCell.value.first);
    if (startColIndex < 0) return;

    // Prepare batch update
    final batch = FirebaseFirestore.instance.batch();
    int cellsUpdated = 0;

    // Process each row from clipboard
    for (var i = 0; i < rows.length; i++) {
      final rowIndex = startRowIndex + i;
      if (rowIndex >= _filteredItems.length) continue;

      final itemId = _filteredItems[rowIndex]['id'] as String;
      final values = rows[i].split('\t');
      final Map<String, dynamic> updates = {};

      for (var j = 0; j < values.length; j++) {
        final colIndex = startColIndex + j;
        if (colIndex >= _columns.length) continue;

        final column = _columns[colIndex];
        if (column == 'id') continue;

        updates[column] = values[j];
        cellsUpdated++;
      }

      if (updates.isNotEmpty) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(itemId);

        batch.update(docRef, updates);

        // Update local data
        final item = _filteredItems[rowIndex];
        for (var key in updates.keys) {
          item[key] = updates[key];
        }

        // Also update in _items
        final originalIndex = _items.indexWhere((item) => item['id'] == itemId);
        if (originalIndex >= 0) {
          for (var key in updates.keys) {
            _items[originalIndex][key] = updates[key];
          }
        }
      }
    }

    await batch.commit();

    // Re-sort if needed
    if (_sortColumn != null) {
      _sortData();
    }

    _showSnackBar('Updated $cellsUpdated cells from clipboard');
  }

  // Utility methods
  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  int _countSelectedCells() {
    int count = 0;
    for (var columns in _selectedCells.values) {
      count += columns.length;
    }
    return count;
  }

  // Helper methods
  String _getDisplayName(String column) {
    // Convert snake_case to Title Case
    return column.split('_').map((word) =>
      word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  Future<void> _updateCell(String itemId, String column, String value) async {
    try {
      // Update in Firestore
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(itemId)
          .update({column: value});

      // Update local data
      final index = _items.indexWhere((item) => item['id'] == itemId);
      if (index >= 0) {
        setState(() {
          _items[index][column] = value;

          // Also update in filtered items
          final filteredIndex = _filteredItems.indexWhere((item) => item['id'] == itemId);
          if (filteredIndex >= 0) {
            _filteredItems[filteredIndex][column] = value;
          }
        });
      }

      _showSnackBar('Updated successfully');
    } catch (e) {
      _showSnackBar('Error updating: $e', isError: true);
    }
  }

  void _moveToNextCell(bool reverse) {
    if (_editingRowIndex == null || _editingColIndex == null) return;

    int nextColIndex = reverse ? _editingColIndex! - 1 : _editingColIndex! + 1;
    int nextRowIndex = _editingRowIndex!;

    // If we've reached the end of the row, move to the next row
    if (nextColIndex >= _columns.length) {
      nextColIndex = 0;
      nextRowIndex++;
    } else if (nextColIndex < 0) {
      nextColIndex = _columns.length - 1;
      nextRowIndex--;
    }

    // Make sure we're within bounds
    if (nextRowIndex < 0 || nextRowIndex >= _filteredItems.length) return;

    // Skip the ID column
    if (_columns[nextColIndex] == 'id') {
      nextColIndex = reverse ? nextColIndex - 1 : nextColIndex + 1;

      // Check bounds again
      if (nextColIndex < 0 || nextColIndex >= _columns.length) return;
    }

    // Start editing the next cell
    final itemId = _filteredItems[nextRowIndex]['id'] as String;
    final column = _columns[nextColIndex];
    final value = _filteredItems[nextRowIndex][column];

    _startCellEdit(itemId, column, value, nextRowIndex, nextColIndex);
  }

  void _moveToNextRow() {
    if (_editingRowIndex == null || _editingColIndex == null) return;

    int nextRowIndex = _editingRowIndex! + 1;

    // Make sure we're within bounds
    if (nextRowIndex >= _filteredItems.length) return;

    // Start editing the cell in the next row
    final itemId = _filteredItems[nextRowIndex]['id'] as String;
    final column = _columns[_editingColIndex!];
    final value = _filteredItems[nextRowIndex][column];

    _startCellEdit(itemId, column, value, nextRowIndex, _editingColIndex!);
  }

  Future<void> _clearSelectedCells() async {
    if (_selectedCells.isEmpty) return;

    // Prepare batch update
    final batch = FirebaseFirestore.instance.batch();
    int cellsCleared = 0;

    // Process each selected cell
    for (var itemId in _selectedCells.keys) {
      final Map<String, dynamic> updates = {};

      for (var column in _selectedCells[itemId]!) {
        if (column == 'id') continue;
        updates[column] = '';
      }

      if (updates.isNotEmpty) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(itemId);

        batch.update(docRef, updates);
        cellsCleared += updates.length;

        // Update local data
        final index = _filteredItems.indexWhere((item) => item['id'] == itemId);
        if (index >= 0) {
          for (var key in updates.keys) {
            _filteredItems[index][key] = '';
          }
        }

        // Also update in _items
        final originalIndex = _items.indexWhere((item) => item['id'] == itemId);
        if (originalIndex >= 0) {
          for (var key in updates.keys) {
            _items[originalIndex][key] = '';
          }
        }
      }
    }

    await batch.commit();

    _showSnackBar('Cleared $cellsCleared cells');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Toolbar
          _buildToolbar(),

          // Add column dialog
          if (_isAddingColumn)
            _buildAddColumnDialog(),

          // Bulk edit dialog
          if (_isBulkEditing)
            _buildBulkEditDialog(),

          // Search bar
          _buildSearchBar(),

          // Data grid or card view
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator(color: widget.themeColor))
                : _error != null
                    ? _buildErrorWidget()
                    : _isCardView
                        ? _buildCardView()
                        : _buildDataGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),

              // View toggle
              ToggleButtons(
                isSelected: [!_isCardView, _isCardView],
                onPressed: (index) {
                  setState(() {
                    _isCardView = index == 1;
                  });
                },
                borderRadius: BorderRadius.circular(4),
                children: const [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Icon(Icons.grid_on),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Icon(Icons.view_module),
                  ),
                ],
              ),
              const SizedBox(width: 16),

              // Action buttons
              if (_selectedCells.isNotEmpty)
                Row(
                  children: [
                    Text('${_countSelectedCells()} cells selected'),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _startBulkEdit,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Edit Selected'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _copySelectedCells,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.themeColor,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Copy'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _pasteToSelectedCells,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.themeColor,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Paste'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _clearSelectedCells,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Clear'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _selectedCells.clear();
                        });
                      },
                      child: const Text('Cancel'),
                    ),
                  ],
                )
              else
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: _loadData,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Refresh'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.themeColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _importFromExcel,
                      icon: const Icon(Icons.file_upload),
                      label: const Text('Import Excel'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.themeColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _exportToExcel,
                      icon: const Icon(Icons.file_download),
                      label: const Text('Export Excel'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.themeColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _importFromClipboard,
                      icon: const Icon(Icons.paste),
                      label: const Text('Import from Clipboard'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.themeColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _addNewRow,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Row'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _startAddColumn,
                      icon: const Icon(Icons.add_box),
                      label: const Text('Add Column'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Total: ${_filteredItems.length} items',
            style: const TextStyle(fontStyle: FontStyle.italic),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _filterData('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onChanged: _filterData,
      ),
    );
  }

  Widget _buildAddColumnDialog() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.blue.shade50,
      child: Row(
        children: [
          const Text('Enter column name:'),
          const SizedBox(width: 16),
          Expanded(
            child: TextField(
              controller: _columnNameController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              onSubmitted: (_) => _addColumn(),
            ),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: _addColumn,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Add'),
          ),
          const SizedBox(width: 8),
          TextButton(
            onPressed: _cancelAddColumn,
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildBulkEditDialog() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.orange.shade50,
      child: Row(
        children: [
          const Text('Enter value for selected cells:'),
          const SizedBox(width: 16),
          Expanded(
            child: TextField(
              controller: _bulkEditController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              onSubmitted: (_) => _applyBulkEdit(),
            ),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: _applyBulkEdit,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Apply'),
          ),
          const SizedBox(width: 8),
          TextButton(
            onPressed: _cancelBulkEdit,
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildDataGrid() {
    return KeyboardListener(
      focusNode: _gridFocusNode,
      onKeyEvent: (KeyEvent event) {
        if (event is KeyDownEvent) {
          if (_editingItemId != null && _editingColumn != null) {
            // Handle keyboard events while editing
            if (event.logicalKey == LogicalKeyboardKey.escape) {
              _cancelCellEdit();
            } else if (event.logicalKey == LogicalKeyboardKey.enter ||
                      event.logicalKey == LogicalKeyboardKey.tab) {
              _saveCellEdit();

              // Move to next cell if tab was pressed
              if (event.logicalKey == LogicalKeyboardKey.tab) {
                _moveToNextCell(HardwareKeyboard.instance.isShiftPressed);
              } else if (event.logicalKey == LogicalKeyboardKey.enter) {
                _moveToNextRow();
              }
            }
          } else {
            // Handle keyboard events for navigation
            if (event.logicalKey == LogicalKeyboardKey.keyC &&
                HardwareKeyboard.instance.isControlPressed) {
              _copySelectedCells();
            } else if (event.logicalKey == LogicalKeyboardKey.keyV &&
                      HardwareKeyboard.instance.isControlPressed) {
              _pasteToSelectedCells();
            } else if (event.logicalKey == LogicalKeyboardKey.delete ||
                      event.logicalKey == LogicalKeyboardKey.backspace) {
              _clearSelectedCells();
            }
          }
        }
      },
      child: Scrollbar(
        controller: _verticalController,
        thumbVisibility: true,
        child: Scrollbar(
          controller: _horizontalController,
          thumbVisibility: true,
          notificationPredicate: (notification) => notification.depth == 1,
          child: SingleChildScrollView(
            controller: _verticalController,
            child: SingleChildScrollView(
              controller: _horizontalController,
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: [
                  DataColumn(
                    label: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            widget.themeColor.withOpacity(0.7),
                            widget.themeColor.withOpacity(0.5),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: const Text(
                        'ID',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    onSort: (_, __) {}, // ID column is not sortable
                  ),
                  ..._columns.where((col) => col != 'id').map((column) {
                    return DataColumn(
                      label: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              widget.themeColor.withOpacity(0.7),
                              widget.themeColor.withOpacity(0.5),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _getDisplayName(column),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 4),
                            if (_sortColumn == column)
                              Icon(
                                _sortAscending
                                    ? Icons.arrow_upward
                                    : Icons.arrow_downward,
                                size: 16,
                                color: Colors.white,
                              ),
                            const SizedBox(width: 4),
                            InkWell(
                              onTap: () => _deleteColumn(column),
                              child: const Icon(
                                Icons.close,
                                size: 16,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                      onSort: (_, __) {
                        setState(() {
                          if (_sortColumn == column) {
                            _sortAscending = !_sortAscending;
                          } else {
                            _sortColumn = column;
                            _sortAscending = true;
                          }
                          _sortData();
                        });
                      },
                    );
                  }),
                  DataColumn(
                    label: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            widget.themeColor.withOpacity(0.7),
                            widget.themeColor.withOpacity(0.5),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: const Text(
                        'Actions',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    onSort: (_, __) {}, // Actions column is not sortable
                  ),
                ],
                rows: _filteredItems.asMap().entries.map((entry) {
                  final rowIndex = entry.key;
                  final item = entry.value;
                  final itemId = item['id'] as String;

                  return DataRow(
                    cells: [
                      DataCell(
                        Text(
                          itemId,
                          style: const TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ),
                      ..._columns.where((col) => col != 'id').toList().asMap().entries.map((colEntry) {
                        final colIndex = colEntry.key + 1; // +1 because we skipped the ID column
                        final column = colEntry.value;
                        final isSelected = _selectedCells[itemId]?.contains(column) ?? false;
                        final isEditing = _editingItemId == itemId &&
                                         _editingColumn == column &&
                                         _editingRowIndex == rowIndex &&
                                         _editingColIndex == colIndex;

                        return DataCell(
                          Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? widget.themeColor.withOpacity(0.2)
                                  : null,
                              border: Border.all(
                                color: isSelected
                                    ? widget.themeColor
                                    : Colors.grey.shade200,
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: isEditing
                                ? TextField(
                                    controller: _cellEditController,
                                    autofocus: true,
                                    decoration: const InputDecoration(
                                      border: InputBorder.none,
                                      contentPadding: EdgeInsets.symmetric(horizontal: 8),
                                    ),
                                    onSubmitted: (_) => _saveCellEdit(),
                                  )
                                : GestureDetector(
                                    onTap: () => _handleCellTap(itemId, column, rowIndex, colIndex),
                                    onTapDown: (_) => _handleCellMouseDown(itemId, column, rowIndex, colIndex),
                                    onTapUp: (_) => _handleCellMouseUp(),
                                    child: MouseRegion(
                                      onEnter: (_) => _handleCellMouseEnter(itemId, column, rowIndex, colIndex),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        child: Text(item[column]?.toString() ?? ''),
                                      ),
                                    ),
                                  ),
                          ),
                          onTap: () => _handleCellTap(itemId, column, rowIndex, colIndex),
                        );
                      }),
                      DataCell(
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                              onPressed: () => _deleteRow(itemId),
                              tooltip: 'Delete Row',
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCardView() {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: 400,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        return CardView(
          item: item,
          columns: _columns,
          themeColor: widget.themeColor,
          onEdit: (column, value) async {
            await _updateCell(item['id'] as String, column, value);
            // Refresh the data
            _loadData();
          },
          onDelete: () async {
            await _deleteRow(item['id'] as String);
            // Refresh the data
            _loadData();
          },
        );
      },
    );
  }
}
