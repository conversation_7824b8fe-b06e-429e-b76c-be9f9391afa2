import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Simple SQLite Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _message = 'Ready';
  bool _isLoading = false;
  Database? _db;
  List<Map<String, dynamic>> _data = [];

  @override
  void initState() {
    super.initState();
    _initDatabase();
  }

  @override
  void dispose() {
    _db?.close();
    super.dispose();
  }

  Future<void> _initDatabase() async {
    setState(() {
      _isLoading = true;
      _message = 'Initializing database...';
    });

    try {
      final dbPath = await getDatabasesPath();
      final path = join(dbPath, 'simple_test.db');
      
      // Delete existing database for testing
      await deleteDatabase(path);
      
      _db = await openDatabase(
        path,
        version: 1,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE test (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT,
              value INTEGER
            )
          ''');
          
          // Insert some test data
          await db.insert('test', {'name': 'Test 1', 'value': 100});
          await db.insert('test', {'name': 'Test 2', 'value': 200});
          await db.insert('test', {'name': 'Test 3', 'value': 300});
        },
      );
      
      setState(() {
        _message = 'Database initialized successfully';
      });
      
      await _loadData();
    } catch (e) {
      setState(() {
        _message = 'Error initializing database: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadData() async {
    if (_db == null) return;
    
    setState(() {
      _isLoading = true;
      _message = 'Loading data...';
    });
    
    try {
      final result = await _db!.query('test');
      setState(() {
        _data = result;
        _message = 'Data loaded successfully';
      });
    } catch (e) {
      setState(() {
        _message = 'Error loading data: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addColumn() async {
    if (_db == null) return;
    
    setState(() {
      _isLoading = true;
      _message = 'Adding column...';
    });
    
    try {
      await _db!.execute('ALTER TABLE test ADD COLUMN test_column TEXT');
      setState(() {
        _message = 'Column added successfully';
      });
      await _loadData();
    } catch (e) {
      setState(() {
        _message = 'Error adding column: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Simple SQLite Test'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status message
                  Container(
                    padding: const EdgeInsets.all(8.0),
                    color: Colors.grey.shade200,
                    width: double.infinity,
                    child: Text(
                      _message,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(height: 16.0),
                  
                  // Buttons
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: _loadData,
                        child: const Text('Load Data'),
                      ),
                      const SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: _addColumn,
                        child: const Text('Add Test Column'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16.0),
                  
                  // Data display
                  if (_data.isNotEmpty) ...[
                    const Text(
                      'Data:',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.0),
                    ),
                    const SizedBox(height: 8.0),
                    Expanded(
                      child: SingleChildScrollView(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: DataTable(
                            columns: _data.first.keys.map((key) => 
                              DataColumn(label: Text(key))
                            ).toList(),
                            rows: _data.map((row) => DataRow(
                              cells: row.keys.map((key) => 
                                DataCell(Text(row[key]?.toString() ?? ''))
                              ).toList(),
                            )).toList(),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}
