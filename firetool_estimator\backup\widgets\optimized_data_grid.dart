import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart'; // Assuming this file exists and defines primaryColor, accentColor

class OptimizedDataGrid extends StatefulWidget {
  final String collectionPath;
  final String title;

  const OptimizedDataGrid({
    super.key,
    required this.collectionPath,
    required this.title,
  });

  @override
  State<OptimizedDataGrid> createState() => _OptimizedDataGridState();
}

class _OptimizedDataGridState extends State<OptimizedDataGrid> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  List<Map<String, dynamic>> _items = [];
  bool _isLoading = true;
  String? _error;

  // Default column headers - Consider making this dynamic or configurable
  final List<String> _columnHeaders = [
    'model',
    'description',
    'manufacturer',
    'approval',
    'ex_works_price',
    'local_price',
    'installation_price',
  ];

  final TextEditingController _cellEditController = TextEditingController();
  String? _editingItemId;
  String? _editingColumn;

  final Map<String, Set<String>> _selectedCells = {};
  String? _selectionStartItemId;
  String? _selectionStartColumn;
  bool _isSelecting = false;
  final FocusNode _cellEditFocusNode = FocusNode();


  final TextEditingController _bulkEditController = TextEditingController();
  bool _isBulkEditing = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _cellEditController.dispose();
    _bulkEditController.dispose();
    _cellEditFocusNode.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, {bool isError = false, Duration duration = const Duration(seconds: 3)}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).removeCurrentSnackBar(); // Remove previous snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Theme.of(context).colorScheme.error : Colors.green,
        duration: duration,
      ),
    );
  }

  Future<void> _loadData() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _error = null;
      _clearSelection(); // Clear selection on reload
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .orderBy('createdAt', descending: true) // Example: order by creation time
          .get();

      final items = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        // Ensure all default column headers exist in the item data, even if null
        Map<String, dynamic> itemWithAllCols = {'id': doc.id};
        for (String header in _columnHeaders) {
          itemWithAllCols[header] = data[header];
        }
        // Add any other fields from Firestore that are not in _columnHeaders but needed
        data.forEach((key, value) {
          if (!itemWithAllCols.containsKey(key)) {
            itemWithAllCols[key] = value;
          }
        });
        return itemWithAllCols;
      }).toList();

      if (!mounted) return;
      setState(() {
        _items = items;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _error = 'Error loading data: ${e.toString()}';
        _isLoading = false;
      });
      _showSnackBar(_error!, isError: true);
    }
  }

  void _startCellEdit(String itemId, String column) {
    final item = _items.firstWhere((item) => item['id'] == itemId, orElse: () => {});
    if (item.isEmpty) return;


    setState(() {
      _editingItemId = itemId;
      _editingColumn = column;
      _cellEditController.text = item[column]?.toString() ?? '';
      // Request focus for the TextField
      FocusScope.of(context).requestFocus(_cellEditFocusNode);
    });
  }

  Future<void> _saveCellEdit({String? valueOverride}) async {
    if (_editingItemId == null || _editingColumn == null) return;

    final originalItemIndex = _items.indexWhere((item) => item['id'] == _editingItemId);
    if (originalItemIndex < 0) return;

    final Map<String, dynamic> originalData = Map.from(_items[originalItemIndex]);
    final String editingColumnCopy = _editingColumn!;
    final String editingItemIdCopy = _editingItemId!;


    setState(() {
      // Optimistically update UI
      final valueToSave = valueOverride ?? _cellEditController.text;
      _items[originalItemIndex][editingColumnCopy] = valueToSave;
      _editingItemId = null;
      _editingColumn = null;
    });
     _cellEditController.clear();


    try {
      final valueToSave = valueOverride ?? _items[originalItemIndex][editingColumnCopy];
      dynamic parsedValue = valueToSave;
      // Try to parse numbers for price fields, adapt as needed for other types
      if (editingColumnCopy.contains('price')) {
        parsedValue = double.tryParse(valueToSave.toString()) ?? valueToSave;
      }

      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(editingItemIdCopy)
          .update({editingColumnCopy: parsedValue});
      // _showSnackBar('Cell updated successfully!'); // Optional: too noisy?
    } catch (e) {
      if (mounted) {
        _showSnackBar('Error updating cell: $e', isError: true);
        // Revert optimistic update if save failed
        setState(() {
          _items[originalItemIndex] = originalData; // Revert to original data
        });
      }
    }
  }

  void _cancelCellEdit() {
    setState(() {
      _editingItemId = null;
      _editingColumn = null;
      _cellEditController.clear();
    });
  }

 void _handleCellTap(String itemId, String column) {
    if (_editingItemId == itemId && _editingColumn == column) {
      // Already editing this cell, do nothing on tap
      return;
    }

    if (_editingItemId != null) {
      // If editing another cell, save it first
      _saveCellEdit().then((_) {
        // After saving, if not mounted, exit
        if (!mounted) return;
        // If the tap is not on an action cell and is a valid column, start editing the new cell
        if (column != 'actions' && _columnHeaders.contains(column)) {
          _startCellEdit(itemId, column);
        }
      });
    } else {
      // No cell is being edited, start editing the tapped cell
      if (column != 'actions' && _columnHeaders.contains(column)) {
        _startCellEdit(itemId, column);
      }
    }
  }


  void _clearSelection() {
    setState(() {
      _selectedCells.clear();
    });
  }

  void _handleCellPanStart(String itemId, String column) {
    // If editing, save and exit edit mode before starting selection
    if (_editingItemId != null || _editingColumn != null) {
      _saveCellEdit();
    }
    _cancelCellEdit(); // Ensure edit mode is fully exited

    setState(() {
      _isSelecting = true;
      _selectionStartItemId = itemId;
      _selectionStartColumn = column;
      _selectedCells.clear(); // Clear previous selection for a new drag
      _updateSelection(itemId, column); // Select the first cell
    });
  }

  void _handleCellPanUpdate(String currentItemId, String currentColumn) {
    if (_isSelecting) {
      _selectCellsInRange(_selectionStartItemId!, _selectionStartColumn!, currentItemId, currentColumn);
    }
  }
  
  void _handleCellPanEnd() {
    if (_isSelecting) {
      setState(() {
        _isSelecting = false;
        // _selectionStartItemId and _selectionStartColumn remain for context if needed
      });
    }
  }


  void _updateSelection(String itemId, String column) {
    if (!_selectedCells.containsKey(itemId)) {
      _selectedCells[itemId] = {};
    }
    _selectedCells[itemId]!.add(column);
  }

  void _selectCellsInRange(String startItemId, String startColumn, String endItemId, String endColumn) {
    final tempSelectedCells = <String, Set<String>>{};

    final startItemIndex = _items.indexWhere((item) => item['id'] == startItemId);
    final endItemIndex = _items.indexWhere((item) => item['id'] == endItemId);
    final startColumnIndex = _columnHeaders.indexOf(startColumn);
    final endColumnIndex = _columnHeaders.indexOf(endColumn);

    if (startItemIndex < 0 || endItemIndex < 0 || startColumnIndex < 0 || endColumnIndex < 0) return;

    final minRow = startItemIndex < endItemIndex ? startItemIndex : endItemIndex;
    final maxRow = startItemIndex > endItemIndex ? startItemIndex : endItemIndex;
    final minCol = startColumnIndex < endColumnIndex ? startColumnIndex : endColumnIndex;
    final maxCol = startColumnIndex > endColumnIndex ? startColumnIndex : endColumnIndex;

    for (int i = minRow; i <= maxRow; i++) {
      final itemId = _items[i]['id'] as String;
      tempSelectedCells[itemId] = {};
      for (int j = minCol; j <= maxCol; j++) {
        tempSelectedCells[itemId]!.add(_columnHeaders[j]);
      }
    }
    setState(() {
      _selectedCells.clear();
      _selectedCells.addAll(tempSelectedCells);
    });
  }


  void _startBulkEdit() {
    if (_selectedCells.isEmpty) {
      _showSnackBar('No cells selected for bulk edit.', isError: true);
      return;
    }
    setState(() {
      _isBulkEditing = true;
      _bulkEditController.clear();
    });
  }

  Future<void> _applyBulkEdit() async {
    if (!_isBulkEditing || _selectedCells.isEmpty) return;

    final newValue = _bulkEditController.text;
    final WriteBatch batch = FirebaseFirestore.instance.batch();
    final List<Map<String, dynamic>> localUpdates = [];

    _selectedCells.forEach((itemId, columns) {
      final itemIndex = _items.indexWhere((item) => item['id'] == itemId);
      if (itemIndex >= 0) {
        Map<String, dynamic> firestoreUpdate = {};
        Map<String, dynamic> localItemUpdate = {'id': itemId};

        for (String column in columns) {
          dynamic parsedValue = newValue;
          if (column.contains('price')) { // Example: parse price fields
            parsedValue = double.tryParse(newValue) ?? newValue;
          }
          firestoreUpdate[column] = parsedValue;
          localItemUpdate[column] = parsedValue;
        }
        if (firestoreUpdate.isNotEmpty) {
          batch.update(
              FirebaseFirestore.instance.collection(widget.collectionPath).doc(itemId),
              firestoreUpdate);
          localUpdates.add(localItemUpdate);
        }
      }
    });

    try {
      await batch.commit();
      setState(() {
        for (var update in localUpdates) {
          final index = _items.indexWhere((item) => item['id'] == update['id']);
          if (index >= 0) {
            update.forEach((key, value) {
              if (key != 'id') {
                _items[index][key] = value;
              }
            });
          }
        }
        _isBulkEditing = false;
        _bulkEditController.clear();
        _clearSelection();
      });
      _showSnackBar('Bulk edit applied successfully.');
    } catch (e) {
      if (mounted) {
        _showSnackBar('Error applying bulk edit: $e', isError: true);
      }
    }
  }


  void _cancelBulkEdit() {
    setState(() {
      _isBulkEditing = false;
      _bulkEditController.clear();
    });
  }

  Future<void> _addNewRow() async {
    setState(() => _isLoading = true); // Show loading indicator
    final Map<String, dynamic> newItemData = {
      'createdAt': FieldValue.serverTimestamp(),
    };
    for (var column in _columnHeaders) {
      newItemData[column] = ''; // Initialize with empty strings or default values
    }

    try {
      await FirebaseFirestore.instance.collection(widget.collectionPath).add(newItemData);
      await _loadData(); // Reload data to get the new row and defaults
      _showSnackBar('New row added. Scroll to bottom or sort to find it.');
    } catch (e) {
      if (mounted) {
        _showSnackBar('Error adding new row: $e', isError: true);
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _deleteSelectedRows() async {
    if (_selectedCells.isEmpty) {
        _showSnackBar('No rows selected to delete.', isError: true);
        return;
    }

    // Extract unique item IDs to delete (as selection might be across multiple cells of the same row)
    final Set<String> itemIdsToDelete = _selectedCells.keys.toSet();

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Delete'),
          content: Text('Are you sure you want to delete ${itemIdsToDelete.length} selected row(s)? This action cannot be undone.'),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      final batch = FirebaseFirestore.instance.batch();
      for (String itemId in itemIdsToDelete) {
        batch.delete(FirebaseFirestore.instance.collection(widget.collectionPath).doc(itemId));
      }
      try {
        await batch.commit();
        _showSnackBar('${itemIdsToDelete.length} row(s) deleted successfully.');
        _loadData(); // Refresh data
      } catch (e) {
        if (mounted) {
          _showSnackBar('Error deleting rows: $e', isError: true);
        }
      }
    }
  }


  Future<void> _importFromClipboard() async {
    final ClipboardData? clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData == null || clipboardData.text == null || clipboardData.text!.isEmpty) {
      _showSnackBar('Clipboard is empty or contains no text.', isError: true);
      return;
    }

    final String data = clipboardData.text!;
    List<String> rows = data.split('\n').where((row) => row.trim().isNotEmpty).toList();

    if (rows.isEmpty) {
      _showSnackBar('No valid rows found in clipboard data.', isError: true);
      return;
    }

    final bool hasHeaderRow = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clipboard Import'),
        content: const Text('Does the first row of your copied data contain headers?'),
        actions: [
          TextButton(child: const Text('No'), onPressed: () => Navigator.pop(context, false)),
          TextButton(child: const Text('Yes'), onPressed: () => Navigator.pop(context, true)),
        ],
      ),
    ) ?? false;

    if (hasHeaderRow && rows.isNotEmpty) {
      rows.removeAt(0); // Remove header row
    }

    if (rows.isEmpty) {
      _showSnackBar('No data rows to import after handling header.', isError: true);
      return;
    }


    final batch = FirebaseFirestore.instance.batch();
    int count = 0;

    for (final rowString in rows) {
      if (rowString.trim().isEmpty) continue;
      final cells = rowString.split('\t'); // Assuming tab-separated values

      final Map<String, dynamic> newItem = {'createdAt': FieldValue.serverTimestamp()};
      for (int j = 0; j < _columnHeaders.length; j++) {
        if (j < cells.length) {
          String cellValue = cells[j].trim();
          // Basic unquoting (Excel often adds extra quotes)
          if (cellValue.startsWith('"') && cellValue.endsWith('"') && cellValue.length > 1) {
            cellValue = cellValue.substring(1, cellValue.length - 1);
          }
          cellValue = cellValue.replaceAll('""', '"'); // Unescape double quotes

          // Try to parse numbers for price fields, adapt as needed
          if (_columnHeaders[j].contains('price')) {
            newItem[_columnHeaders[j]] = double.tryParse(cellValue) ?? cellValue;
          } else {
            newItem[_columnHeaders[j]] = cellValue;
          }
        } else {
          newItem[_columnHeaders[j]] = ''; // Default for missing cells
        }
      }
      batch.set(FirebaseFirestore.instance.collection(widget.collectionPath).doc(), newItem);
      count++;
    }

    if (count > 0) {
      try {
        await batch.commit();
        _showSnackBar('Successfully imported $count items from clipboard.');
        _loadData(); // Refresh data
      } catch (e) {
        if (mounted) {
          _showSnackBar('Error importing from clipboard: $e', isError: true);
        }
      }
    } else {
      _showSnackBar('No items were imported from clipboard.', isError: true);
    }
  }

  Future<void> _copySelectedToClipboard() async {
    if (_selectedCells.isEmpty) {
      _showSnackBar('No cells selected to copy.', isError: true);
      return;
    }

    // Determine the range of selected rows and columns to maintain structure
    List<String> orderedItemIds = _items.map((item) => item['id'] as String).toList();
    List<String> orderedColumnHeaders = List.from(_columnHeaders); // Use current headers

    int minRowIndex = orderedItemIds.length;
    int maxRowIndex = -1;
    int minColIndex = orderedColumnHeaders.length;
    int maxColIndex = -1;

    _selectedCells.forEach((itemId, columns) {
      final rowIndex = orderedItemIds.indexOf(itemId);
      if (rowIndex != -1) {
        if (rowIndex < minRowIndex) minRowIndex = rowIndex;
        if (rowIndex > maxRowIndex) maxRowIndex = rowIndex;
        for (String column in columns) {
          final colIndex = orderedColumnHeaders.indexOf(column);
          if (colIndex != -1) {
            if (colIndex < minColIndex) minColIndex = colIndex;
            if (colIndex > maxColIndex) maxColIndex = colIndex;
          }
        }
      }
    });
    
    if (minRowIndex > maxRowIndex || minColIndex > maxColIndex) {
         _showSnackBar('Selection error or no valid cells to copy.', isError: true);
        return;
    }


    StringBuffer buffer = StringBuffer();

    // Optional: Add headers if you want them in the copied data
    // for (int j = minColIndex; j <= maxColIndex; j++) {
    //   buffer.write(_columnHeaders[j]);
    //   if (j < maxColIndex) buffer.write('\t');
    // }
    // buffer.writeln();


    for (int i = minRowIndex; i <= maxRowIndex; i++) {
      final itemId = orderedItemIds[i];
      final itemData = _items.firstWhere((item) => item['id'] == itemId);

      for (int j = minColIndex; j <= maxColIndex; j++) {
        final column = orderedColumnHeaders[j];
        // Check if this specific cell is selected, or if the entire row range should be copied based on any selection in that row
        bool isCellActuallySelected = _selectedCells[itemId]?.contains(column) ?? false;
        
        if(isCellActuallySelected) { // Or simplify to copy the whole rectangular block regardless of sparse selection within it
            String cellValue = itemData[column]?.toString() ?? '';
            // Escape for TSV: replace tabs with spaces, newlines with spaces, wrap in quotes if necessary
            cellValue = cellValue.replaceAll('\t', ' ').replaceAll('\n', ' ');
            // Basic CSV-like quoting for values containing quotes or delimiters, not fully robust
            if (cellValue.contains('"') || cellValue.contains('\t')) {
                cellValue = '"${cellValue.replaceAll('"', '""')}"';
            }
            buffer.write(cellValue);
        } else {
            buffer.write(''); // Write empty for non-selected cells within the block
        }

        if (j < maxColIndex) {
          buffer.write('\t'); // Tab-separated
        }
      }
      if (i < maxRowIndex) {
        buffer.writeln(); // Newline for next row
      }
    }

    await Clipboard.setData(ClipboardData(text: buffer.toString()));
    _showSnackBar('Selected cells copied to clipboard!');
  }

  Widget _buildDataGrid() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_error != null) {
      return Center(child: Text(_error!, style: const TextStyle(color: Colors.red, fontSize: 16)));
    }
    if (_items.isEmpty) {
      return const Center(child: Text('No data available for this collection.'));
    }

    // Ensure we always have an 'id' column conceptually, even if not displayed directly as a user column
    List<DataColumn> dataColumns = [
      const DataColumn(label: Center(child: Icon(Icons.settings, size: 20))), // Actions
      ..._columnHeaders.map((header) => DataColumn(
        label: Expanded(
          child: Text(
            header.replaceAll('_', ' ').split(' ').map((e) => e.isNotEmpty ?'${e[0].toUpperCase()}${e.substring(1)}' : '').join(' '), // Prettify header
            style: const TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
      )),
    ];

    return Scrollbar(
      controller: _horizontalController,
      thumbVisibility: true,
      trackVisibility: true,
      child: SingleChildScrollView( // Horizontal scroll for the DataTable
        controller: _horizontalController,
        scrollDirection: Axis.horizontal,
        child: ConstrainedBox(
          constraints: BoxConstraints(minWidth: MediaQuery.of(context).size.width), // Ensure DataTable takes at least screen width
          child: DataTable(
            columnSpacing: 20.0,
            headingRowColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
              return Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3);
            }),
            columns: dataColumns,
            rows: _items.map((item) {
              final String itemId = item['id'] as String;
              return DataRow(
                color: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
                    bool rowSelected = _selectedCells.containsKey(itemId) && _selectedCells[itemId]!.isNotEmpty;
                    if (rowSelected) return Theme.of(context).primaryColor.withOpacity(0.1);
                    return null; // Use default value for other states and odd/even rows.
                }),
                cells: [
                  DataCell(
                    IconButton(
                      icon: Icon(Icons.delete_outline, color: Theme.of(context).colorScheme.error),
                      tooltip: 'Delete Row',
                      onPressed: () => _deleteSelectedRows(), // Changed to delete selected, or implement single delete logic
                    ),
                  ),
                  ..._columnHeaders.map((column) {
                    final isEditing = _editingItemId == itemId && _editingColumn == column;
                    final isSelected = _selectedCells[itemId]?.contains(column) ?? false;

                    return DataCell(
                      GestureDetector(
                        onTap: () => _handleCellTap(itemId, column),
                        onPanStart: (details) => _handleCellPanStart(itemId, column),
                        onPanUpdate: (details) => _handleCellPanUpdate(itemId, column),
                        onPanEnd: (details) => _handleCellPanEnd(),
                        child: Container(
                          width: 150, // Default cell width, consider making dynamic or fitting content
                          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                          decoration: BoxDecoration(
                            color: isSelected ? AppConstants.accentColor.withOpacity(0.3) : null,
                            border: Border.all(
                              color: isEditing ? AppConstants.primaryColor : Colors.grey.shade300,
                              width: isEditing ? 1.5 : 0.5,
                            ),
                          ),
                          child: isEditing
                              ? Center( // Center the TextField within the cell
                                child: IntrinsicWidth( // Make TextField take necessary width
                                  child: TextField(
                                    controller: _cellEditController,
                                    focusNode: _cellEditFocusNode,
                                    autofocus: true,
                                    decoration: const InputDecoration(
                                      border: InputBorder.none,
                                      isDense: true,
                                      contentPadding: EdgeInsets.zero,
                                    ),
                                    style: const TextStyle(fontSize: 14),
                                    onSubmitted: (_) => _saveCellEdit(),
                                    onTapOutside: (_) { // Save when user clicks outside
                                       if (_editingItemId != null) _saveCellEdit();
                                    },
                                  ),
                                ),
                              )
                              : Padding( // Add padding for non-editing text
                                  padding: const EdgeInsets.symmetric(vertical: 6.0), // Ensure text is vertically centered
                                  child: Text(
                                    item[column]?.toString() ?? '',
                                    style: const TextStyle(fontSize: 14),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                        ),
                      ),
                    );
                  }),
                ],
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // AppBar is now part of the parent screen (e.g., _buildFireAlarmModuleScreen)
      // If this OptimizedDataGrid is used as a standalone screen, it would need its own AppBar.
      // For now, assuming it's embedded.
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Wrap( // Use Wrap for better responsiveness of buttons
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                ElevatedButton.icon(
                  onPressed: _loadData,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                  style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor, foregroundColor: Colors.white),
                ),
                ElevatedButton.icon(
                  onPressed: _importFromClipboard,
                  icon: const Icon(Icons.content_paste_go),
                  label: const Text('Paste from Excel/Clipboard'),
                   style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.accentColor, foregroundColor: Colors.white),
                ),
                ElevatedButton.icon(
                  onPressed: _copySelectedToClipboard, // NEW BUTTON
                  icon: const Icon(Icons.copy_all_rounded),
                  label: const Text('Copy Selected'),
                   style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blueGrey, foregroundColor: Colors.white),
                ),
                ElevatedButton.icon(
                  onPressed: _addNewRow,
                  icon: const Icon(Icons.add_circle_outline),
                  label: const Text('Add New Row'),
                   style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green, foregroundColor: Colors.white),
                ),
                if (_selectedCells.isNotEmpty)
                  ElevatedButton.icon(
                    onPressed: _startBulkEdit,
                    icon: const Icon(Icons.edit_note),
                    label: Text('Bulk Edit (${_selectedCells.values.fold<int>(0, (total, cells) => total + cells.length)})'),
                     style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange, foregroundColor: Colors.white),
                  ),
                 if (_selectedCells.isNotEmpty)
                  ElevatedButton.icon(
                    onPressed: _deleteSelectedRows,
                    icon: const Icon(Icons.delete_sweep_outlined),
                    label: Text('Delete Selected (${_selectedCells.keys.length})'),
                    style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.error, foregroundColor: Colors.white),
                  ),
              ],
            ),
          ),
          if (_isBulkEditing)
            Padding(
              padding: const EdgeInsets.fromLTRB(12.0, 0, 12.0, 12.0),
              child: Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _bulkEditController,
                          decoration: const InputDecoration(
                            labelText: 'Enter new value for selected cells',
                            border: OutlineInputBorder(),
                            isDense: true,
                          ),
                          autofocus: true,
                        ),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton(
                        onPressed: _applyBulkEdit,
                         style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green, foregroundColor: Colors.white),
                        child: const Text('Apply'),
                      ),
                      const SizedBox(width: 8),
                      TextButton(
                        onPressed: _cancelBulkEdit,
                        child: const Text('Cancel'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _error != null
                    ? Center(child: Text(_error!, style: const TextStyle(color: Colors.red, fontSize: 16)))
                    : _items.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.table_rows_outlined, size: 60, color: Colors.grey),
                                const SizedBox(height: 16),
                                const Text('No data available.', style: TextStyle(fontSize: 18, color: Colors.grey)),
                                const SizedBox(height: 8),
                                Text('Try adding a new row or importing data for "${widget.title}".', style: const TextStyle(color: Colors.grey)),
                              ],
                            ),
                          )
                        : _buildDataGrid(),
          ),
        ],
      ),
    );
  }
}