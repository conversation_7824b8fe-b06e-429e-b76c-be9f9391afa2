import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import '../models/flexible_table_models.dart';
import '../services/flexible_database_service.dart';
import '../widgets/flexible_data_grid.dart';
import 'database_management_screen.dart';

class FlexibleDataGridScreen extends StatefulWidget {
  const FlexibleDataGridScreen({super.key});

  @override
  State<FlexibleDataGridScreen> createState() => _FlexibleDataGridScreenState();
}

class _FlexibleDataGridScreenState extends State<FlexibleDataGridScreen> {
  final FlexibleDatabaseService _databaseService = FlexibleDatabaseService();
  List<FlexibleDatabase> _databases = [];
  FlexibleDatabase? _selectedDatabase;
  List<FlexibleTable> _tables = [];
  FlexibleTable? _selectedTable;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadDatabases();
  }

  Future<void> _loadDatabases() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final databases = await _databaseService.getAllDatabases();
      
      setState(() {
        _databases = databases;
        _isLoading = false;
        
        // If we have databases, select the first one
        if (_databases.isNotEmpty) {
          _selectedDatabase = _databases.first;
          _loadTables(_selectedDatabase!.databaseId!);
        }
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading databases: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadTables(String databaseId) async {
    setState(() {
      _isLoading = true;
      _error = null;
      _selectedTable = null;
    });

    try {
      final tables = await _databaseService.getTablesForDatabase(databaseId);
      
      setState(() {
        _tables = tables;
        _isLoading = false;
        
        // If we have tables, select the first one
        if (_tables.isNotEmpty) {
          _selectedTable = _tables.first;
        }
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading tables: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _createDatabase() async {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Database'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Database Name',
                hintText: 'Enter a name for the database',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Enter a description',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a database name')),
                );
                return;
              }
              
              Navigator.of(context).pop({
                'name': nameController.text.trim(),
                'description': descriptionController.text.trim(),
              });
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
    
    if (result != null) {
      try {
        final database = await _databaseService.createDatabase(
          result['name']!,
          description: result['description']!.isNotEmpty ? result['description'] : null,
        );
        
        setState(() {
          _databases.add(database);
          _selectedDatabase = database;
          _tables = [];
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Database "${result['name']}" created')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error creating database: $e')),
          );
        }
      }
    }
  }

  Future<void> _createTable() async {
    if (_selectedDatabase == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a database first')),
      );
      return;
    }
    
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Table'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Table Name',
                hintText: 'Enter a name for the table',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Enter a description',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a table name')),
                );
                return;
              }
              
              Navigator.of(context).pop({
                'name': nameController.text.trim(),
                'description': descriptionController.text.trim(),
              });
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
    
    if (result != null) {
      try {
        final table = await _databaseService.createTable(
          _selectedDatabase!.databaseId!,
          result['name']!,
          description: result['description']!.isNotEmpty ? result['description'] : null,
        );
        
        setState(() {
          _tables.add(table);
          _selectedTable = table;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Table "${result['name']}" created')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error creating table: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flexible Data Grid'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'Database Management',
            onPressed: () async {
              await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DatabaseManagementScreen(),
                ),
              );
              _loadDatabases();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : _buildContent(),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        _buildDatabaseSelector(),
        const Divider(),
        _buildTableSelector(),
        const Divider(),
        Expanded(
          child: _selectedTable != null
              ? FlexibleDataGrid(tableId: _selectedTable!.tableId!)
              : const Center(child: Text('Select or create a table to get started')),
        ),
      ],
    );
  }

  Widget _buildDatabaseSelector() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          const Text('Database:', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 16),
          Expanded(
            child: _databases.isEmpty
                ? const Text('No databases found')
                : DropdownButton<String>(
                    value: _selectedDatabase?.databaseId,
                    isExpanded: true,
                    hint: const Text('Select a database'),
                    onChanged: (value) {
                      if (value != null) {
                        final database = _databases.firstWhere(
                          (db) => db.databaseId == value,
                        );
                        setState(() {
                          _selectedDatabase = database;
                        });
                        _loadTables(value);
                      }
                    },
                    items: _databases.map((database) {
                      return DropdownMenuItem<String>(
                        value: database.databaseId,
                        child: Text(database.name ?? 'Unnamed Database'),
                      );
                    }).toList(),
                  ),
          ),
          const SizedBox(width: 16),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('New Database'),
            onPressed: _createDatabase,
          ),
        ],
      ),
    );
  }

  Widget _buildTableSelector() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          const Text('Table:', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 16),
          Expanded(
            child: _selectedDatabase == null
                ? const Text('Select a database first')
                : _tables.isEmpty
                    ? const Text('No tables found')
                    : DropdownButton<String>(
                        value: _selectedTable?.tableId,
                        isExpanded: true,
                        hint: const Text('Select a table'),
                        onChanged: (value) {
                          if (value != null) {
                            final table = _tables.firstWhere(
                              (tbl) => tbl.tableId == value,
                            );
                            setState(() {
                              _selectedTable = table;
                            });
                          }
                        },
                        items: _tables.map((table) {
                          return DropdownMenuItem<String>(
                            value: table.tableId,
                            child: Text(table.name ?? 'Unnamed Table'),
                          );
                        }).toList(),
                      ),
          ),
          const SizedBox(width: 16),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('New Table'),
            onPressed: _selectedDatabase != null ? _createTable : null,
          ),
        ],
      ),
    );
  }
}
