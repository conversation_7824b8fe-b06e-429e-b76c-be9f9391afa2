import 'package:flutter/material.dart';
import '../widgets/mock_admin_data_grid.dart';

class MockAdvancedDataGrid extends StatelessWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String> predefinedColumns;

  const MockAdvancedDataGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    required this.predefinedColumns,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: themeColor,
      ),
      body: Column(
        children: [
          // Header with info
          Container(
            padding: const EdgeInsets.all(16),
            color: themeColor.withOpacity(0.1),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: themeColor,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Managing $title Data',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: themeColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'This is a mock implementation for testing. Firebase functionality is disabled.',
                        style: TextStyle(
                          color: themeColor.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Data grid
          Expanded(
            child: MockAdminDataGrid(
              collectionPath: collectionPath,
            ),
          ),
        ],
      ),
    );
  }
}
