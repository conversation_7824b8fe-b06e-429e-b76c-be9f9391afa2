import 'package:flutter/material.dart';
import 'models/project.dart';
import 'services/database_service.dart';

void main() {
  runApp(const TestDatabaseApp());
}

class TestDatabaseApp extends StatelessWidget {
  const TestDatabaseApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Database Test',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
      ),
      home: const TestDatabaseScreen(),
    );
  }
}

class TestDatabaseScreen extends StatefulWidget {
  const TestDatabaseScreen({super.key});

  @override
  State<TestDatabaseScreen> createState() => _TestDatabaseScreenState();
}

class _TestDatabaseScreenState extends State<TestDatabaseScreen> {
  final DatabaseService _databaseService = DatabaseService();
  String _result = 'No operations performed yet';
  List<Project> _projects = [];
  Project? _currentProject;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    try {
      final projects = await _databaseService.getAllProjects();
      setState(() {
        _projects = projects;
        _result = 'Loaded ${projects.length} projects';
      });
    } catch (e) {
      setState(() {
        _result = 'Error loading projects: $e';
      });
    }
  }

  Future<void> _createTestProject() async {
    try {
      final project = Project(
        name: 'Test Project ${DateTime.now().millisecondsSinceEpoch}',
        clientName: 'Test Client',
        location: 'Test Location',
        currency: 'SAR',
        exchangeRate: 3.75,
      );

      // Add a test system
      project.systems.add(
        SystemEstimate(
          name: 'Test System',
          type: 'Fire Alarm',
        ),
      );

      // Add a test material
      project.systems.first.materials.add(
        MaterialItem(
          name: 'Test Material',
          category: 'Initiating Devices',
          quantity: 10,
          unit: 'Each',
          exWorksUnitCost: 100,
          localUnitCost: 50,
        ),
      );

      // Add a test equipment
      project.systems.first.equipment.add(
        EquipmentItem(
          name: 'Test Equipment',
          category: 'Control Equipment',
          quantity: 5,
          exWorksUnitCost: 200,
          localUnitCost: 75,
        ),
      );

      // Add a test labor
      project.systems.first.labor.add(
        LaborItem(
          description: 'Test Labor',
          hours: 20,
          hourlyRate: 50,
        ),
      );

      final projectId = await _databaseService.insertProject(project);
      setState(() {
        _result = 'Created project with ID: $projectId';
      });
      await _loadProjects();
    } catch (e) {
      setState(() {
        _result = 'Error creating project: $e';
      });
    }
  }

  Future<void> _loadProject(String id) async {
    try {
      final project = await _databaseService.getProject(id);
      setState(() {
        _currentProject = project;
        _result = 'Loaded project: ${project.name}';
      });
    } catch (e) {
      setState(() {
        _result = 'Error loading project: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Result: $_result', style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _createTestProject,
              child: const Text('Create Test Project'),
            ),
            const SizedBox(height: 16),
            const Text('Projects:', style: TextStyle(fontWeight: FontWeight.bold)),
            Expanded(
              child: ListView.builder(
                itemCount: _projects.length,
                itemBuilder: (context, index) {
                  final project = _projects[index];
                  return ListTile(
                    title: Text(project.name),
                    subtitle: Text('Systems: ${project.systems.length}'),
                    onTap: () => _loadProject(project.id),
                  );
                },
              ),
            ),
            if (_currentProject != null) ...[
              const SizedBox(height: 16),
              const Text('Current Project:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('Name: ${_currentProject!.name}'),
              Text('Client: ${_currentProject!.clientName}'),
              Text('Location: ${_currentProject!.location}'),
              Text('Currency: ${_currentProject!.currency}'),
              Text('Exchange Rate: ${_currentProject!.exchangeRate}'),
              Text('Systems: ${_currentProject!.systems.length}'),
              if (_currentProject!.systems.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text('First System: ${_currentProject!.systems.first.name}'),
                Text('Materials: ${_currentProject!.systems.first.materials.length}'),
                Text('Equipment: ${_currentProject!.systems.first.equipment.length}'),
                Text('Labor: ${_currentProject!.systems.first.labor.length}'),
              ],
            ],
          ],
        ),
      ),
    );
  }
}
