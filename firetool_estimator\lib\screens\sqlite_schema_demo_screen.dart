import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../services/sqlite_schema_manager.dart';
import '../constants/app_constants.dart';

class SQLiteSchemaDemoScreen extends StatefulWidget {
  const SQLiteSchemaDemoScreen({super.key});

  @override
  State<SQLiteSchemaDemoScreen> createState() => _SQLiteSchemaDemoScreenState();
}

class _SQLiteSchemaDemoScreenState extends State<SQLiteSchemaDemoScreen> {
  late Future<Database> _dbFuture;
  List<Map<String, dynamic>> _tableData = [];
  List<String> _columns = [];
  bool _isLoading = false;
  String? _message;
  final String _tableName = 'demo_table';

  @override
  void initState() {
    super.initState();
    _dbFuture = _initDatabase();
    _loadData();
  }

  Future<Database> _initDatabase() async {
    // Get the database path
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'schema_demo.db');

    // Open the database
    return openDatabase(
      path,
      version: 1,
      onCreate: (Database db, int version) async {
        // Create a simple table
        await db.execute('''
          CREATE TABLE $_tableName (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            age INTEGER
          )
        ''');

        // Insert some sample data
        await db.insert(_tableName, {'name': 'John Doe', 'age': 30});
        await db.insert(_tableName, {'name': 'Jane Smith', 'age': 25});
      },
    );
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _message = null;
    });

    try {
      final db = await _dbFuture;

      // Get table info
      final tableInfo = await db.rawQuery('PRAGMA table_info($_tableName)');

      // Extract column names
      _columns = tableInfo.map((col) => col['name'] as String).toList();

      // Get all data
      final data = await db.query(_tableName);

      setState(() {
        _tableData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _message = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _addColumn() async {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController typeController = TextEditingController(text: 'TEXT');

    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (BuildContext dialogContext) => AlertDialog(
        title: const Text('Add Column'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Column Name',
                hintText: 'Enter a name for the column',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: typeController,
              decoration: const InputDecoration(
                labelText: 'Column Type',
                hintText: 'TEXT, INTEGER, REAL, etc.',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(dialogContext).showSnackBar(
                  const SnackBar(content: Text('Please enter a column name')),
                );
                return;
              }

              Navigator.of(dialogContext).pop({
                'name': nameController.text.trim(),
                'type': typeController.text.trim(),
              });
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );

    if (result != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final db = await _dbFuture;

        // Add the column
        final success = await SQLiteSchemaManager.addColumn(
          db,
          _tableName,
          result['name']!,
          result['type']!,
        );

        if (success) {
          setState(() {
            _message = 'Column ${result['name']} added successfully';
          });

          // Reload data
          await _loadData();
        } else {
          setState(() {
            _message = 'Failed to add column';
            _isLoading = false;
          });
        }
      } catch (e) {
        setState(() {
          _message = 'Error adding column: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateCell(int rowId, String columnName) async {
    // Get current value
    final currentValue = _tableData.firstWhere((row) => row['id'] == rowId)[columnName];

    final TextEditingController valueController = TextEditingController(
      text: currentValue?.toString() ?? '',
    );

    final result = await showDialog<String>(
      context: context,
      builder: (BuildContext dialogContext) => AlertDialog(
        title: Text('Edit $columnName'),
        content: TextField(
          controller: valueController,
          decoration: InputDecoration(
            labelText: columnName,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(dialogContext).pop(valueController.text),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final db = await _dbFuture;

        // Update the cell
        final rowsUpdated = await SQLiteSchemaManager.updateCell(
          db,
          _tableName,
          columnName,
          result,
          'id = ?',
          [rowId],
        );

        setState(() {
          _message = 'Updated $rowsUpdated row(s)';
        });

        // Reload data
        await _loadData();
      } catch (e) {
        setState(() {
          _message = 'Error updating cell: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _dropColumn(String columnName) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to drop the column "$columnName"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () => Navigator.of(dialogContext).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final db = await _dbFuture;

        // Drop the column
        final success = await SQLiteSchemaManager.dropColumn(
          db,
          _tableName,
          columnName,
        );

        if (success) {
          setState(() {
            _message = 'Column $columnName dropped successfully';
          });

          // Reload data
          await _loadData();
        } else {
          setState(() {
            _message = 'Failed to drop column';
            _isLoading = false;
          });
        }
      } catch (e) {
        setState(() {
          _message = 'Error dropping column: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _importData() async {
    try {
      final db = await _dbFuture;

      // Import data
      final rowsImported = await SQLiteSchemaManager.importFromFile(
        db,
        _tableName,
      );

      setState(() {
        _message = 'Imported $rowsImported row(s)';
      });

      // Reload data
      await _loadData();
    } catch (e) {
      setState(() {
        _message = 'Error importing data: $e';
      });
    }
  }

  Future<void> _exportData() async {
    try {
      final db = await _dbFuture;

      // Export data
      final rowsExported = await SQLiteSchemaManager.exportToFile(
        db,
        _tableName,
      );

      setState(() {
        _message = 'Exported $rowsExported row(s)';
      });
    } catch (e) {
      setState(() {
        _message = 'Error exporting data: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SQLite Schema Demo'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Reload Data',
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Message
                if (_message != null)
                  Container(
                    padding: const EdgeInsets.all(8),
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.info, color: Colors.blue),
                        const SizedBox(width: 8),
                        Expanded(child: Text(_message!)),
                        IconButton(
                          icon: const Icon(Icons.close, size: 16),
                          onPressed: () => setState(() => _message = null),
                        ),
                      ],
                    ),
                  ),

                // Toolbar
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      ElevatedButton.icon(
                        icon: const Icon(Icons.add),
                        label: const Text('Add Column'),
                        onPressed: _addColumn,
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.upload_file),
                        label: const Text('Import'),
                        onPressed: _importData,
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.download),
                        label: const Text('Export'),
                        onPressed: _exportData,
                      ),
                    ],
                  ),
                ),

                // Table
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: SingleChildScrollView(
                      child: DataTable(
                        columns: [
                          for (final column in _columns)
                            DataColumn(
                              label: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(column),
                                  if (column != 'id')
                                    IconButton(
                                      icon: const Icon(Icons.delete, size: 16),
                                      onPressed: () => _dropColumn(column),
                                      tooltip: 'Drop Column',
                                    ),
                                ],
                              ),
                            ),
                        ],
                        rows: [
                          for (final row in _tableData)
                            DataRow(
                              cells: [
                                for (final column in _columns)
                                  DataCell(
                                    Text(row[column]?.toString() ?? ''),
                                    onTap: () => _updateCell(row['id'], column),
                                  ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
