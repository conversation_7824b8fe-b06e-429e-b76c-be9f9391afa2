import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/local_sql_service.dart';

class DatabaseDebugScreen extends StatefulWidget {
  const DatabaseDebugScreen({super.key});

  @override
  State<DatabaseDebugScreen> createState() => _DatabaseDebugScreenState();
}

class _DatabaseDebugScreenState extends State<DatabaseDebugScreen> {
  Map<String, dynamic>? _databaseInfo;
  bool _isLoading = true;
  String? _error;
  final TextEditingController _tableNameController = TextEditingController();
  final TextEditingController _columnNameController = TextEditingController();
  final TextEditingController _columnTypeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadDatabaseInfo();
  }

  @override
  void dispose() {
    _tableNameController.dispose();
    _columnNameController.dispose();
    _columnTypeController.dispose();
    super.dispose();
  }

  Future<void> _loadDatabaseInfo() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final sqlService = Provider.of<LocalSqlService>(context, listen: false);
      final info = await sqlService.getDatabaseInfo();

      setState(() {
        _databaseInfo = info;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading database info: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _addColumnToTable() async {
    final tableName = _tableNameController.text.trim();
    final columnName = _columnNameController.text.trim();
    final columnType = _columnTypeController.text.trim().toUpperCase();

    if (tableName.isEmpty || columnName.isEmpty || columnType.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Validate column type
    final validTypes = ['TEXT', 'INTEGER', 'REAL', 'BLOB'];
    if (!validTypes.contains(columnType)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Invalid column type. Use one of: ${validTypes.join(", ")}'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final sqlService = Provider.of<LocalSqlService>(context, listen: false);
      await sqlService.addColumnToTable(tableName, columnName, columnType);

      // Reload database info
      await _loadDatabaseInfo();

      // Clear form
      _tableNameController.clear();
      _columnNameController.clear();
      _columnTypeController.clear();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Column added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding column: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Debug'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDatabaseInfo,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Text(
                    'Error: $_error',
                    style: const TextStyle(color: Colors.red),
                  ),
                )
              : Scrollbar(
                  thickness: 8.0,
                  radius: const Radius.circular(4.0),
                  thumbVisibility: true,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                      // Add column form
                      Card(
                        margin: const EdgeInsets.only(bottom: 16.0),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Add Column to Table',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              TextField(
                                controller: _tableNameController,
                                decoration: const InputDecoration(
                                  labelText: 'Table Name',
                                  border: OutlineInputBorder(),
                                  hintText: 'e.g., alarm, water, foam',
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: _columnNameController,
                                decoration: const InputDecoration(
                                  labelText: 'Column Name',
                                  border: OutlineInputBorder(),
                                  hintText: 'e.g., new_column',
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: _columnTypeController,
                                decoration: const InputDecoration(
                                  labelText: 'Column Type',
                                  border: OutlineInputBorder(),
                                  hintText: 'TEXT, INTEGER, REAL, or BLOB',
                                ),
                              ),
                              const SizedBox(height: 16),
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: _addColumnToTable,
                                  child: const Text('Add Column'),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Database info
                      const Text(
                        'Database Tables',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Tables list
                      if (_databaseInfo?['tables'] != null)
                        ..._buildTablesList(),

                      const SizedBox(height: 16),

                      // Dynamic columns
                      if (_databaseInfo?['dynamicColumns'] != null) ...[
                        const Text(
                          'Dynamic Columns',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        _buildDynamicColumnsTable(),
                      ],
                    ],
                  ),
                ),
              ),
    );
  }

  List<Widget> _buildTablesList() {
    final tables = _databaseInfo!['tables'] as List;
    final tableColumns = _databaseInfo!['tableColumns'] as Map<String, dynamic>;

    return tables.map<Widget>((tableName) {
      final columns = tableColumns[tableName] as List;

      return Card(
        margin: const EdgeInsets.only(bottom: 8.0),
        child: ExpansionTile(
          title: Text(
            tableName.toString(),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Table(
                border: TableBorder.all(color: Colors.grey.shade300),
                columnWidths: const {
                  0: FlexColumnWidth(1),
                  1: FlexColumnWidth(2),
                  2: FlexColumnWidth(1),
                },
                children: [
                  const TableRow(
                    decoration: BoxDecoration(color: Colors.grey),
                    children: [
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text('CID', style: TextStyle(fontWeight: FontWeight.bold)),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text('Name', style: TextStyle(fontWeight: FontWeight.bold)),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text('Type', style: TextStyle(fontWeight: FontWeight.bold)),
                      ),
                    ],
                  ),
                  ...columns.map<TableRow>((column) {
                    return TableRow(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(column['cid'].toString()),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(column['name'].toString()),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(column['type'].toString()),
                        ),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildDynamicColumnsTable() {
    final dynamicColumns = _databaseInfo!['dynamicColumns'] as List;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Table(
          border: TableBorder.all(color: Colors.grey.shade300),
          columnWidths: const {
            0: FlexColumnWidth(2),
            1: FlexColumnWidth(2),
            2: FlexColumnWidth(1),
            3: FlexColumnWidth(2),
          },
          children: [
            const TableRow(
              decoration: BoxDecoration(color: Colors.grey),
              children: [
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Table', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Column', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Type', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Created At', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
              ],
            ),
            ...dynamicColumns.map<TableRow>((column) {
              return TableRow(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(column['table_name'].toString()),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(column['column_name'].toString()),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(column['column_type'].toString()),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(column['created_at'].toString()),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }
}
