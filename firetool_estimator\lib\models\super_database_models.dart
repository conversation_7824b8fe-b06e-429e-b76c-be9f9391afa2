
/// Represents a section in the SuperDatabase (e.g., Fire Alarm, Firefighting, etc.)
class Section {
  final String id;
  final String name;
  final String displayName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? description;
  final String? iconName;
  final String? color;

  Section({
    required this.id,
    required this.name,
    required this.displayName,
    required this.createdAt,
    required this.updatedAt,
    this.description,
    this.iconName,
    this.color,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'displayName': displayName,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'description': description,
        'iconName': iconName,
        'color': color,
      };

  factory Section.fromJson(Map<String, dynamic> json) => Section(
        id: json['id'],
        name: json['name'],
        displayName: json['displayName'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        description: json['description'],
        iconName: json['iconName'],
        color: json['color'],
      );

  Section copyWith({
    String? id,
    String? name,
    String? displayName,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? description,
    String? iconName,
    String? color,
  }) =>
      Section(
        id: id ?? this.id,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        description: description ?? this.description,
        iconName: iconName ?? this.iconName,
        color: color ?? this.color,
      );
}

/// Represents a subsection (table) within a section
class Subsection {
  final String id;
  final String sectionId;
  final String name;
  final String displayName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? description;
  final List<ColumnDefinition> columns;
  final int rowCount;

  Subsection({
    required this.id,
    required this.sectionId,
    required this.name,
    required this.displayName,
    required this.createdAt,
    required this.updatedAt,
    this.description,
    required this.columns,
    this.rowCount = 0,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'sectionId': sectionId,
        'name': name,
        'displayName': displayName,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'description': description,
        'columns': columns.map((c) => c.toJson()).toList(),
        'rowCount': rowCount,
      };

  factory Subsection.fromJson(Map<String, dynamic> json) => Subsection(
        id: json['id'],
        sectionId: json['sectionId'],
        name: json['name'],
        displayName: json['displayName'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        description: json['description'],
        columns: (json['columns'] as List)
            .map((c) => ColumnDefinition.fromJson(c))
            .toList(),
        rowCount: json['rowCount'] ?? 0,
      );

  Subsection copyWith({
    String? id,
    String? sectionId,
    String? name,
    String? displayName,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? description,
    List<ColumnDefinition>? columns,
    int? rowCount,
  }) =>
      Subsection(
        id: id ?? this.id,
        sectionId: sectionId ?? this.sectionId,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        description: description ?? this.description,
        columns: columns ?? this.columns,
        rowCount: rowCount ?? this.rowCount,
      );
}

/// Represents a column definition in a table
class ColumnDefinition {
  final String name;
  final String displayName;
  final ColumnType type;
  final bool isRequired;
  final bool isUnique;
  final String? defaultValue;
  final String? description;
  final Map<String, dynamic>? metadata;

  ColumnDefinition({
    required this.name,
    required this.displayName,
    required this.type,
    this.isRequired = false,
    this.isUnique = false,
    this.defaultValue,
    this.description,
    this.metadata,
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'displayName': displayName,
        'type': type.name,
        'isRequired': isRequired,
        'isUnique': isUnique,
        'defaultValue': defaultValue,
        'description': description,
        'metadata': metadata,
      };

  factory ColumnDefinition.fromJson(Map<String, dynamic> json) =>
      ColumnDefinition(
        name: json['name'],
        displayName: json['displayName'],
        type: ColumnType.values.firstWhere((e) => e.name == json['type']),
        isRequired: json['isRequired'] ?? false,
        isUnique: json['isUnique'] ?? false,
        defaultValue: json['defaultValue'],
        description: json['description'],
        metadata: json['metadata'],
      );

  ColumnDefinition copyWith({
    String? name,
    String? displayName,
    ColumnType? type,
    bool? isRequired,
    bool? isUnique,
    String? defaultValue,
    String? description,
    Map<String, dynamic>? metadata,
  }) =>
      ColumnDefinition(
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        type: type ?? this.type,
        isRequired: isRequired ?? this.isRequired,
        isUnique: isUnique ?? this.isUnique,
        defaultValue: defaultValue ?? this.defaultValue,
        description: description ?? this.description,
        metadata: metadata ?? this.metadata,
      );
}

/// Supported column types
enum ColumnType {
  text,
  integer,
  real,
  boolean,
  date,
  datetime,
  currency,
  email,
  url,
  phone,
  json;

  String get sqliteType {
    switch (this) {
      case ColumnType.text:
      case ColumnType.email:
      case ColumnType.url:
      case ColumnType.phone:
      case ColumnType.json:
        return 'TEXT';
      case ColumnType.integer:
      case ColumnType.boolean:
        return 'INTEGER';
      case ColumnType.real:
      case ColumnType.currency:
        return 'REAL';
      case ColumnType.date:
      case ColumnType.datetime:
        return 'TEXT'; // Store as ISO string
    }
  }

  String get displayName {
    switch (this) {
      case ColumnType.text:
        return 'Text';
      case ColumnType.integer:
        return 'Number (Integer)';
      case ColumnType.real:
        return 'Number (Decimal)';
      case ColumnType.boolean:
        return 'Yes/No';
      case ColumnType.date:
        return 'Date';
      case ColumnType.datetime:
        return 'Date & Time';
      case ColumnType.currency:
        return 'Currency';
      case ColumnType.email:
        return 'Email';
      case ColumnType.url:
        return 'URL';
      case ColumnType.phone:
        return 'Phone';
      case ColumnType.json:
        return 'JSON';
    }
  }
}

/// Import/Export operation status
class ImportExportOperation {
  final String id;
  final String type; // 'import' or 'export'
  final String sectionId;
  final String? subsectionId;
  final String filePath;
  final DateTime startTime;
  final DateTime? endTime;
  final OperationStatus status;
  final int? totalRows;
  final int? processedRows;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;

  ImportExportOperation({
    required this.id,
    required this.type,
    required this.sectionId,
    this.subsectionId,
    required this.filePath,
    required this.startTime,
    this.endTime,
    required this.status,
    this.totalRows,
    this.processedRows,
    this.errorMessage,
    this.metadata,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type,
        'sectionId': sectionId,
        'subsectionId': subsectionId,
        'filePath': filePath,
        'startTime': startTime.toIso8601String(),
        'endTime': endTime?.toIso8601String(),
        'status': status.name,
        'totalRows': totalRows,
        'processedRows': processedRows,
        'errorMessage': errorMessage,
        'metadata': metadata,
      };

  factory ImportExportOperation.fromJson(Map<String, dynamic> json) =>
      ImportExportOperation(
        id: json['id'],
        type: json['type'],
        sectionId: json['sectionId'],
        subsectionId: json['subsectionId'],
        filePath: json['filePath'],
        startTime: DateTime.parse(json['startTime']),
        endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
        status: OperationStatus.values.firstWhere((e) => e.name == json['status']),
        totalRows: json['totalRows'],
        processedRows: json['processedRows'],
        errorMessage: json['errorMessage'],
        metadata: json['metadata'],
      );
}

enum OperationStatus {
  pending,
  inProgress,
  completed,
  failed,
  cancelled;
}
