import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class ExcelSpreadsheetGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;
  final Map<String, List<String>>? dropdownOptions;

  const ExcelSpreadsheetGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
    this.dropdownOptions,
  });

  @override
  State<ExcelSpreadsheetGrid> createState() => _ExcelSpreadsheetGridState();
}

class _ExcelSpreadsheetGridState extends State<ExcelSpreadsheetGrid> {
  late FirebaseDataSource _dataSource;
  final DataGridController _dataGridController = DataGridController();

  List<GridColumn> _columns = [];
  bool _isLoading = true;
  String? _error;

  // For adding new columns
  final TextEditingController _newColumnController = TextEditingController();
  bool _isAddingColumn = false;

  // For adding new rows
  bool _isAddingRow = false;
  final Map<String, TextEditingController> _newRowControllers = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _dataGridController.dispose();
    _newColumnController.dispose();
    for (var controller in _newRowControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final items = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Determine columns
      final Set<String> columnSet = {'id'};

      // Add predefined columns if provided
      if (widget.predefinedColumns != null) {
        columnSet.addAll(widget.predefinedColumns!);
      }

      // Add any additional columns from the data
      for (var item in items) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Create grid columns
      final gridColumns = columnSet.map((column) =>
        GridColumn(
          columnName: column,
          label: Container(
            padding: const EdgeInsets.all(8.0),
            alignment: Alignment.centerLeft,
            child: Text(
              _getDisplayName(column),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          allowEditing: column != 'id', // Don't allow editing ID column
          width: column == 'id' ? 120 : 150, // Make ID column narrower
        )
      ).toList();

      // Initialize controllers for new row
      for (var column in columnSet) {
        if (column != 'id' && !_newRowControllers.containsKey(column)) {
          _newRowControllers[column] = TextEditingController();
        }
      }

      // Create data source
      _dataSource = FirebaseDataSource(
        items: items,
        columns: columnSet.toList(),
        collectionPath: widget.collectionPath,
      );

      setState(() {
        _columns = gridColumns;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // Helper methods
  String _getDisplayName(String column) {
    // Convert snake_case to Title Case
    return column.split('_').map((word) =>
      word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  String _getColumnKey(String displayName) {
    // Convert Title Case to snake_case
    return displayName.toLowerCase().replaceAll(' ', '_');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // CRUD operations
  Future<void> _addNewRow() async {
    try {
      // Create an empty row with default values
      final Map<String, dynamic> newItem = {};

      // Add default empty values for each column
      for (var column in _columns) {
        if (column.columnName != 'id') {
          newItem[column.columnName] = '';
        }
      }

      newItem['createdAt'] = FieldValue.serverTimestamp();

      // Add to Firestore
      final docRef = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Reload data
      await _loadData();

      // Find the newly added row
      final newRowIndex = _dataSource.items.indexWhere((item) => item['id'] == docRef.id);

      if (newRowIndex >= 0) {
        // Select the first editable cell in the new row
        final firstEditableColumnIndex = _columns.indexWhere((column) => column.columnName != 'id');
        if (firstEditableColumnIndex >= 0) {
          // Wait for the UI to update
          Future.delayed(const Duration(milliseconds: 100), () {
            // Begin editing the first cell in the new row
            _dataGridController.beginEdit(
              RowColumnIndex(newRowIndex + 1, firstEditableColumnIndex)
            );

            // Scroll to the new row
            _dataGridController.scrollToRow(newRowIndex.toDouble());
          });
        }
      }

      _showSnackBar('Row added successfully. You can now edit it.');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  Future<void> _addNewColumn() async {
    if (_newColumnController.text.isEmpty) {
      _showSnackBar('Column name cannot be empty', isError: true);
      return;
    }

    try {
      final String columnKey = _getColumnKey(_newColumnController.text);
      final String displayName = _newColumnController.text.trim();

      // Check if column already exists
      if (_columns.any((col) => col.columnName == columnKey)) {
        _showSnackBar('Column already exists', isError: true);
        return;
      }

      // Add the column to all existing items
      final batch = FirebaseFirestore.instance.batch();

      for (var item in _dataSource.items) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(item['id'] as String);

        batch.update(docRef, {columnKey: ''});
      }

      await batch.commit();

      // Add controller for new column
      _newRowControllers[columnKey] = TextEditingController();

      // Create a new column for the grid
      final newColumn = GridColumn(
        columnName: columnKey,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.centerLeft,
          child: Text(
            displayName,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
        width: 150,
      );

      // Add the column to the grid
      setState(() {
        _columns.add(newColumn);
        _isAddingColumn = false;
      });

      _newColumnController.clear();

      // Reload data to refresh the grid
      await _loadData();
      _showSnackBar('Column added successfully');
    } catch (e) {
      _showSnackBar('Error adding column: $e', isError: true);
    }
  }

  Future<void> _deleteSelectedRows() async {
    final selectedRows = _dataGridController.selectedRows;
    if (selectedRows.isEmpty) {
      _showSnackBar('No rows selected', isError: true);
      return;
    }

    try {
      final batch = FirebaseFirestore.instance.batch();

      for (var row in selectedRows) {
        // No need to cast, it's already a DataGridRow
        final cells = row.getCells();
        if (cells.isNotEmpty) {
          final data = cells[0].value as Map<String, dynamic>;
          final id = data['id'] as String;

          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(id);

          batch.delete(docRef);
        }
      }

      await batch.commit();
      await _loadData();
      _showSnackBar('${selectedRows.length} rows deleted');
    } catch (e) {
      _showSnackBar('Error deleting rows: $e', isError: true);
    }
  }

  // Import/Export functions
  Future<void> _importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
      );

      if (result == null || result.files.isEmpty) return;

      final file = File(result.files.first.path!);

      // Check if it's a CSV file
      if (result.files.first.extension?.toLowerCase() == 'csv') {
        await _importFromCSV(file);
        return;
      }

      // For Excel files, use a completely different approach to avoid the numFmtId error
      try {
        // Use the clipboard as an intermediate step
        _showSnackBar('Processing Excel file...', isError: false);

        // Process the Excel file

        // Read the file as text if possible
        String fileContent = '';
        try {
          fileContent = await file.readAsString();
        } catch (e) {
          // If we can't read as text, show a message to the user
          _showSnackBar('Please try using "Import from Clipboard" instead. Copy your Excel data and paste it.', isError: true);
          return;
        }

        // Parse the content line by line
        final lines = fileContent.split('\n');
        if (lines.isEmpty) {
          _showSnackBar('No data found in file', isError: true);
          return;
        }

        // Get headers from first line
        final headers = lines[0].split(',').map((h) => h.trim().replaceAll('"', '')).toList();

        if (headers.isEmpty) {
          _showSnackBar('No headers found in file', isError: true);
          return;
        }

        // Process data rows
        final batch = FirebaseFirestore.instance.batch();
        int count = 0;

        for (var i = 1; i < lines.length; i++) {
          if (lines[i].trim().isEmpty) continue;

          final values = lines[i].split(',').map((v) => v.trim().replaceAll('"', '')).toList();
          final Map<String, dynamic> item = {};

          for (var j = 0; j < values.length && j < headers.length; j++) {
            if (values[j].isNotEmpty) {
              item[_getColumnKey(headers[j])] = values[j];
            }
          }

          if (item.isNotEmpty) {
            item['createdAt'] = FieldValue.serverTimestamp();
            final docRef = FirebaseFirestore.instance
                .collection(widget.collectionPath)
                .doc();
            batch.set(docRef, item);
            count++;
          }
        }

        await batch.commit();
        await _loadData();
        _showSnackBar('Imported $count items from Excel');
      } catch (excelError) {
        // If that fails, show a message to use clipboard import
        _showSnackBar('Error processing Excel file: $excelError', isError: true);
        _showSnackBar('Please try using "Import from Clipboard" instead. Copy your Excel data and paste it.', isError: false);
      }
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
    }
  }

  Future<void> _importFromCSV(File file) async {
    try {
      final content = await file.readAsString();
      final lines = content.split('\n');

      if (lines.isEmpty) {
        _showSnackBar('CSV file is empty', isError: true);
        return;
      }

      // Get headers from first line
      final headers = lines[0].split(',').map((h) => h.trim().replaceAll('"', '')).toList();

      if (headers.isEmpty) {
        _showSnackBar('No headers found in CSV file', isError: true);
        return;
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      int count = 0;

      for (var i = 1; i < lines.length; i++) {
        if (lines[i].trim().isEmpty) continue;

        final values = lines[i].split(',').map((v) => v.trim().replaceAll('"', '')).toList();
        final Map<String, dynamic> item = {};

        for (var j = 0; j < values.length && j < headers.length; j++) {
          if (values[j].isNotEmpty) {
            item[_getColumnKey(headers[j])] = values[j];
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc();
          batch.set(docRef, item);
          count++;
        }
      }

      await batch.commit();
      await _loadData();
      _showSnackBar('Imported $count items from CSV');
    } catch (e) {
      _showSnackBar('Error importing from CSV: $e', isError: true);
    }
  }



  Future<void> _importFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final text = clipboardData?.text;

      if (text == null || text.isEmpty) {
        _showSnackBar('Clipboard is empty', isError: true);
        return;
      }

      final rows = text.split('\n');
      if (rows.isEmpty) {
        _showSnackBar('No data found in clipboard', isError: true);
        return;
      }

      // Get headers from first row
      final headers = rows[0].split('\t');

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      int count = 0;

      for (var i = 1; i < rows.length; i++) {
        if (rows[i].trim().isEmpty) continue;

        final values = rows[i].split('\t');
        if (values.isEmpty) continue;

        final Map<String, dynamic> item = {};

        for (var j = 0; j < values.length && j < headers.length; j++) {
          if (values[j].trim().isNotEmpty) {
            item[_getColumnKey(headers[j].trim())] = values[j].trim();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc();
          batch.set(docRef, item);
          count++;
        }
      }

      await batch.commit();
      await _loadData();
      _showSnackBar('Imported $count items from clipboard');
    } catch (e) {
      _showSnackBar('Error importing from clipboard: $e', isError: true);
    }
  }

  Future<void> _exportToExcel() async {
    try {
      // Create a new Excel workbook
      final xlsio.Workbook workbook = xlsio.Workbook();
      final xlsio.Worksheet sheet = workbook.worksheets[0];
      sheet.name = widget.title;

      // Add headers
      for (var i = 0; i < _columns.length; i++) {
        sheet.getRangeByIndex(1, i + 1).setText(_getDisplayName(_columns[i].columnName));

        // Apply header style
        sheet.getRangeByIndex(1, i + 1).cellStyle.bold = true;
        sheet.getRangeByIndex(1, i + 1).cellStyle.backColor = '#D9EAD3';
      }

      // Add data
      for (var i = 0; i < _dataSource.items.length; i++) {
        final item = _dataSource.items[i];
        for (var j = 0; j < _columns.length; j++) {
          final column = _columns[j].columnName;
          final value = item[column]?.toString() ?? '';
          sheet.getRangeByIndex(i + 2, j + 1).setText(value);
        }
      }

      // Auto-fit columns
      for (var i = 1; i <= _columns.length; i++) {
        sheet.autoFitColumn(i);
      }

      // Save file
      final List<int> bytes = workbook.saveAsStream();
      workbook.dispose();

      // Get file path
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/${widget.title.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final file = File(path);
      await file.writeAsBytes(bytes);

      _showSnackBar('Exported to $path');
    } catch (e) {
      _showSnackBar('Error exporting to Excel: $e', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: widget.themeColor,
        ),
      );
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    return Column(
      children: [
        _buildToolbar(),
        if (_isAddingRow) _buildAddRowForm(),
        Expanded(
          child: SfDataGridTheme(
            data: SfDataGridThemeData(
              headerColor: widget.themeColor.withAlpha(25),
              selectionColor: widget.themeColor.withAlpha(50),
              gridLineColor: Colors.grey.shade300,
            ),
            child: SfDataGrid(
              source: _dataSource,
              columns: _columns,
              controller: _dataGridController,
              allowEditing: true,
              allowSorting: true,
              allowFiltering: true,
              allowSwiping: true,
              allowMultiColumnSorting: true,
              allowTriStateSorting: true,
              selectionMode: SelectionMode.multiple,
              navigationMode: GridNavigationMode.cell,
              editingGestureType: EditingGestureType.tap, // Change to single tap for Excel-like behavior
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              showCheckboxColumn: true,
              frozenColumnsCount: 1, // Freeze ID column
              onCellTap: (details) {
                // Start editing immediately on cell tap (Excel-like behavior)
                if (details.rowColumnIndex.rowIndex > 0 && details.column.columnName != 'id') {
                  _dataGridController.beginEdit(details.rowColumnIndex);
                }
              },
              onQueryRowHeight: (details) {
                // Return the height for the row
                return 45.0;
              },
              onSelectionChanged: (List<DataGridRow> addedRows, List<DataGridRow> removedRows) {
                // Handle selection changes
                setState(() {
                  // Update UI if needed
                });
              },
              // Enable keyboard navigation
              isScrollbarAlwaysShown: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.add),
                label: const Text('Add Row'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    _isAddingRow = !_isAddingRow;
                  });
                },
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.add_box),
                label: const Text('Add Column'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    _isAddingColumn = true;
                  });
                },
              ),
              if (_isAddingColumn) ...[
                const SizedBox(width: 8),
                SizedBox(
                  width: 200,
                  child: TextField(
                    controller: _newColumnController,
                    decoration: const InputDecoration(
                      hintText: 'Column Name',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onSubmitted: (_) => _addNewColumn(),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.check),
                  onPressed: _addNewColumn,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _isAddingColumn = false;
                      _newColumnController.clear();
                    });
                  },
                ),
              ],
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.delete),
                label: const Text('Delete Selected'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                onPressed: _deleteSelectedRows,
              ),
              const Spacer(),
              ElevatedButton.icon(
                icon: const Icon(Icons.upload_file),
                label: const Text('Import Excel'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _importFromExcel,
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.paste),
                label: const Text('Import from Clipboard'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _importFromClipboard,
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.download),
                label: const Text('Export Excel'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _exportToExcel,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddRowForm() {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Add New Row',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _isAddingRow = false;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16.0,
              runSpacing: 16.0,
              children: _columns.map((column) {
                if (column.columnName == 'id') return const SizedBox.shrink();

                final controller = _newRowControllers[column.columnName] ?? TextEditingController();

                // Check if this field has dropdown options
                final dropdownOptions = widget.dropdownOptions?[column.columnName];

                if (dropdownOptions != null) {
                  return SizedBox(
                    width: 200,
                    child: DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: _getDisplayName(column.columnName),
                        border: const OutlineInputBorder(),
                      ),
                      items: dropdownOptions.map((option) {
                        return DropdownMenuItem<String>(
                          value: option,
                          child: Text(option),
                        );
                      }).toList(),
                      onChanged: (value) {
                        controller.text = value ?? '';
                      },
                    ),
                  );
                }

                return SizedBox(
                  width: 200,
                  child: TextField(
                    controller: controller,
                    decoration: InputDecoration(
                      labelText: _getDisplayName(column.columnName),
                      border: const OutlineInputBorder(),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _addNewRow,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }
}

// DataSource for the SfDataGrid
class FirebaseDataSource extends DataGridSource {
  final List<Map<String, dynamic>> items;
  final List<String> columns;
  final String collectionPath;
  List<DataGridRow> _dataGridRows = [];

  FirebaseDataSource({
    required this.items,
    required this.columns,
    required this.collectionPath,
  }) {
    _dataGridRows = _generateDataGridRows();
  }

  List<DataGridRow> _generateDataGridRows() {
    return items.map<DataGridRow>((item) {
      return DataGridRow(
        cells: columns.map<DataGridCell>((column) {
          return DataGridCell<Map<String, dynamic>>(
            columnName: column,
            value: {'id': item['id'], 'value': item[column]},
          );
        }).toList(),
      );
    }).toList();
  }

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  DataGridRowAdapter? buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((dataGridCell) {
        final data = dataGridCell.value as Map<String, dynamic>;
        final value = data['value']?.toString() ?? '';

        return Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(8.0),
          child: Text(
            value,
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
    );
  }

  // Custom method to handle cell updates
  Future<void> handleCellSubmit(DataGridRow row, GridColumn column, dynamic value) async {
    final rowIndex = _dataGridRows.indexOf(row);
    if (rowIndex == -1) return;

    final columnIndex = columns.indexOf(column.columnName);
    if (columnIndex == -1) return;

    final cell = row.getCells()[columnIndex];
    final data = cell.value as Map<String, dynamic>;
    final id = data['id'] as String;

    // Don't update ID column
    if (column.columnName == 'id') return;

    try {
      // Update in Firestore
      await FirebaseFirestore.instance
          .collection(collectionPath)
          .doc(id)
          .update({column.columnName: value});

      // Update local data
      final itemIndex = items.indexWhere((item) => item['id'] == id);
      if (itemIndex != -1) {
        items[itemIndex][column.columnName] = value;

        // Update cell value
        final updatedCell = DataGridCell<Map<String, dynamic>>(
          columnName: column.columnName,
          value: {'id': id, 'value': value},
        );

        row.getCells()[columnIndex] = updatedCell;

        // Notify listeners
        notifyListeners();
      }
    } catch (e) {
      // Use a better approach than print for error handling
      debugPrint('Error updating cell: $e');
    }
  }

  @override
  Future<void> onCellSubmit(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) async {
    final newValue = newCellValue;

    await handleCellSubmit(dataGridRow, column, newValue);

    // Reset the new cell value
    newCellValue = null;
  }

  @override
  bool onCellBeginEdit(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) {
    final cell = dataGridRow.getCells()[rowColumnIndex.columnIndex];
    final data = cell.value as Map<String, dynamic>;
    final value = data['value'];

    // Set the current value for editing
    currentCellValue = value;

    // Allow editing
    return true;
  }

  // Properties for cell editing
  dynamic currentCellValue;
  dynamic newCellValue;
}
