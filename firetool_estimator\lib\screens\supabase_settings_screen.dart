import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/supabase_sync_service.dart';

class SupabaseSettingsScreen extends StatefulWidget {
  const SupabaseSettingsScreen({Key? key}) : super(key: key);

  @override
  State<SupabaseSettingsScreen> createState() => _SupabaseSettingsScreenState();
}

class _SupabaseSettingsScreenState extends State<SupabaseSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _urlController = TextEditingController();
  final _anonKeyController = TextEditingController();

  final _supabaseService = SupabaseSyncService();
  bool _isLoading = false;
  bool _isConnected = false;
  String? _connectionStatus;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _checkConnection();
  }

  @override
  void dispose() {
    _urlController.dispose();
    _anonKeyController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _urlController.text = prefs.getString('supabase_url') ?? '';
      _anonKeyController.text = prefs.getString('supabase_anon_key') ?? '';
    });
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('supabase_url', _urlController.text.trim());
      await prefs.setString('supabase_anon_key', _anonKeyController.text.trim());

      _showSuccessSnackBar('Settings saved successfully');
    } catch (e) {
      _showErrorSnackBar('Failed to save settings: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testConnection() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _connectionStatus = 'Testing connection...';
    });

    try {
      await _supabaseService.initialize(
        url: _urlController.text.trim(),
        anonKey: _anonKeyController.text.trim(),
      );

      final status = await _supabaseService.getSyncStatus();

      setState(() {
        _isConnected = status == SyncStatus.connected;
        _connectionStatus = _isConnected
            ? 'Connected successfully!'
            : 'Connection failed. Please check your credentials.';
      });

      if (_isConnected) {
        _showSuccessSnackBar('Connection successful!');
      } else {
        _showErrorSnackBar('Connection failed. Please check your credentials.');
      }
    } catch (e) {
      setState(() {
        _isConnected = false;
        _connectionStatus = 'Connection failed: $e';
      });
      _showErrorSnackBar('Connection failed: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkConnection() async {
    try {
      final status = await _supabaseService.getSyncStatus();
      setState(() {
        _isConnected = status == SyncStatus.connected;
        _connectionStatus = _isConnected
            ? 'Connected'
            : 'Not connected';
      });
    } catch (e) {
      setState(() {
        _isConnected = false;
        _connectionStatus = 'Not configured';
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Supabase Settings'),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
            tooltip: 'Help',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Connection status card
              Card(
                color: _isConnected ? Colors.green.shade50 : Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        _isConnected ? Icons.cloud_done : Icons.cloud_off,
                        color: _isConnected ? Colors.green : Colors.red,
                        size: 32,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Connection Status',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _connectionStatus ?? 'Unknown',
                              style: TextStyle(
                                color: _isConnected ? Colors.green.shade700 : Colors.red.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (!_isLoading)
                        IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: _checkConnection,
                          tooltip: 'Refresh status',
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Configuration form
              Text(
                'Configuration',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Supabase URL
              TextFormField(
                controller: _urlController,
                decoration: const InputDecoration(
                  labelText: 'Supabase URL *',
                  hintText: 'https://your-project.supabase.co',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.link),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Supabase URL is required';
                  }
                  if (Uri.tryParse(value.trim())?.hasAbsolutePath != true) {
                    return 'Please enter a valid URL';
                  }
                  return null;
                },
                keyboardType: TextInputType.url,
              ),

              const SizedBox(height: 16),

              // Anon Key
              TextFormField(
                controller: _anonKeyController,
                decoration: const InputDecoration(
                  labelText: 'Anonymous Key *',
                  hintText: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.key),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Anonymous key is required';
                  }
                  if (value.trim().length < 50) {
                    return 'Anonymous key seems too short';
                  }
                  return null;
                },
                maxLines: 3,
                obscureText: true,
              ),

              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _saveSettings,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.save),
                      label: const Text('Save Settings'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _isLoading ? null : _testConnection,
                      icon: const Icon(Icons.wifi_protected_setup),
                      label: const Text('Test Connection'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // Information section
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'About Supabase Sync',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        'Supabase sync allows you to backup and synchronize your SuperDatabase data to the cloud. '
                        'This enables data sharing across devices and provides a backup solution.\n\n'
                        'To get started:\n'
                        '1. Create a free account at supabase.com\n'
                        '2. Create a new project\n'
                        '3. Copy your project URL and anonymous key\n'
                        '4. Enter them above and test the connection',
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supabase Setup Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'How to get your Supabase credentials:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text('1. Go to supabase.com and create an account'),
              Text('2. Create a new project'),
              Text('3. Go to Settings > API'),
              Text('4. Copy the "URL" and "anon public" key'),
              Text('5. Paste them in the form above'),
              SizedBox(height: 16),
              Text(
                'Security Note:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                'The anonymous key is safe to use in client applications. '
                'It only allows access to data that your Row Level Security policies permit.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}
