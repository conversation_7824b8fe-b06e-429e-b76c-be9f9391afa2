import 'package:flutter/foundation.dart';
import '../models/project.dart';
import 'database_service.dart';

class ProjectProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();

  Project? _currentProject;
  List<Project> _projects = [];
  bool _isLoading = false;
  String? _error;

  // Always return true for admin access
  bool get isAdmin => true;

  // Update auth service reference
  void updateAuth(dynamic auth) {
    // No-op for now
  }

  // Getters
  Project? get currentProject => _currentProject;
  List<Project> get projects => _projects;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize provider
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await _loadProjects();
    } catch (e) {
      print('Error in initialize: $e');
      _setError('Failed to initialize: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load all projects
  Future<void> _loadProjects() async {
    try {
      _projects = await _databaseService.getAllProjects();
      print('Loaded ${_projects.length} projects');
      notifyListeners();
    } catch (e) {
      print('Error in _loadProjects: $e');
      _setError('Failed to load projects: ${e.toString()}');
    }
  }

  // Create a new project
  Future<void> createProject({
    required String name,
    String clientName = '',
    String? currency,
    double? exchangeRate,
  }) async {
    _setLoading(true);
    try {
      final newProject = Project(
        name: name,
        clientName: clientName,
        currency: currency ?? 'SAR',
        exchangeRate: exchangeRate ?? 3.75,
      );

      await _databaseService.insertProject(newProject);
      _currentProject = newProject;
      await _loadProjects();
    } catch (e) {
      _setError('Failed to create project: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load a project
  Future<void> loadProject(String projectId) async {
    _setLoading(true);
    try {
      _currentProject = await _databaseService.getProject(projectId);
      print('Loaded project: ${_currentProject?.name}, systems: ${_currentProject?.systems.length}');

      // Debug: Print systems details
      if (_currentProject != null && _currentProject!.systems.isNotEmpty) {
        for (var i = 0; i < _currentProject!.systems.length; i++) {
          final system = _currentProject!.systems[i];
          print('System $i: ${system.name}, materials: ${system.materials.length}, equipment: ${system.equipment.length}, services: ${system.services.length}');
        }
      }

      notifyListeners();
    } catch (e) {
      print('Error in loadProject: $e');
      _setError('Failed to load project: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Save current project
  Future<void> saveCurrentProject() async {
    if (_currentProject == null) {
      _setError('No project to save');
      return;
    }

    _setLoading(true);
    try {
      _currentProject!.updatedAt = DateTime.now();
      await _databaseService.updateProject(_currentProject!);
      await _loadProjects();
    } catch (e) {
      _setError('Failed to save project: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Delete a project
  Future<void> deleteProject(String projectId) async {
    _setLoading(true);
    try {
      await _databaseService.deleteProject(projectId);
      if (_currentProject?.id == projectId) {
        _currentProject = null;
      }
      await _loadProjects();
    } catch (e) {
      _setError('Failed to delete project: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Add a system to the current project
  void addSystem(SystemEstimate system) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    _currentProject!.systems.add(system);
    notifyListeners();
  }

  // Update a system in the current project
  void updateSystem(SystemEstimate updatedSystem) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final index = _currentProject!.systems.indexWhere((s) => s.id == updatedSystem.id);
    if (index != -1) {
      _currentProject!.systems[index] = updatedSystem;
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // Remove a system from the current project
  void removeSystem(String systemId) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    _currentProject!.systems.removeWhere((s) => s.id == systemId);
    notifyListeners();
  }

  // Add a material to a system
  void addMaterial(String systemId, MaterialItem material) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].materials.add(material);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // Update a material in a system
  void updateMaterial(String systemId, MaterialItem updatedMaterial) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      final materialIndex = _currentProject!.systems[systemIndex].materials
          .indexWhere((m) => m.id == updatedMaterial.id);

      if (materialIndex != -1) {
        _currentProject!.systems[systemIndex].materials[materialIndex] = updatedMaterial;
        notifyListeners();
      } else {
        _setError('Material not found');
      }
    } else {
      _setError('System not found');
    }
  }

  // Remove a material from a system
  void removeMaterial(String systemId, String materialId) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].materials.removeWhere((m) => m.id == materialId);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // Equipment CRUD operations
  void addEquipment(String systemId, EquipmentItem equipment) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].equipment.add(equipment);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  void updateEquipment(String systemId, EquipmentItem updatedEquipment) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      final equipmentIndex = _currentProject!.systems[systemIndex].equipment
          .indexWhere((e) => e.id == updatedEquipment.id);

      if (equipmentIndex != -1) {
        _currentProject!.systems[systemIndex].equipment[equipmentIndex] = updatedEquipment;
        notifyListeners();
      } else {
        _setError('Equipment not found');
      }
    } else {
      _setError('System not found');
    }
  }

  void removeEquipment(String systemId, String equipmentId) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].equipment.removeWhere((e) => e.id == equipmentId);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // Service CRUD operations
  void addService(String systemId, ServiceItem service) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].services.add(service);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  void updateService(String systemId, ServiceItem updatedService) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      final serviceIndex = _currentProject!.systems[systemIndex].services
          .indexWhere((s) => s.id == updatedService.id);

      if (serviceIndex != -1) {
        _currentProject!.systems[systemIndex].services[serviceIndex] = updatedService;
        notifyListeners();
      } else {
        _setError('Service item not found');
      }
    } else {
      _setError('System not found');
    }
  }

  void removeService(String systemId, String serviceId) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].services.removeWhere((s) => s.id == serviceId);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // For backward compatibility
  void addLabor(String systemId, LaborItem labor) {
    addService(systemId, labor);
  }

  void updateLabor(String systemId, LaborItem updatedLabor) {
    updateService(systemId, updatedLabor);
  }

  void removeLabor(String systemId, String laborId) {
    removeService(systemId, laborId);
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? errorMessage) {
    _error = errorMessage;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
