import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../constants/app_constants.dart';

class TrueExcelGrid extends StatefulWidget {
  final String collectionPath;

  const TrueExcelGrid({
    super.key,
    required this.collectionPath,
  });

  @override
  State<TrueExcelGrid> createState() => _TrueExcelGridState();
}

class _TrueExcelGridState extends State<TrueExcelGrid> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  List<Map<String, dynamic>> _items = [];
  Set<String> _allColumns = {};
  List<String> _visibleColumns = [];
  bool _isLoading = true;
  String? _error;

  // For cell selection and editing
  final Map<String, Map<String, bool>> _selectedCells = {}; // itemId -> {column -> isSelected}
  final TextEditingController _bulkEditController = TextEditingController();
  bool _isBulkEditing = false;

  // For adding new columns
  final TextEditingController _newColumnController = TextEditingController();

  // For adding new rows
  final Map<String, TextEditingController> _newRowControllers = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _bulkEditController.dispose();
    _newColumnController.dispose();

    for (var controller in _newRowControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final items = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Extract all possible columns from all items
      final Set<String> allColumns = {'id'};
      for (var item in items) {
        allColumns.addAll(item.keys);
      }

      // Initialize controllers for new row
      for (var column in allColumns) {
        if (column != 'id') {
          _newRowControllers[column] = TextEditingController();
        }
      }

      setState(() {
        _items = items;
        _allColumns = allColumns;
        _visibleColumns = allColumns.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _toggleCellSelection(String itemId, String column) {
    setState(() {
      if (!_selectedCells.containsKey(itemId)) {
        _selectedCells[itemId] = {};
      }

      _selectedCells[itemId]![column] = !(_selectedCells[itemId]![column] ?? false);

      // If no cells are selected anymore, clear the map
      bool anySelected = false;
      for (var item in _selectedCells.values) {
        for (var selected in item.values) {
          if (selected) {
            anySelected = true;
            break;
          }
        }
        if (anySelected) break;
      }

      if (!anySelected) {
        _selectedCells.clear();
      }
    });
  }

  void _startBulkEdit() {
    setState(() {
      _isBulkEditing = true;
      _bulkEditController.clear();
    });
  }

  Future<void> _applyBulkEdit() async {
    final newValue = _bulkEditController.text;

    // Apply to all selected cells
    for (var itemId in _selectedCells.keys) {
      final Map<String, dynamic> updates = {};

      for (var column in _selectedCells[itemId]!.keys) {
        if (_selectedCells[itemId]![column] == true && column != 'id') {
          updates[column] = newValue;
        }
      }

      if (updates.isNotEmpty) {
        try {
          await FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(itemId)
              .update(updates);
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error updating item: $e')),
          );
        }
      }
    }

    // Reload data and clear selection
    await _loadData();
    setState(() {
      _selectedCells.clear();
      _isBulkEditing = false;
    });
  }

  void _cancelBulkEdit() {
    setState(() {
      _isBulkEditing = false;
      _selectedCells.clear();
    });
  }

  void _addColumn() {
    final newColumnName = _newColumnController.text.trim();
    if (newColumnName.isEmpty) return;

    setState(() {
      _allColumns.add(newColumnName);
      _visibleColumns.add(newColumnName);
      _newColumnController.clear();

      // Add controller for new column in new row form
      _newRowControllers[newColumnName] = TextEditingController();
    });
  }

  void _removeColumn(String column) {
    if (column == 'id') return; // Can't remove ID column

    setState(() {
      _visibleColumns.remove(column);
    });
  }

  Future<void> _addRow() async {
    final Map<String, dynamic> newItem = {};

    // Collect values from controllers
    for (var entry in _newRowControllers.entries) {
      if (entry.value.text.isNotEmpty) {
        newItem[entry.key] = entry.value.text;
      }
    }

    if (newItem.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill at least one field')),
      );
      return;
    }

    // Add timestamp
    newItem['createdAt'] = FieldValue.serverTimestamp();

    try {
      // Add to Firestore
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Clear controllers
      for (var controller in _newRowControllers.values) {
        controller.clear();
      }

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Row added successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding row: $e')),
      );
    }
  }

  Future<void> _deleteRow(String id) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .delete();

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Row deleted')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting row: $e')),
      );
    }
  }

  Future<void> _updateCell(String id, String column, String value) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .update({column: value});

      // Update local data
      setState(() {
        final itemIndex = _items.indexWhere((item) => item['id'] == id);
        if (itemIndex >= 0) {
          _items[itemIndex][column] = value;
        }
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating cell: $e')),
      );
    }
  }

  Future<void> _importFromClipboard() async {
    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data == null || data.text == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No data in clipboard')),
        );
        return;
      }

      // Parse clipboard data (assuming tab-separated values from Excel)
      final rows = data.text!.split('\n');
      if (rows.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No rows found in clipboard data')),
        );
        return;
      }

      // Parse header row
      final headers = rows[0].split('\t');

      // Add new columns if needed
      for (var header in headers) {
        final trimmedHeader = header.trim();
        if (trimmedHeader.isNotEmpty && !_allColumns.contains(trimmedHeader)) {
          _allColumns.add(trimmedHeader);
          _visibleColumns.add(trimmedHeader);
          _newRowControllers[trimmedHeader] = TextEditingController();
        }
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      int count = 0;

      for (int i = 1; i < rows.length; i++) {
        if (rows[i].trim().isEmpty) continue;

        final values = rows[i].split('\t');
        if (values.length != headers.length) continue;

        final Map<String, dynamic> item = {};
        for (int j = 0; j < headers.length; j++) {
          if (headers[j].trim().isNotEmpty && values[j].trim().isNotEmpty) {
            item[headers[j].trim()] = values[j].trim();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance.collection(widget.collectionPath).doc();
          batch.set(docRef, item);
          count++;
        }
      }

      await batch.commit();

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Imported $count items from clipboard')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error importing from clipboard: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    // Check if user is admin
    if (!authService.isAdmin) {
      return const Center(child: Text('Admin access required'));
    }

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      // Check if it's a permission error
      if (_error!.contains('permission-denied')) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.security, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'Firebase Permission Error',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Your Firebase security rules need to be updated to allow database access.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('Try Again'),
                ),
              ],
            ),
          ),
        );
      }
      return Center(child: Text(_error!, style: const TextStyle(color: Colors.red)));
    }

    return Column(
      children: [
        // Toolbar
        Container(
          padding: const EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Main toolbar
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: _loadData,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Refresh'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _importFromClipboard,
                    icon: const Icon(Icons.paste),
                    label: const Text('Import from Excel'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _addRow,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Row'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const Spacer(),
                  if (_selectedCells.isNotEmpty && !_isBulkEditing)
                    ElevatedButton.icon(
                      onPressed: _startBulkEdit,
                      icon: const Icon(Icons.edit),
                      label: const Text('Edit Selected'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                    ),
                  if (_isBulkEditing) ...[
                    Expanded(
                      child: TextField(
                        controller: _bulkEditController,
                        decoration: const InputDecoration(
                          hintText: 'Enter value for selected cells',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        autofocus: true,
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: _applyBulkEdit,
                      icon: const Icon(Icons.check, color: Colors.green),
                      tooltip: 'Apply',
                    ),
                    IconButton(
                      onPressed: _cancelBulkEdit,
                      icon: const Icon(Icons.close, color: Colors.red),
                      tooltip: 'Cancel',
                    ),
                  ],
                  if (!_isBulkEditing)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppConstants.primaryColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${_items.length} rows',
                        style: TextStyle(
                          color: AppConstants.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),

              // Column management
              const SizedBox(height: 12),
              Row(
                children: [
                  const Text('Add Column:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(width: 8),
                  SizedBox(
                    width: 200,
                    child: TextField(
                      controller: _newColumnController,
                      decoration: const InputDecoration(
                        hintText: 'New column name',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      onSubmitted: (_) => _addColumn(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: _addColumn,
                    icon: const Icon(Icons.add_circle, color: Colors.green),
                    tooltip: 'Add Column',
                  ),
                  const SizedBox(width: 16),
                  const Text('Visible Columns:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(width: 8),
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: _allColumns.map((column) {
                          final isVisible = _visibleColumns.contains(column);
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text(column),
                              selected: isVisible,
                              onSelected: (selected) {
                                setState(() {
                                  if (selected) {
                                    _visibleColumns.add(column);
                                  } else if (column != 'id') { // Can't hide ID column
                                    _visibleColumns.remove(column);
                                  }
                                });
                              },
                              backgroundColor: Colors.grey.shade200,
                              selectedColor: AppConstants.primaryColor.withAlpha(100),
                              checkmarkColor: AppConstants.primaryColor,
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Help text for new users
        if (_items.isEmpty)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue),
                    SizedBox(width: 8),
                    Text(
                      'Excel-like Data Grid',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'How to use:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                const Text('• Click on cells to select them (click again to deselect)'),
                const Text('• Select multiple cells and click "Edit Selected" to bulk edit'),
                const Text('• Add/remove columns using the controls above'),
                const Text('• Double-click on a cell to edit it individually'),
                const Text('• Import data directly from Excel using the "Import from Excel" button'),
              ],
            ),
          ),

        // Excel-like data grid
        Expanded(
          child: Card(
            margin: const EdgeInsets.all(8.0),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // New row form
                Container(
                  padding: const EdgeInsets.all(8.0),
                  color: Colors.grey.shade100,
                  child: Row(
                    children: [
                      Container(
                        width: 50,
                        alignment: Alignment.center,
                        child: IconButton(
                          icon: const Icon(Icons.add_circle, color: Colors.green),
                          onPressed: _addRow,
                          tooltip: 'Add Row',
                        ),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: _visibleColumns
                                .where((col) => col != 'id')
                                .map((column) {
                              return Container(
                                width: 150,
                                padding: const EdgeInsets.symmetric(horizontal: 4),
                                child: TextField(
                                  controller: _newRowControllers[column],
                                  decoration: InputDecoration(
                                    labelText: column,
                                    border: const OutlineInputBorder(),
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 8,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Table header
                Container(
                  color: const Color(0xFFE3F2FD),
                  child: Row(
                    children: [
                      // Action column
                      Container(
                        width: 50,
                        padding: const EdgeInsets.all(8),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: const Text(
                          'Actions',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      // Data columns
                      ...List.generate(_visibleColumns.length, (index) {
                        final column = _visibleColumns[index];
                        return Container(
                          width: 150,
                          padding: const EdgeInsets.all(8),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Text(
                                  column,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              if (column != 'id')
                                IconButton(
                                  icon: const Icon(Icons.close, size: 16),
                                  onPressed: () => _removeColumn(column),
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                  tooltip: 'Remove Column',
                                ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),

                // Table body
                Expanded(
                  child: Scrollbar(
                    controller: _verticalController,
                    thumbVisibility: true,
                    child: Scrollbar(
                      controller: _horizontalController,
                      thumbVisibility: true,
                      notificationPredicate: (notification) => notification.depth == 1,
                      child: SingleChildScrollView(
                        controller: _verticalController,
                        child: SingleChildScrollView(
                          controller: _horizontalController,
                          scrollDirection: Axis.horizontal,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: _items.map((item) {
                              return Row(
                                children: [
                                  // Action column
                                  Container(
                                    width: 50,
                                    height: 40,
                                    padding: const EdgeInsets.all(4),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey.shade300),
                                      color: Colors.grey.shade50,
                                    ),
                                    child: IconButton(
                                      icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                                      onPressed: () => _deleteRow(item['id']),
                                      tooltip: 'Delete Row',
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                    ),
                                  ),
                                  // Data columns
                                  ...List.generate(_visibleColumns.length, (index) {
                                    final column = _visibleColumns[index];
                                    final value = item[column]?.toString() ?? '';
                                    final isSelected = _selectedCells[item['id']]?[column] ?? false;

                                    return GestureDetector(
                                      onTap: () => _toggleCellSelection(item['id'], column),
                                      onDoubleTap: () {
                                        if (column != 'id') {
                                          // Show inline editor
                                          final TextEditingController controller = TextEditingController(text: value);
                                          showDialog(
                                            context: context,
                                            builder: (context) => AlertDialog(
                                              title: Text('Edit $column'),
                                              content: TextField(
                                                controller: controller,
                                                autofocus: true,
                                                decoration: const InputDecoration(
                                                  border: OutlineInputBorder(),
                                                ),
                                              ),
                                              actions: [
                                                TextButton(
                                                  onPressed: () => Navigator.of(context).pop(),
                                                  child: const Text('Cancel'),
                                                ),
                                                TextButton(
                                                  onPressed: () {
                                                    _updateCell(item['id'], column, controller.text);
                                                    Navigator.of(context).pop();
                                                  },
                                                  child: const Text('Save'),
                                                ),
                                              ],
                                            ),
                                          );
                                        }
                                      },
                                      child: Container(
                                        width: 150,
                                        height: 40,
                                        padding: const EdgeInsets.symmetric(horizontal: 8),
                                        alignment: Alignment.centerLeft,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: isSelected
                                                ? Colors.blue
                                                : Colors.grey.shade300,
                                            width: isSelected ? 2 : 1,
                                          ),
                                          color: isSelected
                                              ? Colors.blue.shade50
                                              : Colors.white,
                                        ),
                                        child: Text(
                                          value,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            color: column == 'id'
                                                ? Colors.grey
                                                : Colors.black,
                                            fontStyle: column == 'id'
                                                ? FontStyle.italic
                                                : FontStyle.normal,
                                          ),
                                        ),
                                      ),
                                    );
                                  }),
                                ],
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}