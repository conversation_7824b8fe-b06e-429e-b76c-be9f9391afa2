import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import '../models/super_database_models.dart';

class SuperDataGrid extends StatefulWidget {
  final List<Map<String, dynamic>> data;
  final List<ColumnDefinition> columns;
  final Function(String column, bool ascending)? onSort;
  final Function(int rowIndex, String columnName, dynamic newValue)? onCellEdit;

  const SuperDataGrid({
    super.key,
    required this.data,
    required this.columns,
    this.onSort,
    this.onCellEdit,
  });

  @override
  State<SuperDataGrid> createState() => _SuperDataGridState();
}

class _SuperDataGridState extends State<SuperDataGrid> {
  late SuperDataGridSource _dataSource;
  final DataGridController _dataGridController = DataGridController();

  @override
  void initState() {
    super.initState();
    _dataSource = SuperDataGridSource(
      data: widget.data,
      columns: widget.columns,
      onCellEdit: widget.onCellEdit,
    );
  }

  @override
  void didUpdateWidget(SuperDataGrid oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data || oldWidget.columns != widget.columns) {
      _dataSource = SuperDataGridSource(
        data: widget.data,
        columns: widget.columns,
        onCellEdit: widget.onCellEdit,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: SfDataGrid(
          source: _dataSource,
          controller: _dataGridController,
          allowSorting: true,
          selectionMode: SelectionMode.single,
          columnWidthMode: ColumnWidthMode.auto,
          gridLinesVisibility: GridLinesVisibility.both,
          headerGridLinesVisibility: GridLinesVisibility.both,
          headerRowHeight: 56.0,
          rowHeight: 48.0,
          columns: _buildGridColumns(),
        ),
      ),
    );
  }

  List<GridColumn> _buildGridColumns() {
    return widget.columns.map((column) {
      return GridColumn(
        columnName: column.name,
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            border: Border(
              right: BorderSide(color: Colors.grey.shade300),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  column.displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                _getColumnTypeIcon(column.type),
                size: 16,
                color: Colors.grey.shade600,
              ),
            ],
          ),
        ),
        columnWidthMode: _getColumnWidthMode(column.type),
        allowSorting: true,
      );
    }).toList();
  }

  IconData _getColumnTypeIcon(ColumnType type) {
    switch (type) {
      case ColumnType.text:
        return Icons.text_fields;
      case ColumnType.integer:
        return Icons.numbers;
      case ColumnType.real:
        return Icons.numbers;
      case ColumnType.boolean:
        return Icons.check_box;
      case ColumnType.date:
        return Icons.calendar_today;
      case ColumnType.datetime:
        return Icons.access_time;
      case ColumnType.currency:
        return Icons.attach_money;
      case ColumnType.email:
        return Icons.email;
      case ColumnType.url:
        return Icons.link;
      case ColumnType.phone:
        return Icons.phone;
      case ColumnType.json:
        return Icons.code;
    }
  }

  ColumnWidthMode _getColumnWidthMode(ColumnType type) {
    switch (type) {
      case ColumnType.integer:
        return ColumnWidthMode.fitByColumnName;
      case ColumnType.boolean:
        return ColumnWidthMode.fitByColumnName;
      case ColumnType.date:
        return ColumnWidthMode.fitByColumnName;
      case ColumnType.currency:
        return ColumnWidthMode.fitByColumnName;
      default:
        return ColumnWidthMode.auto;
    }
  }

  @override
  void dispose() {
    _dataGridController.dispose();
    super.dispose();
  }
}

class SuperDataGridSource extends DataGridSource {
  final List<Map<String, dynamic>> data;
  final List<ColumnDefinition> columns;
  final Function(int rowIndex, String columnName, dynamic newValue)? onCellEdit;

  SuperDataGridSource({
    required this.data,
    required this.columns,
    this.onCellEdit,
  }) {
    _buildDataGridRows();
  }

  List<DataGridRow> _dataGridRows = [];

  void _buildDataGridRows() {
    _dataGridRows = data.map<DataGridRow>((row) {
      return DataGridRow(
        cells: columns.map<DataGridCell>((column) {
          final value = row[column.name];
          return DataGridCell<dynamic>(
            columnName: column.name,
            value: _formatCellValue(value, column.type),
          );
        }).toList(),
      );
    }).toList();
  }

  dynamic _formatCellValue(dynamic value, ColumnType type) {
    if (value == null) return '';

    switch (type) {
      case ColumnType.boolean:
        if (value is int) {
          return value == 1 ? 'Yes' : 'No';
        } else if (value is bool) {
          return value ? 'Yes' : 'No';
        }
        return value.toString().toLowerCase() == 'true' ? 'Yes' : 'No';
      case ColumnType.date:
      case ColumnType.datetime:
        if (value is String) {
          final date = DateTime.tryParse(value);
          if (date != null) {
            if (type == ColumnType.date) {
              return '${date.day}/${date.month}/${date.year}';
            } else {
              return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
            }
          }
        }
        return value.toString();
      case ColumnType.currency:
        if (value is num) {
          return '\$${value.toStringAsFixed(2)}';
        }
        return value.toString();
      default:
        return value.toString();
    }
  }

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((cell) {
        final columnName = cell.columnName;
        final column = columns.firstWhere((col) => col.name == columnName);

        return Container(
          alignment: _getCellAlignment(column.type),
          padding: const EdgeInsets.all(8.0),
          child: _buildCellWidget(cell.value, column),
        );
      }).toList(),
    );
  }

  Widget _buildCellWidget(dynamic value, ColumnDefinition column) {
    final displayValue = value?.toString() ?? '';

    switch (column.type) {
      case ColumnType.boolean:
        final boolValue = _parseBoolValue(value);
        return Icon(
          boolValue ? Icons.check_circle : Icons.cancel,
          color: boolValue ? Colors.green : Colors.red,
          size: 20,
        );
      case ColumnType.url:
        return Text(
          displayValue,
          style: const TextStyle(
            color: Colors.blue,
            decoration: TextDecoration.underline,
          ),
          overflow: TextOverflow.ellipsis,
        );
      case ColumnType.email:
        return Text(
          displayValue,
          style: const TextStyle(color: Colors.blue),
          overflow: TextOverflow.ellipsis,
        );
      case ColumnType.currency:
        return Text(
          displayValue,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.green,
          ),
          overflow: TextOverflow.ellipsis,
        );
      default:
        return Text(
          displayValue,
          overflow: TextOverflow.ellipsis,
        );
    }
  }

  bool _parseBoolValue(dynamic value) {
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) {
      return value.toLowerCase() == 'true' || value.toLowerCase() == 'yes' || value == '1';
    }
    return false;
  }

  Alignment _getCellAlignment(ColumnType type) {
    switch (type) {
      case ColumnType.integer:
      case ColumnType.real:
      case ColumnType.currency:
        return Alignment.centerRight;
      case ColumnType.boolean:
        return Alignment.center;
      default:
        return Alignment.centerLeft;
    }
  }


}
