import 'package:flutter/material.dart';
import '../widgets/simple_excel_grid.dart';

class SimpleExcelGridFactory {
  static Widget createScreen(String systemType) {
    switch (systemType) {
      case 'alarm':
        return SimpleExcelGrid(
          collectionPath: 'fire_alarm',
          title: 'Fire Alarm Systems',
          themeColor: Colors.red.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'water':
        return SimpleExcelGrid(
          collectionPath: 'water_systems',
          title: 'Water Systems',
          themeColor: Colors.blue.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'foam':
        return SimpleExcelGrid(
          collectionPath: 'foam_systems',
          title: 'Foam Systems',
          themeColor: Colors.amber.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'fm200':
        return SimpleExcelGrid(
          collectionPath: 'clean_agent/fm200',
          title: 'FM200 Systems',
          themeColor: Colors.green.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'novec':
        return SimpleExcelGrid(
          collectionPath: 'clean_agent/novec',
          title: 'Novec Systems',
          themeColor: Colors.teal.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'co2':
        return SimpleExcelGrid(
          collectionPath: 'co2_systems',
          title: 'CO2 Systems',
          themeColor: Colors.purple.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      default:
        return SimpleExcelGrid(
          collectionPath: 'materials',
          title: 'Materials',
          themeColor: Colors.brown.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
    }
  }
}
