import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../constants/app_constants.dart';

class AdminDataGrid extends StatefulWidget {
  final String collectionPath;

  const AdminDataGrid({
    super.key,
    required this.collectionPath,
  });

  @override
  State<AdminDataGrid> createState() => _AdminDataGridState();
}

class _AdminDataGridState extends State<AdminDataGrid> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  List<Map<String, dynamic>> _items = [];
  List<String> _columns = [];
  bool _isLoading = true;
  String? _error;

  // For adding new items
  final Map<String, TextEditingController> _newItemControllers = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();

    // Dispose all text controllers
    for (var controller in _newItemControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final items = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Extract all possible columns from all items
      final Set<String> columns = {'id'};
      for (var item in items) {
        columns.addAll(item.keys);
      }

      // Create controllers for new item
      for (var column in columns) {
        if (column != 'id') {
          _newItemControllers[column] = TextEditingController();
        }
      }

      setState(() {
        _items = items;
        _columns = columns.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _addItem() async {
    try {
      final Map<String, dynamic> newItem = {};

      // Collect values from controllers
      for (var entry in _newItemControllers.entries) {
        if (entry.value.text.isNotEmpty) {
          newItem[entry.key] = entry.value.text;
        }
      }

      if (newItem.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please fill at least one field')),
        );
        return;
      }

      // Add timestamp
      newItem['createdAt'] = FieldValue.serverTimestamp();

      // Add to Firestore
      final docRef = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Clear controllers
      for (var controller in _newItemControllers.values) {
        controller.clear();
      }

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Item added with ID: ${docRef.id}')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding item: $e')),
      );
    }
  }

  Future<void> _updateItem(String id, String field, String value) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .update({field: value});

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Item updated')),
      );

      // Reload data
      await _loadData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating item: $e')),
      );
    }
  }

  Future<void> _deleteItem(String id) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .delete();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Item deleted')),
      );

      // Reload data
      await _loadData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting item: $e')),
      );
    }
  }

  Future<void> _importFromClipboard() async {
    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data == null || data.text == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No data in clipboard')),
        );
        return;
      }

      // Parse clipboard data (assuming tab-separated values from Excel)
      final rows = data.text!.split('\n');
      if (rows.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No rows found in clipboard data')),
        );
        return;
      }

      // Parse header row
      final headers = rows[0].split('\t');

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      int count = 0;

      for (int i = 1; i < rows.length; i++) {
        if (rows[i].trim().isEmpty) continue;

        final values = rows[i].split('\t');
        if (values.length != headers.length) continue;

        final Map<String, dynamic> item = {};
        for (int j = 0; j < headers.length; j++) {
          if (headers[j].trim().isNotEmpty && values[j].trim().isNotEmpty) {
            item[headers[j].trim()] = values[j].trim();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance.collection(widget.collectionPath).doc();
          batch.set(docRef, item);
          count++;
        }
      }

      await batch.commit();

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Imported $count items from clipboard')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error importing from clipboard: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    // Check if user is admin
    if (!authService.isAdmin) {
      return const Center(child: Text('Admin access required'));
    }

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      // Check if it's a permission error
      if (_error!.contains('permission-denied')) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.security, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'Firebase Permission Error',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Your Firebase security rules need to be updated to allow database access.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('Try Again'),
                ),
              ],
            ),
          ),
        );
      }
      return Center(child: Text(_error!, style: const TextStyle(color: Colors.red)));
    }

    return Column(
      children: [
        // Toolbar
        Container(
          padding: const EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              ElevatedButton.icon(
                onPressed: _loadData,
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _importFromClipboard,
                icon: const Icon(Icons.paste),
                label: const Text('Import from Clipboard'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AppConstants.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_items.length} items',
                  style: TextStyle(
                    color: AppConstants.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Help text for new users
        if (_items.isEmpty)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue),
                    SizedBox(width: 8),
                    Text(
                      'Getting Started',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'To add a new item:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                const Text('1. Fill in at least one field in the form below (e.g., name, description)'),
                const Text('2. Click the + button to add the item'),
                const SizedBox(height: 8),
                const Text(
                  'To import from Excel:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                const Text('1. Copy data from Excel (including headers)'),
                const Text('2. Click "Import from Clipboard" button'),
              ],
            ),
          ),

        // Data grid
        Expanded(
          child: Card(
            margin: const EdgeInsets.all(8.0),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Scrollbar(
              controller: _verticalController,
              thumbVisibility: true,
              child: Scrollbar(
                controller: _horizontalController,
                thumbVisibility: true,
                notificationPredicate: (notification) => notification.depth == 1,
                child: SingleChildScrollView(
                  controller: _verticalController,
                  child: SingleChildScrollView(
                    controller: _horizontalController,
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      headingRowColor: WidgetStateProperty.all(
                        const Color(0xFFE3F2FD),
                      ),
                      dataRowColor: WidgetStateProperty.all(
                        Colors.white,
                      ),
                      columns: [
                        const DataColumn(
                          label: Text(
                            'Actions',
                            style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
                          ),
                        ),
                        ..._columns.map((column) => DataColumn(
                          label: Text(
                            column,
                            style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
                          ),
                        )),
                      ],
                      rows: [
                        // New item row with better styling
                        DataRow(
                          color: WidgetStateProperty.all(const Color(0xFFF5F5F5)),
                          cells: [
                            DataCell(
                              Tooltip(
                                message: 'Add new item (fill at least one field first)',
                                child: IconButton(
                                  icon: const Icon(Icons.add_circle, color: Colors.green, size: 28),
                                  onPressed: _addItem,
                                ),
                              ),
                            ),
                            ..._columns.map((column) {
                              if (column == 'id') {
                                return const DataCell(Text('(auto)', style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey)));
                              }
                              return DataCell(
                                TextField(
                                  controller: _newItemControllers[column],
                                  decoration: InputDecoration(
                                    hintText: 'Enter ${column.replaceAll('_', ' ')}',
                                    border: InputBorder.none,
                                  ),
                                ),
                              );
                            }),
                          ],
                        ),
                        // Existing items
                        ..._items.map((item) {
                          return DataRow(
                            cells: [
                              DataCell(
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Tooltip(
                                      message: 'Delete item',
                                      child: IconButton(
                                        icon: const Icon(Icons.delete, color: Colors.red),
                                        onPressed: () => _deleteItem(item['id']),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ..._columns.map((column) {
                                final value = item[column]?.toString() ?? '';
                                if (column == 'id') {
                                  return DataCell(
                                    Text(
                                      value,
                                      style: const TextStyle(
                                        color: Colors.grey,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  );
                                }
                                return DataCell(
                                  TextField(
                                    controller: TextEditingController(text: value),
                                    decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Enter ${column.replaceAll('_', ' ')}',
                                      hintStyle: const TextStyle(
                                        fontStyle: FontStyle.italic,
                                        color: Colors.grey,
                                      ),
                                    ),
                                    onSubmitted: (newValue) {
                                      if (column != 'id' && newValue != value) {
                                        _updateItem(item['id'], column, newValue);
                                      }
                                    },
                                  ),
                                );
                              }),
                            ],
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
