import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class MockSyncfusionExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const MockSyncfusionExcelGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  });

  @override
  State<MockSyncfusionExcelGrid> createState() => _MockSyncfusionExcelGridState();
}

class _MockSyncfusionExcelGridState extends State<MockSyncfusionExcelGrid> {
  late ExcelDataSource _excelDataSource;
  List<GridColumn> _columns = [];
  List<Map<String, dynamic>> _items = [];
  bool _isLoading = true;
  String? _error;
  final DataGridController _dataGridController = DataGridController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Mock data based on collection path
      final loadedItems = _getMockData(widget.collectionPath);

      // Determine columns
      final Set<String> columnSet = {'id'};

      // Add predefined columns if provided
      if (widget.predefinedColumns != null) {
        columnSet.addAll(widget.predefinedColumns!);
      }

      // Add any additional columns from the data
      for (var item in loadedItems) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Create GridColumns
      final gridColumns = columnSet.map((field) {
        return GridColumn(
          columnName: field,
          label: Container(
            padding: const EdgeInsets.all(8.0),
            alignment: Alignment.center,
            color: Color.fromRGBO(
              widget.themeColor.red,
              widget.themeColor.green,
              widget.themeColor.blue,
              0.1,
            ),
            child: Text(
              _getDisplayName(field),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.themeColor,
              ),
            ),
          ),
        );
      }).toList();

      setState(() {
        _columns = gridColumns;
        _items = loadedItems;
        _excelDataSource = ExcelDataSource(
          items: _items,
          columns: columnSet.toList(),
          onCellSubmitted: _updateCell,
          onRowAdded: _addNewRow,
          onRowDeleted: _deleteRow,
        );
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // Helper methods
  String _getDisplayName(String column) {
    // Convert snake_case to Title Case
    return column.split('_').map((word) =>
      word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Mock data generator
  List<Map<String, dynamic>> _getMockData(String collectionPath) {
    // Generate different mock data based on collection path
    final List<Map<String, dynamic>> mockData = [];

    // Generate 10 items with appropriate fields
    for (int i = 1; i <= 10; i++) {
      final Map<String, dynamic> item = {
        'id': 'mock-$i',
        'model': 'Model $i',
        'description': 'Description for item $i',
        'manufacturer': 'Manufacturer ${i % 3 + 1}',
        'approval': 'UL Listed',
      };

      // Add pricing fields
      item['ex_works_price'] = '${i * 100}';
      item['local_price'] = '${i * 150}';
      item['installation_price'] = '${i * 50}';

      // Add any predefined columns
      if (widget.predefinedColumns != null) {
        for (var column in widget.predefinedColumns!) {
          if (!item.containsKey(column)) {
            item[column] = 'Value for $column';
          }
        }
      }

      mockData.add(item);
    }

    return mockData;
  }

  // CRUD operations
  Future<void> _updateCell(String itemId, String column, dynamic value) async {
    try {
      // Don't update ID column
      if (column == 'id') return;

      // Update local data
      setState(() {
        final itemIndex = _items.indexWhere((item) => item['id'] == itemId);
        if (itemIndex != -1) {
          _items[itemIndex][column] = value;
        }
      });

      _showSnackBar('Cell updated successfully');
    } catch (e) {
      _showSnackBar('Error updating cell: $e', isError: true);
    }
  }

  Future<void> _addNewRow() async {
    try {
      // Create an empty row with default values
      final Map<String, dynamic> newItem = {
        'id': 'mock-${_items.length + 1}',
      };

      // Add default empty values for each column
      for (var column in _columns) {
        if (column.columnName != 'id') {
          newItem[column.columnName] = '';
        }
      }

      // Add to local data
      setState(() {
        _items.add(newItem);
        _excelDataSource.items = _items;
        _excelDataSource.buildDataGridRows();
        _excelDataSource.notifyListeners();
      });

      _showSnackBar('Row added successfully');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  Future<void> _deleteRow(String itemId) async {
    try {
      // Delete from local data
      setState(() {
        _items.removeWhere((item) => item['id'] == itemId);
        _excelDataSource.items = _items;
        _excelDataSource.buildDataGridRows();
        _excelDataSource.notifyListeners();
      });

      _showSnackBar('Row deleted successfully');
    } catch (e) {
      _showSnackBar('Error deleting row: $e', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildToolbar(),
          Expanded(
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: widget.themeColor,
                    ),
                  )
                : _error != null
                    ? _buildErrorWidget()
                    : _buildDataGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.add),
                label: const Text('Add Row'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _addNewRow,
              ),
              const SizedBox(width: 8),
              const Spacer(),
              Text(
                'Firebase functionality disabled',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDataGrid() {
    return SfDataGrid(
      source: _excelDataSource,
      columns: _columns,
      controller: _dataGridController,
      allowEditing: true,
      navigationMode: GridNavigationMode.cell,
      selectionMode: SelectionMode.multiple,
      editingGestureType: EditingGestureType.tap,
      gridLinesVisibility: GridLinesVisibility.both,
      headerGridLinesVisibility: GridLinesVisibility.both,
    );
  }
}

class ExcelDataSource extends DataGridSource {
  List<Map<String, dynamic>> items;
  List<String> columns;
  List<DataGridRow> _dataGridRows = [];
  final Function(String, String, dynamic) onCellSubmitted;
  final Function() onRowAdded;
  final Function(String) onRowDeleted;

  ExcelDataSource({
    required this.items,
    required this.columns,
    required this.onCellSubmitted,
    required this.onRowAdded,
    required this.onRowDeleted,
  }) {
    buildDataGridRows();
  }

  void buildDataGridRows() {
    _dataGridRows = items.map<DataGridRow>((item) {
      return DataGridRow(
        cells: columns.map<DataGridCell>((column) {
          return DataGridCell<String>(
            columnName: column,
            value: item[column]?.toString() ?? '',
          );
        }).toList(),
      );
    }).toList();
  }

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((cell) {
        return Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(8.0),
          child: Text(
            cell.value.toString(),
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
    );
  }

  void updateCell(int rowIndex, int columnIndex, String value) {
    if (rowIndex < 0 || rowIndex >= items.length) return;
    if (columnIndex < 0 || columnIndex >= columns.length) return;

    final itemId = items[rowIndex]['id'] as String;
    final columnName = columns[columnIndex];

    // Don't update ID column
    if (columnName == 'id') return;

    // Update the item
    items[rowIndex][columnName] = value;

    // Update the cell value
    final cell = _dataGridRows[rowIndex].getCells()[columnIndex];
    _dataGridRows[rowIndex].getCells()[columnIndex] = DataGridCell(
      columnName: cell.columnName,
      value: value,
    );

    // Call the callback
    onCellSubmitted(itemId, columnName, value);
    notifyListeners();
  }

  @override
  bool onCellBeginEdit(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) {
    // Don't allow editing ID column
    return column.columnName != 'id';
  }
}
