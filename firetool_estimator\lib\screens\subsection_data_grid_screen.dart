import 'package:flutter/material.dart';
import '../models/super_database_models.dart';
import '../services/super_database_service.dart';
import '../services/csv_import_service.dart';
import '../widgets/enhanced_data_grid.dart';

class SubsectionDataGridScreen extends StatefulWidget {
  final Section section;
  final Subsection subsection;

  const SubsectionDataGridScreen({
    super.key,
    required this.section,
    required this.subsection,
  });

  @override
  State<SubsectionDataGridScreen> createState() => _SubsectionDataGridScreenState();
}

class _SubsectionDataGridScreenState extends State<SubsectionDataGridScreen> {
  final _superDbService = SuperDatabaseService();
  final _csvService = CsvImportService();

  List<Map<String, dynamic>> _data = [];
  bool _isLoading = true;
  String? _errorMessage;

  // Pagination
  int _currentPage = 1;
  final int _pageSize = 100;
  int _totalRows = 0;

  // Filtering and sorting
  String _searchQuery = '';
  String? _sortColumn;
  bool _sortAscending = true;
  final Map<String, String> _columnFilters = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final offset = (_currentPage - 1) * _pageSize;

      final rows = await _superDbService.getRows(
        sectionName: widget.section.name,
        tableName: widget.subsection.name,
        limit: _pageSize,
        offset: offset,
        sortByColumn: _sortColumn,
        sortAscending: _sortAscending,
        filterColumn: _searchQuery.isNotEmpty ? 'name' : null, // Simple search for now
        filterValue: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      // Get total count for pagination
      final allRows = await _superDbService.getRows(
        sectionName: widget.section.name,
        tableName: widget.subsection.name,
      );

      setState(() {
        _data = rows;
        _totalRows = allRows.length;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load data: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _addRow() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _AddRowDialog(subsection: widget.subsection),
    );

    if (result != null) {
      try {
        await _superDbService.insertRow(
          sectionName: widget.section.name,
          tableName: widget.subsection.name,
          data: result,
        );

        _showSuccessSnackBar('Row added successfully');
        await _loadData();
      } catch (e) {
        _showErrorSnackBar('Failed to add row: $e');
      }
    }
  }

  Future<void> _updateCell(int rowIndex, String columnName, dynamic newValue) async {
    try {
      if (rowIndex < _data.length) {
        final row = _data[rowIndex];
        final rowId = row['_id'];

        if (rowId != null) {
          await _superDbService.updateRow(
            sectionName: widget.section.name,
            tableName: widget.subsection.name,
            id: rowId,
            data: {columnName: newValue},
          );

          // Update local data
          setState(() {
            _data[rowIndex][columnName] = newValue;
          });

          _showSuccessSnackBar('Cell updated successfully');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Failed to update cell: $e');
    }
  }

  Future<void> _deleteRow(int rowIndex) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Row'),
        content: const Text('Are you sure you want to delete this row? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        if (rowIndex < _data.length) {
          final row = _data[rowIndex];
          final rowId = row['_id'];

          if (rowId != null) {
            await _superDbService.deleteRow(
              sectionName: widget.section.name,
              tableName: widget.subsection.name,
              id: rowId,
            );

            _showSuccessSnackBar('Row deleted successfully');
            await _loadData();
          }
        }
      } catch (e) {
        _showErrorSnackBar('Failed to delete row: $e');
      }
    }
  }

  Future<void> _importCsv() async {
    try {
      _showLoadingDialog('Importing CSV file...');

      final operation = await _csvService.importCsvAsSubsection(
        sectionId: widget.section.id,
        subsectionName: '${widget.subsection.displayName}_CSV_Import',
        strategy: ImportStrategy.addNew,
      );

      if (mounted) Navigator.of(context).pop(); // Close loading dialog

      if (operation.status == OperationStatus.completed) {
        _showSuccessSnackBar(
          'Successfully imported ${operation.processedRows} rows from CSV file'
        );
        await _loadData();
      } else {
        _showErrorSnackBar(
          'Import failed: ${operation.errorMessage ?? "Unknown error"}'
        );
      }
    } catch (e) {
      if (mounted) Navigator.of(context).pop(); // Close loading dialog
      _showErrorSnackBar('Import failed: $e');
    }
  }

  Future<void> _exportCsv() async {
    try {
      _showLoadingDialog('Exporting to CSV...');

      final operation = await _csvService.exportSubsectionToCsv(
        sectionId: widget.section.id,
        subsectionId: widget.subsection.id,
      );

      if (mounted) Navigator.of(context).pop(); // Close loading dialog

      if (operation.status == OperationStatus.completed) {
        _showSuccessSnackBar(
          'Successfully exported ${operation.processedRows} rows to CSV file'
        );
      } else {
        _showErrorSnackBar(
          'Export failed: ${operation.errorMessage ?? "Unknown error"}'
        );
      }
    } catch (e) {
      if (mounted) Navigator.of(context).pop(); // Close loading dialog
      _showErrorSnackBar('Export failed: $e');
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final color = _parseColor(widget.section.color) ?? Theme.of(context).primaryColor;

    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.section.displayName} - ${widget.subsection.displayName}'),
        backgroundColor: color,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.upload_file),
            onPressed: _importCsv,
            tooltip: 'Import CSV',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportCsv,
            tooltip: 'Export CSV',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addRow,
            tooltip: 'Add Row',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey.shade50,
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Search...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                      // Debounce search
                      Future.delayed(const Duration(milliseconds: 500), () {
                        if (_searchQuery == value) {
                          _currentPage = 1;
                          _loadData();
                        }
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'Total: $_totalRows rows',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Data grid
          Expanded(
            child: _buildDataGrid(),
          ),

          // Pagination
          if (_totalRows > _pageSize) _buildPagination(),
        ],
      ),
    );
  }

  Widget _buildDataGrid() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading data...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red.shade700),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_data.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.table_chart,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No data found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Add your first row to get started',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _addRow,
              icon: const Icon(Icons.add),
              label: const Text('Add Row'),
            ),
          ],
        ),
      );
    }

    return EnhancedDataGrid(
      data: _data,
      columns: widget.subsection.columns,
      onSort: (column, ascending) {
        setState(() {
          _sortColumn = column;
          _sortAscending = ascending;
          _currentPage = 1;
        });
        _loadData();
      },
      onCellEdit: (rowIndex, columnName, newValue) async {
        await _updateCell(rowIndex, columnName, newValue);
      },
      onRowDelete: (rowIndex) async {
        await _deleteRow(rowIndex);
      },
      onAddRow: _addRow,
    );
  }

  Widget _buildPagination() {
    final totalPages = (_totalRows / _pageSize).ceil();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          Text(
            'Page $_currentPage of $totalPages',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const Spacer(),
          IconButton(
            onPressed: _currentPage > 1 ? () {
              setState(() {
                _currentPage--;
              });
              _loadData();
            } : null,
            icon: const Icon(Icons.chevron_left),
          ),
          IconButton(
            onPressed: _currentPage < totalPages ? () {
              setState(() {
                _currentPage++;
              });
              _loadData();
            } : null,
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;

    try {
      switch (colorString.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'teal':
          return Colors.teal;
        case 'amber':
          return Colors.amber;
        case 'indigo':
          return Colors.indigo;
        default:
          return null;
      }
    } catch (e) {
      return null;
    }
  }
}

class _AddRowDialog extends StatefulWidget {
  final Subsection subsection;

  const _AddRowDialog({required this.subsection});

  @override
  State<_AddRowDialog> createState() => _AddRowDialogState();
}

class _AddRowDialogState extends State<_AddRowDialog> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    for (final column in widget.subsection.columns) {
      if (column.name != '_id') { // Skip auto-increment ID
        _controllers[column.name] = TextEditingController();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add New Row'),
      content: SizedBox(
        width: 400,
        height: 400,
        child: Form(
          key: _formKey,
          child: ListView(
            children: widget.subsection.columns
                .where((col) => col.name != '_id')
                .map((column) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: TextFormField(
                    controller: _controllers[column.name],
                    decoration: InputDecoration(
                      labelText: column.displayName + (column.isRequired ? ' *' : ''),
                      hintText: _getHintForColumnType(column.type),
                      border: const OutlineInputBorder(),
                    ),
                    validator: column.isRequired ? (value) {
                      if (value == null || value.trim().isEmpty) {
                        return '${column.displayName} is required';
                      }
                      return null;
                    } : null,
                    keyboardType: _getKeyboardTypeForColumn(column.type),
                  ),
                ))
                .toList(),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final data = <String, dynamic>{};
              for (final entry in _controllers.entries) {
                final value = entry.value.text.trim();
                if (value.isNotEmpty) {
                  data[entry.key] = value;
                }
              }
              Navigator.of(context).pop(data);
            }
          },
          child: const Text('Add'),
        ),
      ],
    );
  }

  String _getHintForColumnType(ColumnType type) {
    switch (type) {
      case ColumnType.email:
        return '<EMAIL>';
      case ColumnType.url:
        return 'https://example.com';
      case ColumnType.phone:
        return '+1234567890';
      case ColumnType.date:
        return 'YYYY-MM-DD';
      case ColumnType.datetime:
        return 'YYYY-MM-DD HH:MM:SS';
      case ColumnType.currency:
        return '0.00';
      case ColumnType.boolean:
        return 'true/false';
      default:
        return '';
    }
  }

  TextInputType _getKeyboardTypeForColumn(ColumnType type) {
    switch (type) {
      case ColumnType.integer:
        return TextInputType.number;
      case ColumnType.real:
      case ColumnType.currency:
        return const TextInputType.numberWithOptions(decimal: true);
      case ColumnType.email:
        return TextInputType.emailAddress;
      case ColumnType.url:
        return TextInputType.url;
      case ColumnType.phone:
        return TextInputType.phone;
      default:
        return TextInputType.text;
    }
  }
}
