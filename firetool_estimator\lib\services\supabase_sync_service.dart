import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/super_database_models.dart';
import 'super_database_service.dart';

/// Service for synchronizing data with Supabase
class SupabaseSyncService {
  static final SupabaseSyncService _instance = SupabaseSyncService._internal();
  factory SupabaseSyncService() => _instance;
  SupabaseSyncService._internal();

  final _superDbService = SuperDatabaseService();
  SupabaseClient? _supabase;

  /// Initialize Supabase connection
  Future<void> initialize({
    required String url,
    required String anonKey,
  }) async {
    try {
      await Supabase.initialize(
        url: url,
        anonKey: anon<PERSON>ey,
      );
      _supabase = Supabase.instance.client;
      
      if (kDebugMode) {
        print('Supabase initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing Supabase: $e');
      }
      rethrow;
    }
  }

  /// Check if Supa<PERSON> is initialized and connected
  bool get isInitialized => _supabase != null;

  /// Upload all local sections and subsections to Supabase
  Future<SyncResult> uploadAllSections() async {
    if (!isInitialized) {
      throw Exception('Supabase not initialized');
    }

    final result = SyncResult();
    
    try {
      final sections = await _superDbService.getSections();
      
      for (final section in sections) {
        final sectionResult = await uploadSection(section.id);
        result.merge(sectionResult);
      }

      result.status = SyncStatus.completed;
      result.endTime = DateTime.now();

      if (kDebugMode) {
        print('Upload completed: ${result.sectionsProcessed} sections, ${result.tablesProcessed} tables, ${result.rowsProcessed} rows');
      }

    } catch (e) {
      result.status = SyncStatus.failed;
      result.errorMessage = e.toString();
      result.endTime = DateTime.now();
      
      if (kDebugMode) {
        print('Upload failed: $e');
      }
    }

    return result;
  }

  /// Upload a specific section to Supabase
  Future<SyncResult> uploadSection(String sectionId) async {
    if (!isInitialized) {
      throw Exception('Supabase not initialized');
    }

    final result = SyncResult();
    
    try {
      final section = await _superDbService.getSection(sectionId);
      if (section == null) {
        throw Exception('Section not found');
      }

      // Upload section metadata
      await _uploadSectionMetadata(section);
      result.sectionsProcessed++;

      // Upload subsections and their data
      final subsections = await _superDbService.getSubsections(sectionId);
      
      for (final subsection in subsections) {
        await _uploadSubsectionMetadata(subsection);
        final rowsUploaded = await _uploadSubsectionData(section, subsection);
        result.rowsProcessed += rowsUploaded;
        result.tablesProcessed++;
      }

      result.status = SyncStatus.completed;
      result.endTime = DateTime.now();

    } catch (e) {
      result.status = SyncStatus.failed;
      result.errorMessage = e.toString();
      result.endTime = DateTime.now();
      
      if (kDebugMode) {
        print('Section upload failed: $e');
      }
    }

    return result;
  }

  /// Download and sync all data from Supabase
  Future<SyncResult> syncFromSupabase() async {
    if (!isInitialized) {
      throw Exception('Supabase not initialized');
    }

    final result = SyncResult();
    
    try {
      // Download section metadata
      final sectionsData = await _supabase!
          .from('super_database_sections')
          .select();

      for (final sectionData in sectionsData) {
        await _syncSectionFromSupabase(sectionData);
        result.sectionsProcessed++;
      }

      // Download subsection metadata
      final subsectionsData = await _supabase!
          .from('super_database_subsections')
          .select();

      for (final subsectionData in subsectionsData) {
        await _syncSubsectionFromSupabase(subsectionData);
        result.tablesProcessed++;
      }

      result.status = SyncStatus.completed;
      result.endTime = DateTime.now();

      if (kDebugMode) {
        print('Sync completed: ${result.sectionsProcessed} sections, ${result.tablesProcessed} tables');
      }

    } catch (e) {
      result.status = SyncStatus.failed;
      result.errorMessage = e.toString();
      result.endTime = DateTime.now();
      
      if (kDebugMode) {
        print('Sync failed: $e');
      }
    }

    return result;
  }

  /// Upload section metadata to Supabase
  Future<void> _uploadSectionMetadata(Section section) async {
    await _supabase!
        .from('super_database_sections')
        .upsert({
          'id': section.id,
          'name': section.name,
          'display_name': section.displayName,
          'created_at': section.createdAt.toIso8601String(),
          'updated_at': section.updatedAt.toIso8601String(),
          'description': section.description,
          'icon_name': section.iconName,
          'color': section.color,
        });
  }

  /// Upload subsection metadata to Supabase
  Future<void> _uploadSubsectionMetadata(Subsection subsection) async {
    await _supabase!
        .from('super_database_subsections')
        .upsert({
          'id': subsection.id,
          'section_id': subsection.sectionId,
          'name': subsection.name,
          'display_name': subsection.displayName,
          'created_at': subsection.createdAt.toIso8601String(),
          'updated_at': subsection.updatedAt.toIso8601String(),
          'description': subsection.description,
          'columns_json': jsonEncode(subsection.columns.map((c) => c.toJson()).toList()),
          'row_count': subsection.rowCount,
        });
  }

  /// Upload subsection data to Supabase
  Future<int> _uploadSubsectionData(Section section, Subsection subsection) async {
    final db = await _superDbService.getSectionDatabase(section.name);
    final rows = await db.query(subsection.name);

    if (rows.isEmpty) return 0;

    // Create table name for Supabase
    final tableName = 'data_${section.name}_${subsection.name}';

    // Ensure table exists in Supabase (this would need to be done via SQL or migrations)
    // For now, we'll store data in a generic table with JSON structure

    final dataToUpload = rows.map((row) => {
      'section_id': section.id,
      'subsection_id': subsection.id,
      'row_id': row['_id'],
      'data_json': jsonEncode(row),
      'updated_at': DateTime.now().toIso8601String(),
    }).toList();

    // Upload in batches to avoid size limits
    const batchSize = 100;
    int totalUploaded = 0;

    for (int i = 0; i < dataToUpload.length; i += batchSize) {
      final batch = dataToUpload.skip(i).take(batchSize).toList();
      
      await _supabase!
          .from('super_database_data')
          .upsert(batch);
      
      totalUploaded += batch.length;
    }

    return totalUploaded;
  }

  /// Sync section from Supabase to local database
  Future<void> _syncSectionFromSupabase(Map<String, dynamic> sectionData) async {
    // Check if section exists locally
    final existingSection = await _superDbService.getSection(sectionData['id']);
    
    if (existingSection == null) {
      // Create new section
      await _superDbService.createSection(
        name: sectionData['name'],
        displayName: sectionData['display_name'],
        description: sectionData['description'],
        iconName: sectionData['icon_name'],
        color: sectionData['color'],
      );
    }
    // TODO: Handle updates to existing sections
  }

  /// Sync subsection from Supabase to local database
  Future<void> _syncSubsectionFromSupabase(Map<String, dynamic> subsectionData) async {
    final columnsJson = subsectionData['columns_json'] as String;
    final columnsList = jsonDecode(columnsJson) as List;
    final columns = columnsList.map((c) => ColumnDefinition.fromJson(c)).toList();

    // Check if subsection exists locally
    final existingSubsections = await _superDbService.getSubsections(subsectionData['section_id']);
    final existingSubsection = existingSubsections
        .where((s) => s.id == subsectionData['id'])
        .firstOrNull;

    if (existingSubsection == null) {
      // Create new subsection
      await _superDbService.createSubsection(
        sectionId: subsectionData['section_id'],
        name: subsectionData['name'],
        displayName: subsectionData['display_name'],
        columns: columns,
        description: subsectionData['description'],
      );
    }
    // TODO: Handle updates to existing subsections and sync data
  }

  /// Get sync status
  Future<SyncStatus> getSyncStatus() async {
    if (!isInitialized) return SyncStatus.notInitialized;
    
    try {
      // Simple connectivity check
      await _supabase!.from('super_database_sections').select('id').limit(1);
      return SyncStatus.connected;
    } catch (e) {
      return SyncStatus.disconnected;
    }
  }
}

/// Sync operation result
class SyncResult {
  DateTime startTime = DateTime.now();
  DateTime? endTime;
  SyncStatus status = SyncStatus.inProgress;
  String? errorMessage;
  int sectionsProcessed = 0;
  int tablesProcessed = 0;
  int rowsProcessed = 0;

  void merge(SyncResult other) {
    sectionsProcessed += other.sectionsProcessed;
    tablesProcessed += other.tablesProcessed;
    rowsProcessed += other.rowsProcessed;
  }

  Duration get duration => (endTime ?? DateTime.now()).difference(startTime);

  Map<String, dynamic> toJson() => {
    'startTime': startTime.toIso8601String(),
    'endTime': endTime?.toIso8601String(),
    'status': status.name,
    'errorMessage': errorMessage,
    'sectionsProcessed': sectionsProcessed,
    'tablesProcessed': tablesProcessed,
    'rowsProcessed': rowsProcessed,
    'duration': duration.inMilliseconds,
  };
}

enum SyncStatus {
  notInitialized,
  connected,
  disconnected,
  inProgress,
  completed,
  failed,
}
