import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SQLite Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const DatabaseTestPage(),
    );
  }
}

class DatabaseTestPage extends StatefulWidget {
  const DatabaseTestPage({Key? key}) : super(key: key);

  @override
  State<DatabaseTestPage> createState() => _DatabaseTestPageState();
}

class _DatabaseTestPageState extends State<DatabaseTestPage> {
  final TextEditingController _tableNameController = TextEditingController(text: 'test_table');
  final TextEditingController _columnNameController = TextEditingController(text: 'test_column');
  final TextEditingController _columnTypeController = TextEditingController(text: 'TEXT');
  final TextEditingController _sqlController = TextEditingController();
  
  String _statusMessage = 'Ready';
  List<Map<String, dynamic>> _tableInfo = [];
  List<Map<String, dynamic>> _tableData = [];
  Database? _db;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initDatabase();
  }

  @override
  void dispose() {
    _tableNameController.dispose();
    _columnNameController.dispose();
    _columnTypeController.dispose();
    _sqlController.dispose();
    _closeDatabase();
    super.dispose();
  }

  Future<void> _initDatabase() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Initializing database...';
    });

    try {
      final dbPath = await getDatabasesPath();
      final path = join(dbPath, 'test.db');
      
      // Delete existing database for testing
      await deleteDatabase(path);
      
      _db = await openDatabase(
        path,
        version: 1,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE test_table (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT,
              value INTEGER
            )
          ''');
          
          // Insert some test data
          await db.insert('test_table', {'name': 'Test 1', 'value': 100});
          await db.insert('test_table', {'name': 'Test 2', 'value': 200});
          await db.insert('test_table', {'name': 'Test 3', 'value': 300});
        },
      );
      
      setState(() {
        _statusMessage = 'Database initialized successfully';
      });
      
      await _loadTableInfo();
      await _loadTableData();
    } catch (e) {
      setState(() {
        _statusMessage = 'Error initializing database: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _closeDatabase() async {
    await _db?.close();
  }

  Future<void> _loadTableInfo() async {
    if (_db == null) return;
    
    final tableName = _tableNameController.text.trim();
    if (tableName.isEmpty) return;
    
    setState(() {
      _isLoading = true;
      _statusMessage = 'Loading table info...';
    });
    
    try {
      final result = await _db!.rawQuery("PRAGMA table_info($tableName)");
      setState(() {
        _tableInfo = result;
        _statusMessage = 'Table info loaded successfully';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading table info: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadTableData() async {
    if (_db == null) return;
    
    final tableName = _tableNameController.text.trim();
    if (tableName.isEmpty) return;
    
    setState(() {
      _isLoading = true;
      _statusMessage = 'Loading table data...';
    });
    
    try {
      final result = await _db!.query(tableName);
      setState(() {
        _tableData = result;
        _statusMessage = 'Table data loaded successfully';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading table data: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addColumn() async {
    if (_db == null) return;
    
    final tableName = _tableNameController.text.trim();
    final columnName = _columnNameController.text.trim();
    final columnType = _columnTypeController.text.trim();
    
    if (tableName.isEmpty || columnName.isEmpty || columnType.isEmpty) {
      setState(() {
        _statusMessage = 'Please enter table name, column name, and column type';
      });
      return;
    }
    
    setState(() {
      _isLoading = true;
      _statusMessage = 'Adding column...';
    });
    
    try {
      await _db!.execute('ALTER TABLE $tableName ADD COLUMN $columnName $columnType');
      setState(() {
        _statusMessage = 'Column added successfully';
      });
      await _loadTableInfo();
    } catch (e) {
      setState(() {
        _statusMessage = 'Error adding column: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _executeSQL() async {
    if (_db == null) return;
    
    final sql = _sqlController.text.trim();
    if (sql.isEmpty) {
      setState(() {
        _statusMessage = 'Please enter SQL query';
      });
      return;
    }
    
    setState(() {
      _isLoading = true;
      _statusMessage = 'Executing SQL...';
    });
    
    try {
      await _db!.execute(sql);
      setState(() {
        _statusMessage = 'SQL executed successfully';
      });
      await _loadTableInfo();
      await _loadTableData();
    } catch (e) {
      setState(() {
        _statusMessage = 'Error executing SQL: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SQLite Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadTableInfo();
              _loadTableData();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status message
                  Container(
                    padding: const EdgeInsets.all(8.0),
                    color: Colors.grey.shade200,
                    width: double.infinity,
                    child: Text(
                      _statusMessage,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(height: 16.0),

                  // Table name input
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _tableNameController,
                          decoration: const InputDecoration(
                            labelText: 'Table Name',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: _loadTableInfo,
                        child: const Text('Load Info'),
                      ),
                      const SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: _loadTableData,
                        child: const Text('Load Data'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16.0),

                  // Add column section
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _columnNameController,
                          decoration: const InputDecoration(
                            labelText: 'Column Name',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      Expanded(
                        child: TextField(
                          controller: _columnTypeController,
                          decoration: const InputDecoration(
                            labelText: 'Column Type',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: _addColumn,
                        child: const Text('Add Column'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16.0),

                  // SQL execution section
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _sqlController,
                          decoration: const InputDecoration(
                            labelText: 'SQL Query',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: _executeSQL,
                        child: const Text('Execute'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16.0),

                  // Table info
                  if (_tableInfo.isNotEmpty) ...[
                    const Text(
                      'Table Structure:',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.0),
                    ),
                    const SizedBox(height: 8.0),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        columns: const [
                          DataColumn(label: Text('CID')),
                          DataColumn(label: Text('Name')),
                          DataColumn(label: Text('Type')),
                          DataColumn(label: Text('Not Null')),
                          DataColumn(label: Text('Default')),
                          DataColumn(label: Text('PK')),
                        ],
                        rows: _tableInfo.map((info) => DataRow(
                          cells: [
                            DataCell(Text(info['cid'].toString())),
                            DataCell(Text(info['name'].toString())),
                            DataCell(Text(info['type'].toString())),
                            DataCell(Text(info['notnull'].toString())),
                            DataCell(Text(info['dflt_value']?.toString() ?? '')),
                            DataCell(Text(info['pk'].toString())),
                          ],
                        )).toList(),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                  ],

                  // Table data
                  if (_tableData.isNotEmpty) ...[
                    const Text(
                      'Table Data:',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.0),
                    ),
                    const SizedBox(height: 8.0),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        columns: _tableData.first.keys.map((key) => 
                          DataColumn(label: Text(key))
                        ).toList(),
                        rows: _tableData.map((row) => DataRow(
                          cells: row.keys.map((key) => 
                            DataCell(Text(row[key]?.toString() ?? ''))
                          ).toList(),
                        )).toList(),
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}
