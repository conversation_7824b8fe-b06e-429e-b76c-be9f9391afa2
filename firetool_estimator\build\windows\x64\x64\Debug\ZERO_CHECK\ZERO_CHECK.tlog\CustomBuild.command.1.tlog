^D:\FIRETOOL\FIRETOOL_ESTIMATOR\BUILD\WINDOWS\X64\CMAKEFILES\342E957E6519E950E5393BE3F4957B0F\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Firetool/firetool_estimator/windows -BD:/Firetool/firetool_estimator/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/Firetool/firetool_estimator/build/windows/x64/firetool_estimator.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
