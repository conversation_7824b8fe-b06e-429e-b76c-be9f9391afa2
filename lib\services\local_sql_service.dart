import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

class LocalSqlService {
  static final LocalSqlService _instance = LocalSqlService._internal();
  static Database? _database;

  // Current database version - increment this when schema changes
  static const int _databaseVersion = 2;

  // Factory constructor
  factory LocalSqlService() {
    return _instance;
  }

  // Private constructor
  LocalSqlService._internal();

  // Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialize database
  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'firetool.db');

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  // Handle database upgrades
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    debugPrint('Upgrading database from version $oldVersion to $newVersion');

    if (oldVersion < 2) {
      // Add any schema changes for version 2
      debugPrint('Applying version 2 upgrades...');
      // Example: We could add new columns to existing tables here
    }

    // Add more version checks as needed for future upgrades
  }

  // Create database tables
  Future<void> _createDatabase(Database db, int version) async {
    // Create tables for each system type
    await db.execute('''
      CREATE TABLE alarm (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE water (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE foam (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE fm200 (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE novec (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE co2 (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE materials (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    // Insert sample data
    await _insertSampleData(db);
  }

  // Insert sample data for each system type
  Future<void> _insertSampleData(Database db) async {
    final systemTypes = ['alarm', 'water', 'foam', 'fm200', 'novec', 'co2', 'materials'];

    for (var systemType in systemTypes) {
      for (var i = 1; i <= 20; i++) {
        await db.insert(systemType, {
          'id': 'sample-$i',
          'model': 'Model $i',
          'description': 'Description for $systemType item $i',
          'manufacturer': 'Manufacturer ${i % 3 + 1}',
          'approval': 'UL Listed',
          'ex_works_price': '${i * 100}',
          'local_price': '${i * 150}',
          'installation_price': '${i * 50}',
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }
  }

  // CRUD Operations

  // Get all items from a collection
  Future<List<Map<String, dynamic>>> getItems(String collectionPath) async {
    final db = await database;
    return await db.query(collectionPath);
  }

  // Add a new item to a collection
  Future<String> addItem(String collectionPath, Map<String, dynamic> item) async {
    final db = await database;

    // Generate a unique ID if not provided
    if (!item.containsKey('id') || item['id'] == null) {
      item['id'] = 'item-${DateTime.now().millisecondsSinceEpoch}';
    }

    // Add created_at timestamp if not provided
    if (!item.containsKey('created_at') || item['created_at'] == null) {
      item['created_at'] = DateTime.now().toIso8601String();
    }

    await db.insert(collectionPath, item);
    return item['id'];
  }

  // Update an item in a collection
  Future<void> updateItem(String collectionPath, String itemId, Map<String, dynamic> item) async {
    final db = await database;
    await db.update(
      collectionPath,
      item,
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Update a specific field in an item
  Future<void> updateField(String collectionPath, String itemId, String field, dynamic value) async {
    final db = await database;
    await db.update(
      collectionPath,
      {field: value},
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Delete an item from a collection
  Future<void> deleteItem(String collectionPath, String itemId) async {
    final db = await database;
    await db.delete(
      collectionPath,
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Close the database
  Future<void> close() async {
    final db = await database;
    db.close();
  }

  // Schema modification methods

  // Check if a column exists in a table
  Future<bool> columnExists(String tableName, String columnName) async {
    final db = await database;
    var result = await db.rawQuery("PRAGMA table_info($tableName)");

    for (var column in result) {
      if (column['name'] == columnName) {
        return true;
      }
    }

    return false;
  }

  // Add a new column to an existing table
  Future<void> addColumn(String tableName, String columnName, String columnType) async {
    final db = await database;

    // Check if column already exists
    bool exists = await columnExists(tableName, columnName);
    if (exists) {
      print('Column $columnName already exists in table $tableName');
      return;
    }

    try {
      await db.execute('ALTER TABLE $tableName ADD COLUMN $columnName $columnType');
      print('Added column $columnName to table $tableName');
    } catch (e) {
      print('Error adding column: $e');
      throw e;
    }
  }

  // Delete a column from a table (SQLite doesn't support DROP COLUMN directly)
  Future<void> deleteColumn(String tableName, String columnName) async {
    final db = await database;

    // Check if column exists
    bool exists = await columnExists(tableName, columnName);
    if (!exists) {
      print('Column $columnName does not exist in table $tableName');
      return;
    }

    try {
      // SQLite doesn't support DROP COLUMN directly, so we need to:
      // 1. Get all columns except the one to delete
      // 2. Create a new table with those columns
      // 3. Copy data from old table to new table
      // 4. Drop old table
      // 5. Rename new table to old table name

      // Get all columns
      List<Map<String, dynamic>> columns = await getTableColumns(tableName);

      // Filter out the column to delete
      List<Map<String, dynamic>> remainingColumns = columns.where((col) => col['name'] != columnName).toList();

      // Create column definitions for new table
      List<String> columnDefs = remainingColumns.map((col) {
        String name = col['name'].toString();
        String type = col['type'].toString();
        bool notNull = col['notnull'] == 1;
        bool primaryKey = col['pk'] == 1;

        String def = '$name $type';
        if (notNull) def += ' NOT NULL';
        if (primaryKey) def += ' PRIMARY KEY';
        if (col['dflt_value'] != null) def += ' DEFAULT ${col["dflt_value"]}';

        return def;
      }).toList();

      // Create new table
      String tempTableName = '${tableName}_temp';
      await db.execute('CREATE TABLE $tempTableName (${columnDefs.join(', ')})');

      // Get column names for SELECT statement
      List<String> columnNames = remainingColumns.map((col) => col['name'].toString()).toList();

      // Copy data
      await db.execute('INSERT INTO $tempTableName SELECT ${columnNames.join(', ')} FROM $tableName');

      // Drop old table
      await db.execute('DROP TABLE $tableName');

      // Rename new table
      await db.execute('ALTER TABLE $tempTableName RENAME TO $tableName');

      print('Deleted column $columnName from table $tableName');
    } catch (e) {
      print('Error deleting column: $e');
      throw e;
    }
  }

  // Get all table names in the database
  Future<List<String>> getTableNames() async {
    final db = await database;
    var tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE 'android_%'");
    return tables.map((table) => table['name'] as String).toList();
  }

  // Get all columns for a table
  Future<List<Map<String, dynamic>>> getTableColumns(String tableName) async {
    final db = await database;
    return await db.rawQuery("PRAGMA table_info($tableName)");
  }

  // Create a new table with the specified columns
  Future<void> createTable(String tableName, Map<String, String> columns) async {
    final db = await database;

    // Build the CREATE TABLE statement
    StringBuffer sql = StringBuffer();
    sql.write('CREATE TABLE $tableName (');

    List<String> columnDefs = [];
    columns.forEach((name, type) {
      columnDefs.add('$name $type');
    });

    sql.write(columnDefs.join(', '));
    sql.write(')');

    try {
      await db.execute(sql.toString());
      print('Created table $tableName');
    } catch (e) {
      print('Error creating table: $e');
      throw e;
    }
  }

  // Drop a table
  Future<void> dropTable(String tableName) async {
    final db = await database;

    try {
      await db.execute('DROP TABLE IF EXISTS $tableName');
      print('Dropped table $tableName');
    } catch (e) {
      print('Error dropping table: $e');
      throw e;
    }
  }

  // Rename a table
  Future<void> renameTable(String oldName, String newName) async {
    final db = await database;

    try {
      await db.execute('ALTER TABLE $oldName RENAME TO $newName');
      print('Renamed table $oldName to $newName');
    } catch (e) {
      print('Error renaming table: $e');
      throw e;
    }
  }

  // Copy table structure and data to a new table
  Future<void> copyTable(String sourceTable, String targetTable) async {
    final db = await database;

    try {
      // Create the new table with the same structure
      await db.execute('CREATE TABLE $targetTable AS SELECT * FROM $sourceTable');
      print('Copied table $sourceTable to $targetTable');
    } catch (e) {
      print('Error copying table: $e');
      throw e;
    }
  }

  // Execute a raw SQL query
  Future<void> executeRawSql(String sql) async {
    final db = await database;

    try {
      await db.execute(sql);
      print('Executed SQL: $sql');
    } catch (e) {
      print('Error executing SQL: $e');
      throw e;
    }
  }
}
