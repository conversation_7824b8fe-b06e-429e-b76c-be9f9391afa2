import 'package:flutter/material.dart';
import '../widgets/super_database_grid.dart';
import '../services/supabase_sync_service.dart';
import '../services/excel_import_export_service.dart';
import '../models/super_database_models.dart';
import 'supabase_settings_screen.dart';

class SuperDatabaseScreen extends StatefulWidget {
  const SuperDatabaseScreen({super.key});

  @override
  State<SuperDatabaseScreen> createState() => _SuperDatabaseScreenState();
}

class _SuperDatabaseScreenState extends State<SuperDatabaseScreen>
    with TickerProviderStateMixin {

  late TabController _tabController;

  // Define sections (like Fire Alarm, Firefighting, Foam, etc.)
  final List<String> _sectionNames = [
    'Fire Alarm',
    'Firefighting',
    'Foam Systems',
    'Clean Agent',
    'Water Systems',
    'Detection',
    'Emergency',
    'Access Control',
  ];

  final List<Color> _sectionColors = [
    Colors.red.shade700,
    Colors.blue.shade700,
    Colors.green.shade700,
    Colors.orange.shade700,
    Colors.cyan.shade700,
    Colors.purple.shade700,
    Colors.amber.shade700,
    Colors.indigo.shade700,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: _sectionNames.length,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SuperDatabase'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.cloud_upload),
            onPressed: _uploadToSupabase,
            tooltip: 'Upload to Supabase',
          ),
          IconButton(
            icon: const Icon(Icons.cloud_sync),
            onPressed: _syncWithSupabase,
            tooltip: 'Sync with Supabase',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _openSettings,
            tooltip: 'Settings',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addNewSection,
            tooltip: 'Add Section',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _sectionNames.asMap().entries.map((entry) {
            final index = entry.key;
            final name = entry.value;
            return Tab(
              icon: Icon(
                _getSectionIcon(name),
                color: _sectionColors[index],
              ),
              text: name,
            );
          }).toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: _sectionNames.asMap().entries.map((entry) {
          final index = entry.key;
          final name = entry.value;
          return SuperDatabaseGrid(
            sectionName: name,
            sectionId: name.toLowerCase().replaceAll(' ', '_'),
            themeColor: _sectionColors[index],
          );
        }).toList(),
      ),
    );
  }

  IconData _getSectionIcon(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'fire alarm':
        return Icons.warning;
      case 'firefighting':
        return Icons.local_fire_department;
      case 'foam systems':
        return Icons.bubble_chart;
      case 'clean agent':
        return Icons.air;
      case 'water systems':
        return Icons.water_drop;
      case 'detection':
        return Icons.sensors;
      case 'emergency':
        return Icons.emergency;
      case 'access control':
        return Icons.security;
      default:
        return Icons.storage;
    }
  }

  Future<void> _uploadToSupabase() async {
    try {
      _showLoadingDialog('Uploading to Supabase...');

      final supabaseService = SupabaseSyncService();
      final result = await supabaseService.uploadAllSections();

      if (mounted) Navigator.of(context).pop(); // Close loading dialog

      if (result.status == SyncStatus.completed) {
        _showSnackBar('Successfully uploaded all sections to Supabase', isError: false);
      } else {
        _showSnackBar('Upload failed: ${result.errorMessage}', isError: true);
      }
    } catch (e) {
      if (mounted) Navigator.of(context).pop(); // Close loading dialog
      _showSnackBar('Upload error: $e', isError: true);
    }
  }

  Future<void> _syncWithSupabase() async {
    try {
      _showLoadingDialog('Syncing with Supabase...');

      final supabaseService = SupabaseSyncService();
      final result = await supabaseService.syncFromSupabase();

      if (mounted) Navigator.of(context).pop(); // Close loading dialog

      if (result.status == SyncStatus.completed) {
        _showSnackBar('Successfully synced with Supabase', isError: false);
      } else {
        _showSnackBar('Sync failed: ${result.errorMessage}', isError: true);
      }
    } catch (e) {
      if (mounted) Navigator.of(context).pop(); // Close loading dialog
      _showSnackBar('Sync error: $e', isError: true);
    }
  }

  void _openSettings() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SupabaseSettingsScreen(),
      ),
    );
  }

  Future<void> _addNewSection() async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Section'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose how to create the new section:'),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop('manual'),
              icon: const Icon(Icons.edit),
              label: const Text('Create Manually'),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop('excel'),
              icon: const Icon(Icons.upload_file),
              label: const Text('Import from Excel'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (result == 'manual') {
      _createManualSection();
    } else if (result == 'excel') {
      _importFromExcel();
    }
  }

  Future<void> _createManualSection() async {
    final nameController = TextEditingController();

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Section'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(
            labelText: 'Section Name',
            hintText: 'e.g., Fire Alarm, Firefighting',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(nameController.text),
            child: const Text('Create'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      setState(() {
        _sectionNames.add(result);
        _sectionColors.add(Colors.teal.shade700);
        _tabController.dispose();
        _tabController = TabController(
          length: _sectionNames.length,
          vsync: this,
        );
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Section "$result" created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _importFromExcel() async {
    try {
      final excelService = ExcelImportExportService();
      final operation = await excelService.importExcelAsSection(
        sectionDisplayName: 'Imported Section ${DateTime.now().millisecondsSinceEpoch}',
        description: 'Section created from Excel import',
      );

      if (operation.status == OperationStatus.completed) {
        _showSnackBar('Successfully imported ${operation.processedRows} rows from Excel', isError: false);

        // Add the new section to our list
        setState(() {
          _sectionNames.add(operation.sectionId);
          _sectionColors.add(Colors.teal.shade700);
          _tabController.dispose();
          _tabController = TabController(
            length: _sectionNames.length,
            vsync: this,
          );
        });
      } else {
        _showSnackBar('Import failed: ${operation.errorMessage}', isError: true);
      }
    } catch (e) {
      _showSnackBar('Import error: $e', isError: true);
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: Duration(seconds: isError ? 5 : 3),
        ),
      );
    }
  }
}
