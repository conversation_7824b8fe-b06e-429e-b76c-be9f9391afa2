import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/project.dart';

class EditMaterialDialog extends StatefulWidget {
  final MaterialItem material;
  final double exchangeRate;
  final Function(MaterialItem) onSave;

  const EditMaterialDialog({
    super.key,
    required this.material,
    required this.exchangeRate,
    required this.onSave,
  });

  @override
  State<EditMaterialDialog> createState() => _EditMaterialDialogState();
}

class _EditMaterialDialogState extends State<EditMaterialDialog> {
  late final TextEditingController _quantityController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _vendorController;
  late final TextEditingController _approvalController;
  late final TextEditingController _exWorksUnitCostController;
  late final TextEditingController _localUnitCostController;
  late final TextEditingController _installationUnitCostController;

  double _totalUnitRate = 0;

  @override
  void initState() {
    super.initState();
    _quantityController = TextEditingController(text: widget.material.quantity.toStringAsFixed(0));
    _descriptionController = TextEditingController(text: widget.material.description);
    _vendorController = TextEditingController(text: widget.material.vendor);
    _approvalController = TextEditingController(text: widget.material.approval);
    _exWorksUnitCostController = TextEditingController(text: widget.material.exWorksUnitCost.toString());
    _localUnitCostController = TextEditingController(text: widget.material.localUnitCost.toString());
    _installationUnitCostController = TextEditingController(text: widget.material.installationUnitCost.toString());

    _calculateTotalUnitRate();
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _descriptionController.dispose();
    _vendorController.dispose();
    _approvalController.dispose();
    _exWorksUnitCostController.dispose();
    _localUnitCostController.dispose();
    _installationUnitCostController.dispose();
    super.dispose();
  }

  void _calculateTotalUnitRate() {
    final exWorksCost = double.tryParse(_exWorksUnitCostController.text) ?? 0.0;
    final localCost = double.tryParse(_localUnitCostController.text) ?? 0.0;
    final installationCost = double.tryParse(_installationUnitCostController.text) ?? 0.0;

    // Calculate and round to nearest whole number
    final calculatedRate = (exWorksCost * widget.exchangeRate) + localCost + installationCost;
    setState(() {
      _totalUnitRate = calculatedRate.roundToDouble();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Edit ${widget.material.name}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const Divider(),

            // Content
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Quantity
                    _buildSectionHeader('Quantity'),
                    _buildQuantityField(),
                    const SizedBox(height: 16),

                    // Description
                    _buildSectionHeader('Description'),
                    _buildTextField(_descriptionController, 'Enter description', maxLines: 2),
                    const SizedBox(height: 16),

                    // Vendor/Manufacturer
                    _buildSectionHeader('Vendor/Manufacturer'),
                    _buildTextField(_vendorController, 'Enter vendor or manufacturer'),
                    const SizedBox(height: 16),

                    // Approval/Certification
                    _buildSectionHeader('Approval/Certification'),
                    _buildTextField(_approvalController, 'Enter approval or certification'),
                    const SizedBox(height: 16),

                    const Divider(),

                    // Pricing
                    _buildSectionHeader('Ex-Works Unit Cost (USD)'),
                    _buildNumberField(_exWorksUnitCostController, '\$', _onCostChanged),
                    const SizedBox(height: 16),

                    _buildSectionHeader('Local Unit Cost (SAR)'),
                    _buildNumberField(_localUnitCostController, 'SAR', _onCostChanged),
                    const SizedBox(height: 16),

                    _buildSectionHeader('Installation Unit Cost (SAR)'),
                    _buildNumberField(_installationUnitCostController, 'SAR', _onCostChanged),
                    const SizedBox(height: 16),

                    // Exchange rate info
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Text(
                        'Exchange Rate: \$1 = SAR ${widget.exchangeRate.toStringAsFixed(2)}',
                        style: const TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                      ),
                    ),

                    // Total unit rate
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Text(
                            'Total Unit Rate:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'SAR ${_totalUnitRate.toInt()}',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Buttons
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _saveChanges,
                  child: const Text('Save'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildTextField(TextEditingController controller, String hint, {int maxLines = 1}) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          hintText: hint,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        maxLines: maxLines,
      ),
    );
  }

  Widget _buildNumberField(TextEditingController controller, String prefix, Function() onChanged) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            child: Text(
              prefix,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: controller,
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
              onChanged: (_) => onChanged(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityField() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: const Text(
              '#',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: _quantityController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
            ),
          ),
          Row(
            children: [
              InkWell(
                onTap: () {
                  final currentValue = int.tryParse(_quantityController.text) ?? 0;
                  if (currentValue > 0) {
                    setState(() {
                      _quantityController.text = (currentValue - 1).toString();
                    });
                  }
                },
                child: Container(
                  width: 36,
                  height: 36,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.remove,
                    color: Colors.black54,
                    size: 20,
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  final currentValue = int.tryParse(_quantityController.text) ?? 0;
                  setState(() {
                    _quantityController.text = (currentValue + 1).toString();
                  });
                },
                child: Container(
                  width: 36,
                  height: 36,
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.add,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _onCostChanged() {
    _calculateTotalUnitRate();
  }

  void _saveChanges() {
    final updatedMaterial = MaterialItem(
      id: widget.material.id,
      name: widget.material.name,
      category: widget.material.category,
      description: _descriptionController.text,
      quantity: double.tryParse(_quantityController.text) ?? widget.material.quantity,
      unit: widget.material.unit,
      exWorksUnitCost: double.tryParse(_exWorksUnitCostController.text) ?? widget.material.exWorksUnitCost,
      localUnitCost: double.tryParse(_localUnitCostController.text) ?? widget.material.localUnitCost,
      installationUnitCost: double.tryParse(_installationUnitCostController.text) ?? widget.material.installationUnitCost,
      isImported: widget.material.isImported,
      vendor: _vendorController.text,
      approval: _approvalController.text,
    );

    widget.onSave(updatedMaterial);
    Navigator.pop(context);
  }
}
