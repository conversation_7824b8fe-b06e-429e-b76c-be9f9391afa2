import 'package:flutter/material.dart';

class CreateSectionDialog extends StatefulWidget {
  const CreateSectionDialog({super.key});

  @override
  State<CreateSectionDialog> createState() => _CreateSectionDialogState();
}

class _CreateSectionDialogState extends State<CreateSectionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  bool _importFromExcel = false;
  String _selectedIcon = 'folder';
  String _selectedColor = 'blue';

  final List<Map<String, dynamic>> _iconOptions = [
    {'name': 'folder', 'icon': Icons.folder, 'label': 'Folder'},
    {'name': 'fire_alarm', 'icon': Icons.local_fire_department, 'label': 'Fire Alarm'},
    {'name': 'water', 'icon': Icons.water_drop, 'label': 'Water Systems'},
    {'name': 'foam', 'icon': Icons.bubble_chart, 'label': 'Foam Systems'},
    {'name': 'co2', 'icon': Icons.cloud, 'label': 'CO2 Systems'},
    {'name': 'pump', 'icon': Icons.settings_input_hdmi, 'label': 'Pumps'},
    {'name': 'civil', 'icon': Icons.construction, 'label': 'Civil Works'},
    {'name': 'clean_agent', 'icon': Icons.cleaning_services, 'label': 'Clean Agent'},
    {'name': 'database', 'icon': Icons.storage, 'label': 'Database'},
    {'name': 'safety', 'icon': Icons.security, 'label': 'Safety'},
  ];

  final List<Map<String, dynamic>> _colorOptions = [
    {'name': 'blue', 'color': Colors.blue, 'label': 'Blue'},
    {'name': 'red', 'color': Colors.red, 'label': 'Red'},
    {'name': 'green', 'color': Colors.green, 'label': 'Green'},
    {'name': 'orange', 'color': Colors.orange, 'label': 'Orange'},
    {'name': 'purple', 'color': Colors.purple, 'label': 'Purple'},
    {'name': 'teal', 'color': Colors.teal, 'label': 'Teal'},
    {'name': 'amber', 'color': Colors.amber, 'label': 'Amber'},
    {'name': 'indigo', 'color': Colors.indigo, 'label': 'Indigo'},
  ];

  @override
  void initState() {
    super.initState();
    _displayNameController.addListener(_updateNameFromDisplayName);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _displayNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _updateNameFromDisplayName() {
    final displayName = _displayNameController.text;
    final sanitizedName = displayName
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .replaceAll(RegExp(r'\s+'), '_');
    _nameController.text = sanitizedName;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New Section'),
      content: SizedBox(
        width: 500,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Import option
                SwitchListTile(
                  title: const Text('Import from Excel'),
                  subtitle: const Text('Create section from Excel file with multiple sheets'),
                  value: _importFromExcel,
                  onChanged: (value) {
                    setState(() {
                      _importFromExcel = value;
                    });
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Display name
                TextFormField(
                  controller: _displayNameController,
                  decoration: const InputDecoration(
                    labelText: 'Display Name *',
                    hintText: 'e.g., Fire Alarm Systems',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Display name is required';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Internal name (auto-generated)
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Internal Name',
                    hintText: 'Auto-generated from display name',
                    border: OutlineInputBorder(),
                  ),
                  readOnly: true,
                  style: TextStyle(color: Colors.grey.shade600),
                ),
                
                const SizedBox(height: 16),
                
                // Description
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    hintText: 'Optional description for this section',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                
                const SizedBox(height: 24),
                
                // Icon selection
                Text(
                  'Icon',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _iconOptions.map((option) {
                    final isSelected = _selectedIcon == option['name'];
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedIcon = option['name'];
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: isSelected 
                              ? Theme.of(context).primaryColor.withOpacity(0.1)
                              : Colors.grey.shade100,
                          border: Border.all(
                            color: isSelected 
                                ? Theme.of(context).primaryColor
                                : Colors.grey.shade300,
                            width: isSelected ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              option['icon'],
                              color: isSelected 
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey.shade600,
                              size: 24,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              option['label'],
                              style: TextStyle(
                                fontSize: 10,
                                color: isSelected 
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
                
                const SizedBox(height: 24),
                
                // Color selection
                Text(
                  'Color',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _colorOptions.map((option) {
                    final isSelected = _selectedColor == option['name'];
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedColor = option['name'];
                        });
                      },
                      child: Container(
                        width: 60,
                        height: 40,
                        decoration: BoxDecoration(
                          color: option['color'],
                          border: Border.all(
                            color: isSelected ? Colors.black : Colors.grey.shade300,
                            width: isSelected ? 3 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 20,
                              )
                            : null,
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _createSection,
          child: Text(_importFromExcel ? 'Import & Create' : 'Create'),
        ),
      ],
    );
  }

  void _createSection() {
    if (_formKey.currentState!.validate()) {
      final result = {
        'name': _nameController.text.trim(),
        'displayName': _displayNameController.text.trim(),
        'description': _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        'iconName': _selectedIcon,
        'color': _selectedColor,
        'importFromExcel': _importFromExcel,
      };
      
      Navigator.of(context).pop(result);
    }
  }
}
