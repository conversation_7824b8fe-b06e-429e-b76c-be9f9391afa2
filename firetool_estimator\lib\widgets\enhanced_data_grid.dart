import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/super_database_models.dart';

class EnhancedDataGrid extends StatefulWidget {
  final List<Map<String, dynamic>> data;
  final List<ColumnDefinition> columns;
  final Function(String column, bool ascending)? onSort;
  final Function(int rowIndex, String columnName, dynamic newValue)? onCellEdit;
  final Function(int rowIndex)? onRowDelete;
  final VoidCallback? onAddRow;

  const EnhancedDataGrid({
    Key? key,
    required this.data,
    required this.columns,
    this.onSort,
    this.onCellEdit,
    this.onRowDelete,
    this.onAddRow,
  }) : super(key: key);

  @override
  State<EnhancedDataGrid> createState() => _EnhancedDataGridState();
}

class _EnhancedDataGridState extends State<EnhancedDataGrid> {
  String? _sortColumn;
  bool _sortAscending = true;
  int? _editingRow;
  String? _editingColumn;
  final Map<String, TextEditingController> _editControllers = {};
  final Map<String, bool> _columnVisibility = {};
  String _searchQuery = '';

  // Keyboard navigation
  int _selectedRow = 0;
  int _selectedColumn = 0;
  final FocusNode _gridFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Initialize column visibility
    for (final column in widget.columns) {
      _columnVisibility[column.name] = true;
    }
  }

  @override
  void dispose() {
    for (final controller in _editControllers.values) {
      controller.dispose();
    }
    _gridFocusNode.dispose();
    super.dispose();
  }

  void _handleKeyEvent(KeyEvent event) {
    if (event is KeyDownEvent) {
      final data = _sortedData;
      if (data.isEmpty) return;

      switch (event.logicalKey) {
        case LogicalKeyboardKey.arrowUp:
          setState(() {
            _selectedRow = (_selectedRow - 1).clamp(0, data.length - 1);
          });
          break;
        case LogicalKeyboardKey.arrowDown:
          setState(() {
            _selectedRow = (_selectedRow + 1).clamp(0, data.length - 1);
          });
          break;
        case LogicalKeyboardKey.arrowLeft:
          setState(() {
            _selectedColumn = (_selectedColumn - 1).clamp(0, _visibleColumns.length - 1);
          });
          break;
        case LogicalKeyboardKey.arrowRight:
          setState(() {
            _selectedColumn = (_selectedColumn + 1).clamp(0, _visibleColumns.length - 1);
          });
          break;
        case LogicalKeyboardKey.enter:
          if (_selectedRow < data.length && _selectedColumn < _visibleColumns.length) {
            final column = _visibleColumns[_selectedColumn];
            if (column.name != '_id') {
              final value = data[_selectedRow][column.name];
              _startEditing(_selectedRow, column.name, value);
            }
          }
          break;
      }
    }
  }

  List<Map<String, dynamic>> get _filteredData {
    if (_searchQuery.isEmpty) return widget.data;

    return widget.data.where((row) {
      return widget.columns.any((column) {
        final value = row[column.name]?.toString().toLowerCase() ?? '';
        return value.contains(_searchQuery.toLowerCase());
      });
    }).toList();
  }

  List<Map<String, dynamic>> get _sortedData {
    final data = List<Map<String, dynamic>>.from(_filteredData);

    if (_sortColumn != null) {
      data.sort((a, b) {
        final aValue = a[_sortColumn] ?? '';
        final bValue = b[_sortColumn] ?? '';

        int comparison;
        if (aValue is num && bValue is num) {
          comparison = aValue.compareTo(bValue);
        } else {
          comparison = aValue.toString().compareTo(bValue.toString());
        }

        return _sortAscending ? comparison : -comparison;
      });
    }

    return data;
  }

  List<ColumnDefinition> get _visibleColumns {
    return widget.columns.where((col) => _columnVisibility[col.name] == true).toList();
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: _gridFocusNode,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: () => _gridFocusNode.requestFocus(),
        child: Column(
          children: [
            // Toolbar
            _buildToolbar(),

            // Data grid
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    // Header
                    _buildHeader(),

                    // Data rows
                    Expanded(
                      child: _buildDataRows(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: Row(
        children: [
          // Search
          Expanded(
            flex: 2,
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          const SizedBox(width: 16),

          // Column visibility
          PopupMenuButton<String>(
            icon: const Icon(Icons.view_column),
            tooltip: 'Show/Hide Columns',
            itemBuilder: (context) => widget.columns.map((column) {
              return PopupMenuItem<String>(
                value: column.name,
                child: StatefulBuilder(
                  builder: (context, setState) => CheckboxListTile(
                    title: Text(column.displayName),
                    value: _columnVisibility[column.name],
                    onChanged: (value) {
                      setState(() {
                        _columnVisibility[column.name] = value ?? true;
                      });
                      this.setState(() {});
                    },
                    dense: true,
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(width: 8),

          // Add row button
          if (widget.onAddRow != null)
            ElevatedButton.icon(
              onPressed: widget.onAddRow,
              icon: const Icon(Icons.add, size: 16),
              label: const Text('Add Row'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          // Row number header
          Container(
            width: 60,
            height: 48,
            decoration: BoxDecoration(
              border: Border(right: BorderSide(color: Colors.grey.shade300)),
            ),
            child: const Center(
              child: Text('#', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          ),

          // Column headers
          ..._visibleColumns.map((column) => _buildHeaderCell(column)),

          // Actions header
          Container(
            width: 80,
            height: 48,
            decoration: BoxDecoration(
              border: Border(left: BorderSide(color: Colors.grey.shade300)),
            ),
            child: const Center(
              child: Text('Actions', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(ColumnDefinition column) {
    final isCurrentSort = _sortColumn == column.name;

    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            if (isCurrentSort) {
              _sortAscending = !_sortAscending;
            } else {
              _sortColumn = column.name;
              _sortAscending = true;
            }
          });

          if (widget.onSort != null) {
            widget.onSort!(column.name, _sortAscending);
          }
        },
        child: Container(
          height: 48,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border(right: BorderSide(color: Colors.grey.shade300)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  column.displayName,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                _getColumnTypeIcon(column.type),
                size: 16,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              if (isCurrentSort)
                Icon(
                  _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDataRows() {
    final data = _sortedData;

    if (data.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.table_chart, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No data available'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: data.length,
      itemBuilder: (context, index) => _buildDataRow(data[index], index),
    );
  }

  Widget _buildDataRow(Map<String, dynamic> row, int index) {
    final isSelectedRow = _selectedRow == index;

    return Container(
      decoration: BoxDecoration(
        color: isSelectedRow
            ? Colors.blue.shade50
            : (index.isEven ? Colors.white : Colors.grey.shade50),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
          left: isSelectedRow ? BorderSide(color: Colors.blue.shade300, width: 3) : BorderSide.none,
        ),
      ),
      child: Row(
        children: [
          // Row number
          Container(
            width: 60,
            height: 48,
            decoration: BoxDecoration(
              border: Border(right: BorderSide(color: Colors.grey.shade300)),
              color: isSelectedRow ? Colors.blue.shade100 : null,
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: TextStyle(
                  color: isSelectedRow ? Colors.blue.shade700 : Colors.grey.shade600,
                  fontWeight: isSelectedRow ? FontWeight.bold : FontWeight.w500,
                ),
              ),
            ),
          ),

          // Data cells
          ..._visibleColumns.asMap().entries.map((entry) {
            final colIndex = entry.key;
            final column = entry.value;
            return _buildDataCell(row, column, index, colIndex);
          }),

          // Actions
          Container(
            width: 80,
            height: 48,
            decoration: BoxDecoration(
              border: Border(left: BorderSide(color: Colors.grey.shade300)),
              color: isSelectedRow ? Colors.blue.shade100 : null,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (widget.onRowDelete != null)
                  IconButton(
                    icon: const Icon(Icons.delete, size: 16),
                    onPressed: () => widget.onRowDelete!(index),
                    color: Colors.red,
                    tooltip: 'Delete row',
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataCell(Map<String, dynamic> row, ColumnDefinition column, int rowIndex, int colIndex) {
    final value = row[column.name];
    final isEditing = _editingRow == rowIndex && _editingColumn == column.name;
    final isSelectedCell = _selectedRow == rowIndex && _selectedColumn == colIndex;

    return Expanded(
      child: InkWell(
        onDoubleTap: column.name != '_id' ? () => _startEditing(rowIndex, column.name, value) : null,
        onTap: () {
          setState(() {
            _selectedRow = rowIndex;
            _selectedColumn = colIndex;
          });
          _gridFocusNode.requestFocus();
        },
        child: Container(
          height: 48,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border(right: BorderSide(color: Colors.grey.shade300)),
            color: isEditing
                ? Colors.blue.shade100
                : isSelectedCell
                    ? Colors.blue.shade200
                    : null,
          ),
          child: isEditing ? _buildEditWidget(column, value, rowIndex) : _buildDisplayWidget(column, value),
        ),
      ),
    );
  }

  Widget _buildDisplayWidget(ColumnDefinition column, dynamic value) {
    return Align(
      alignment: _getCellAlignment(column.type),
      child: Text(
        _formatCellValue(value, column.type),
        overflow: TextOverflow.ellipsis,
        style: _getCellTextStyle(column.type, value),
      ),
    );
  }

  Widget _buildEditWidget(ColumnDefinition column, dynamic value, int rowIndex) {
    final key = '${rowIndex}_${column.name}';
    _editControllers[key] ??= TextEditingController(text: value?.toString() ?? '');

    return TextField(
      controller: _editControllers[key],
      autofocus: true,
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.zero,
      ),
      keyboardType: _getKeyboardType(column.type),
      onSubmitted: (newValue) => _finishEditing(rowIndex, column.name, newValue),
      onTapOutside: (_) => _finishEditing(rowIndex, column.name, _editControllers[key]?.text ?? ''),
    );
  }

  void _startEditing(int rowIndex, String columnName, dynamic currentValue) {
    setState(() {
      _editingRow = rowIndex;
      _editingColumn = columnName;
    });
  }

  void _finishEditing(int rowIndex, String columnName, String newValue) {
    setState(() {
      _editingRow = null;
      _editingColumn = null;
    });

    if (widget.onCellEdit != null) {
      widget.onCellEdit!(rowIndex, columnName, newValue);
    }
  }

  IconData _getColumnTypeIcon(ColumnType type) {
    switch (type) {
      case ColumnType.text: return Icons.text_fields;
      case ColumnType.integer: return Icons.numbers;
      case ColumnType.real: return Icons.numbers;
      case ColumnType.boolean: return Icons.check_box;
      case ColumnType.date: return Icons.calendar_today;
      case ColumnType.datetime: return Icons.access_time;
      case ColumnType.currency: return Icons.attach_money;
      case ColumnType.email: return Icons.email;
      case ColumnType.url: return Icons.link;
      case ColumnType.phone: return Icons.phone;
      case ColumnType.json: return Icons.code;
    }
  }

  Alignment _getCellAlignment(ColumnType type) {
    switch (type) {
      case ColumnType.integer:
      case ColumnType.real:
      case ColumnType.currency:
        return Alignment.centerRight;
      case ColumnType.boolean:
        return Alignment.center;
      default:
        return Alignment.centerLeft;
    }
  }

  TextStyle? _getCellTextStyle(ColumnType type, dynamic value) {
    switch (type) {
      case ColumnType.currency:
        return TextStyle(
          fontWeight: FontWeight.w500,
          color: Colors.green.shade700,
        );
      case ColumnType.email:
      case ColumnType.url:
        return const TextStyle(
          color: Colors.blue,
          decoration: TextDecoration.underline,
        );
      default:
        return null;
    }
  }

  String _formatCellValue(dynamic value, ColumnType type) {
    if (value == null) return '';

    switch (type) {
      case ColumnType.boolean:
        if (value is int) return value == 1 ? 'Yes' : 'No';
        if (value is bool) return value ? 'Yes' : 'No';
        return value.toString().toLowerCase() == 'true' ? 'Yes' : 'No';
      case ColumnType.currency:
        if (value is num) return '\$${value.toStringAsFixed(2)}';
        return value.toString();
      case ColumnType.date:
      case ColumnType.datetime:
        if (value is String) {
          final date = DateTime.tryParse(value);
          if (date != null) {
            if (type == ColumnType.date) {
              return '${date.day}/${date.month}/${date.year}';
            } else {
              return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
            }
          }
        }
        return value.toString();
      default:
        return value.toString();
    }
  }

  TextInputType _getKeyboardType(ColumnType type) {
    switch (type) {
      case ColumnType.integer:
        return TextInputType.number;
      case ColumnType.real:
      case ColumnType.currency:
        return const TextInputType.numberWithOptions(decimal: true);
      case ColumnType.email:
        return TextInputType.emailAddress;
      case ColumnType.url:
        return TextInputType.url;
      case ColumnType.phone:
        return TextInputType.phone;
      default:
        return TextInputType.text;
    }
  }
}
