import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Database Table Editor',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const DatabaseTableEditor(),
    );
  }
}

class DatabaseTableEditor extends StatefulWidget {
  const DatabaseTableEditor({Key? key}) : super(key: key);

  @override
  State<DatabaseTableEditor> createState() => _DatabaseTableEditorState();
}

class _DatabaseTableEditorState extends State<DatabaseTableEditor> {
  late Database _db;
  bool _isLoading = true;
  String _tableName = 'test_table';
  List<Map<String, dynamic>> _data = [];
  List<Map<String, dynamic>> _columns = [];
  
  final TextEditingController _newColumnNameController = TextEditingController();
  final TextEditingController _newColumnTypeController = TextEditingController(text: 'TEXT');
  
  @override
  void initState() {
    super.initState();
    _initDatabase();
  }
  
  @override
  void dispose() {
    _newColumnNameController.dispose();
    _newColumnTypeController.dispose();
    _db.close();
    super.dispose();
  }
  
  Future<void> _initDatabase() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Get the database path
      final dbPath = await getDatabasesPath();
      final path = join(dbPath, 'test_database.db');
      
      // Delete existing database for testing
      await deleteDatabase(path);
      
      // Open the database
      _db = await openDatabase(
        path,
        version: 1,
        onCreate: (db, version) async {
          // Create the test table
          await db.execute('''
            CREATE TABLE $_tableName (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT,
              value INTEGER
            )
          ''');
          
          // Insert some test data
          await db.insert(_tableName, {'name': 'Item 1', 'value': 100});
          await db.insert(_tableName, {'name': 'Item 2', 'value': 200});
          await db.insert(_tableName, {'name': 'Item 3', 'value': 300});
        },
      );
      
      // Load the data
      await _refreshData();
      
    } catch (e) {
      print('Error initializing database: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error initializing database: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _refreshData() async {
    try {
      // Get the table columns
      final columns = await _db.rawQuery("PRAGMA table_info($_tableName)");
      
      // Get the table data
      final data = await _db.query(_tableName);
      
      setState(() {
        _columns = columns;
        _data = data;
      });
    } catch (e) {
      print('Error refreshing data: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error refreshing data: $e')),
      );
    }
  }
  
  Future<void> _addColumn() async {
    final name = _newColumnNameController.text.trim();
    final type = _newColumnTypeController.text.trim().toUpperCase();
    
    if (name.isEmpty || type.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Column name and type are required')),
      );
      return;
    }
    
    try {
      // Check if column already exists
      bool exists = false;
      for (var column in _columns) {
        if (column['name'] == name) {
          exists = true;
          break;
        }
      }
      
      if (exists) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Column $name already exists')),
        );
        return;
      }
      
      // Add the column
      await _db.execute('ALTER TABLE $_tableName ADD COLUMN $name $type');
      
      // Refresh the data
      await _refreshData();
      
      // Clear the text fields
      _newColumnNameController.clear();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Column $name added successfully')),
      );
    } catch (e) {
      print('Error adding column: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding column: $e')),
      );
    }
  }
  
  Future<void> _deleteColumn(String columnName) async {
    try {
      // SQLite doesn't support DROP COLUMN directly, so we need to:
      // 1. Get all columns except the one to delete
      // 2. Create a new table with those columns
      // 3. Copy data from old table to new table
      // 4. Drop old table
      // 5. Rename new table to old table name
      
      // Filter out the column to delete
      List<Map<String, dynamic>> remainingColumns = _columns.where((col) => col['name'] != columnName).toList();
      
      // Create column definitions for new table
      List<String> columnDefs = remainingColumns.map((col) {
        String name = col['name'].toString();
        String type = col['type'].toString();
        bool notNull = col['notnull'] == 1;
        bool primaryKey = col['pk'] == 1;
        
        String def = '$name $type';
        if (notNull) def += ' NOT NULL';
        if (primaryKey) def += ' PRIMARY KEY';
        if (col['dflt_value'] != null) def += ' DEFAULT ${col["dflt_value"]}';
        
        return def;
      }).toList();
      
      // Create new table
      String tempTableName = '${_tableName}_temp';
      await _db.execute('CREATE TABLE $tempTableName (${columnDefs.join(', ')})');
      
      // Get column names for SELECT statement
      List<String> columnNames = remainingColumns.map((col) => col['name'].toString()).toList();
      
      // Copy data
      await _db.execute('INSERT INTO $tempTableName SELECT ${columnNames.join(', ')} FROM $_tableName');
      
      // Drop old table
      await _db.execute('DROP TABLE $_tableName');
      
      // Rename new table
      await _db.execute('ALTER TABLE $tempTableName RENAME TO $_tableName');
      
      // Refresh the data
      await _refreshData();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Column $columnName deleted successfully')),
      );
    } catch (e) {
      print('Error deleting column: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting column: $e')),
      );
    }
  }
  
  Future<void> _addRow() async {
    try {
      // Create a new row with default values
      Map<String, dynamic> newRow = {};
      
      for (var column in _columns) {
        final name = column['name'].toString();
        if (name == 'id') continue; // Skip the ID column
        
        // Set default values based on column type
        final type = column['type'].toString().toUpperCase();
        if (type.contains('INT')) {
          newRow[name] = 0;
        } else if (type.contains('REAL') || type.contains('FLOAT') || type.contains('DOUBLE')) {
          newRow[name] = 0.0;
        } else {
          newRow[name] = '';
        }
      }
      
      // Insert the new row
      await _db.insert(_tableName, newRow);
      
      // Refresh the data
      await _refreshData();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Row added successfully')),
      );
    } catch (e) {
      print('Error adding row: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding row: $e')),
      );
    }
  }
  
  Future<void> _deleteRow(int id) async {
    try {
      // Delete the row
      await _db.delete(
        _tableName,
        where: 'id = ?',
        whereArgs: [id],
      );
      
      // Refresh the data
      await _refreshData();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Row deleted successfully')),
      );
    } catch (e) {
      print('Error deleting row: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting row: $e')),
      );
    }
  }
  
  Future<void> _updateCell(int id, String columnName, String value) async {
    try {
      // Update the cell
      await _db.update(
        _tableName,
        {columnName: value},
        where: 'id = ?',
        whereArgs: [id],
      );
      
      // Refresh the data
      await _refreshData();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Cell updated successfully')),
      );
    } catch (e) {
      print('Error updating cell: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating cell: $e')),
      );
    }
  }
  
  void _showAddColumnDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Column'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _newColumnNameController,
              decoration: const InputDecoration(
                labelText: 'Column Name',
                hintText: 'Enter column name',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _newColumnTypeController,
              decoration: const InputDecoration(
                labelText: 'Column Type',
                hintText: 'TEXT, INTEGER, REAL, etc.',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _addColumn();
            },
            child: const Text('Add Column'),
          ),
        ],
      ),
    );
  }
  
  void _showEditCellDialog(int id, String columnName, dynamic currentValue) {
    final controller = TextEditingController(text: currentValue?.toString() ?? '');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit $columnName'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: columnName,
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateCell(id, columnName, controller.text);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Table Editor - $_tableName'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            tooltip: 'Add Column',
            onPressed: _showAddColumnDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Data',
            onPressed: _refreshData,
          ),
        ],
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : Column(
            children: [
              // Table structure
              Container(
                padding: const EdgeInsets.all(8.0),
                color: Colors.blue.withOpacity(0.1),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Table Structure:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        columns: const [
                          DataColumn(label: Text('Name')),
                          DataColumn(label: Text('Type')),
                          DataColumn(label: Text('Actions')),
                        ],
                        rows: _columns.map((column) => DataRow(
                          cells: [
                            DataCell(Text(column['name'].toString())),
                            DataCell(Text(column['type'].toString())),
                            DataCell(
                              column['pk'] == 1
                                ? const Text('Primary Key')
                                : IconButton(
                                    icon: const Icon(Icons.delete, color: Colors.red),
                                    onPressed: () => _deleteColumn(column['name'].toString()),
                                  ),
                            ),
                          ],
                        )).toList(),
                      ),
                    ),
                  ],
                ),
              ),
              
              const Divider(),
              
              // Table data
              Expanded(
                child: SingleChildScrollView(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: _data.isEmpty
                      ? const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Text('No data available'),
                        )
                      : DataTable(
                          columns: [
                            const DataColumn(label: Text('Actions')),
                            ..._columns.map((column) => 
                              DataColumn(label: Text(column['name'].toString()))
                            ),
                          ],
                          rows: _data.map((row) => DataRow(
                            cells: [
                              DataCell(
                                IconButton(
                                  icon: const Icon(Icons.delete, color: Colors.red),
                                  onPressed: () => _deleteRow(row['id']),
                                ),
                              ),
                              ..._columns.map((column) {
                                final columnName = column['name'].toString();
                                return DataCell(
                                  Text(row[columnName]?.toString() ?? ''),
                                  onTap: () => _showEditCellDialog(
                                    row['id'],
                                    columnName,
                                    row[columnName],
                                  ),
                                );
                              }),
                            ],
                          )).toList(),
                        ),
                  ),
                ),
              ),
            ],
          ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addRow,
        tooltip: 'Add Row',
        child: const Icon(Icons.add),
      ),
    );
  }
}
