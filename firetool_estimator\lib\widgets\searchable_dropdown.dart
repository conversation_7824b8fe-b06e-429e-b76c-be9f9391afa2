import 'package:flutter/material.dart';

class SearchableDropdown<T> extends StatefulWidget {
  final List<T> items;
  final T? value;
  final String Function(T) displayItemFn;
  final String Function(T)? displayCategoryFn;
  final String Function(T)? displayDescriptionFn;
  final Function(T?) onChanged;
  final String hintText;
  final bool isExpanded;
  final bool Function(T, String)? searchMatcher;
  final Widget? icon;
  final String? labelText;
  final bool enabled;
  final bool showCategory;
  final bool showDescription;

  const SearchableDropdown({
    super.key,
    required this.items,
    required this.displayItemFn,
    required this.onChanged,
    this.displayCategoryFn,
    this.displayDescriptionFn,
    this.value,
    this.hintText = 'Select an item',
    this.isExpanded = true,
    this.searchMatcher,
    this.icon,
    this.labelText,
    this.enabled = true,
    this.showCategory = false,
    this.showDescription = false,
  });

  @override
  State<SearchableDropdown<T>> createState() => _SearchableDropdownState<T>();
}

class _SearchableDropdownState<T> extends State<SearchableDropdown<T>> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  List<T> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _filteredItems = List.from(widget.items);
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _openDropdown();
      } else {
        _closeDropdown();
      }
    });
  }

  @override
  void didUpdateWidget(SearchableDropdown<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      _filteredItems = List.from(widget.items);
    }
  }

  @override
  void dispose() {
    _closeDropdown();
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _openDropdown() {
    _isOpen = true;
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _closeDropdown() {
    if (_isOpen) {
      _isOpen = false;
      _overlayEntry?.remove();
      _overlayEntry = null;
    }
  }

  void _filterItems(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(widget.items);
      });
    } else {
      setState(() {
        if (widget.searchMatcher != null) {
          _filteredItems = widget.items
              .where((item) => widget.searchMatcher!(item, query))
              .toList();
        } else {
          _filteredItems = widget.items
              .where((item) {
                final itemName = widget.displayItemFn(item).toLowerCase();
                final searchQuery = query.toLowerCase();

                // Also search in category if available
                String category = '';
                if (widget.displayCategoryFn != null) {
                  category = widget.displayCategoryFn!(item).toLowerCase();
                }

                return itemName.contains(searchQuery) ||
                       category.contains(searchQuery);
              })
              .toList();
        }
      });
    }
    // Update the overlay
    _closeDropdown();
    if (_focusNode.hasFocus) {
      _openDropdown();
    }
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, size.height + 5),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(4),
            child: Container(
              constraints: BoxConstraints(
                maxHeight: 200,
                minWidth: size.width,
              ),
              child: _filteredItems.isEmpty
                  ? const ListTile(
                      title: Text('No items found'),
                      enabled: false,
                    )
                  : ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemCount: _filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = _filteredItems[index];
                        return ListTile(
                          title: Text(widget.displayItemFn(item)),
                          subtitle: _buildSubtitle(item),
                          selected: widget.value == item,
                          onTap: () {
                            // Call onChanged with the selected item
                            widget.onChanged(item);

                            // Update the text field with the selected item's name
                            _searchController.text = widget.displayItemFn(item);

                            // Close the dropdown and unfocus
                            Future.delayed(const Duration(milliseconds: 100), () {
                              _closeDropdown();
                              _focusNode.unfocus();
                            });
                          },
                        );
                      },
                    ),
            ),
          ),
        ),
      ),
    );
  }

  Widget? _buildSubtitle(T item) {
    if (!widget.showCategory && !widget.showDescription) {
      return null;
    }

    String? categoryText;
    String? descriptionText;

    if (widget.showCategory && widget.displayCategoryFn != null) {
      categoryText = widget.displayCategoryFn!(item);
    }

    if (widget.showDescription && widget.displayDescriptionFn != null) {
      descriptionText = widget.displayDescriptionFn!(item);
    }

    if (categoryText != null && descriptionText != null) {
      return Text('$categoryText - $descriptionText');
    } else if (categoryText != null) {
      return Text(categoryText);
    } else if (descriptionText != null) {
      return Text(descriptionText);
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: _searchController,
        focusNode: _focusNode,
        decoration: InputDecoration(
          hintText: widget.hintText,
          labelText: widget.labelText,
          suffixIcon: widget.icon ?? const Icon(Icons.arrow_drop_down),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        ),
        onChanged: _filterItems,
        enabled: widget.enabled,
        readOnly: true, // Make it read-only to force dropdown on tap
        onTap: () {
          if (!_isOpen) {
            _openDropdown();
          } else {
            _closeDropdown();
          }
        },
      ),
    );
  }
}
