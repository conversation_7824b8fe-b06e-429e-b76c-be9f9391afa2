import 'package:hive_flutter/hive_flutter.dart';
import 'package:uuid/uuid.dart';

class DatabaseService {
  static const String _tablesBoxName = 'tables';
  static const String _dataBoxPrefix = 'data_';
  static const String _metadataBoxName = 'metadata';

  final Uuid _uuid = const Uuid();

  // Get or create a Hive box for table metadata
  Future<Box<Map>> _getTablesBox() async {
    if (!Hive.isBoxOpen(_tablesBoxName)) {
      return await Hive.openBox<Map>(_tablesBoxName);
    }
    return Hive.box<Map>(_tablesBoxName);
  }

  // Get or create a Hive box for table data
  Future<Box<Map>> _getDataBox(String tableName) async {
    final boxName = '$_dataBoxPrefix$tableName';
    if (!Hive.isBoxOpen(boxName)) {
      return await Hive.openBox<Map>(boxName);
    }
    return Hive.box<Map>(boxName);
  }

  // Get table schema
  Future<Map<String, String>?> getTableSchema(String tableName) async {
    try {
      final tablesBox = await _getTablesBox();
      final schema = tablesBox.get(tableName);
      if (schema != null) {
        return Map<String, String>.from(schema);
      }

      // If table doesn't exist, create it with default schema
      await _createDefaultTable(tableName);
      return await getTableSchema(tableName);
    } catch (e) {
      print('Error getting table schema: $e');
      return null;
    }
  }

  // Create default table with basic columns
  Future<void> _createDefaultTable(String tableName) async {
    final defaultSchema = {
      '_id': 'TEXT',
      'name': 'TEXT',
      'description': 'TEXT',
      'quantity': 'INTEGER',
      'unit_price': 'DOUBLE',
      'total_price': 'DOUBLE',
    };

    final tablesBox = await _getTablesBox();
    await tablesBox.put(tableName, defaultSchema);
  }

  // Get all data from a table
  Future<List<Map<String, dynamic>>> getTableData(String tableName) async {
    try {
      final dataBox = await _getDataBox(tableName);
      final List<Map<String, dynamic>> result = [];

      for (int i = 0; i < dataBox.length; i++) {
        final data = dataBox.getAt(i);
        if (data != null) {
          result.add(Map<String, dynamic>.from(data));
        }
      }

      return result;
    } catch (e) {
      print('Error getting table data: $e');
      return [];
    }
  }

  // Insert a new row
  Future<void> insertRow(String tableName, Map<String, dynamic> data) async {
    try {
      // Add unique ID if not present
      if (!data.containsKey('_id')) {
        data['_id'] = _uuid.v4();
      }

      final dataBox = await _getDataBox(tableName);
      await dataBox.add(data);
    } catch (e) {
      print('Error inserting row: $e');
      rethrow;
    }
  }

  // Update a cell
  Future<void> updateCell(String tableName, String id, String column, dynamic value) async {
    try {
      final dataBox = await _getDataBox(tableName);

      for (int i = 0; i < dataBox.length; i++) {
        final data = dataBox.getAt(i);
        if (data != null && data['_id'] == id) {
          final updatedData = Map<String, dynamic>.from(data);
          updatedData[column] = value;
          await dataBox.putAt(i, updatedData);
          return;
        }
      }
    } catch (e) {
      print('Error updating cell: $e');
      rethrow;
    }
  }

  // Delete a row
  Future<void> deleteRow(String tableName, String id) async {
    try {
      final dataBox = await _getDataBox(tableName);

      for (int i = 0; i < dataBox.length; i++) {
        final data = dataBox.getAt(i);
        if (data != null && data['_id'] == id) {
          await dataBox.deleteAt(i);
          return;
        }
      }
    } catch (e) {
      print('Error deleting row: $e');
      rethrow;
    }
  }

  // Add a new column to a table
  Future<void> addColumn(String tableName, String columnName, String columnType) async {
    try {
      final tablesBox = await _getTablesBox();
      final schema = tablesBox.get(tableName);

      if (schema != null) {
        final updatedSchema = Map<String, String>.from(schema);
        updatedSchema[columnName] = columnType;
        await tablesBox.put(tableName, updatedSchema);

        // Update existing rows to include the new column with default value
        final dataBox = await _getDataBox(tableName);
        for (int i = 0; i < dataBox.length; i++) {
          final data = dataBox.getAt(i);
          if (data != null) {
            final updatedData = Map<String, dynamic>.from(data);
            if (!updatedData.containsKey(columnName)) {
              updatedData[columnName] = _getDefaultValue(columnType);
              await dataBox.putAt(i, updatedData);
            }
          }
        }
      }
    } catch (e) {
      print('Error adding column: $e');
      rethrow;
    }
  }

  // Get default value for a column type
  dynamic _getDefaultValue(String type) {
    switch (type.toUpperCase()) {
      case 'INTEGER':
        return 0;
      case 'DOUBLE':
        return 0.0;
      case 'BOOLEAN':
        return false;
      case 'DATE':
        return DateTime.now().toIso8601String();
      default:
        return '';
    }
  }

  // Close all Hive boxes (cleanup method)
  Future<void> closeAllDatabases() async {
    await Hive.close();
  }
}
