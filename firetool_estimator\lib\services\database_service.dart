import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:sqflite/sqflite.dart';
import '../models/project.dart';

// For desktop platforms, sqflite_common_ffi is needed.
// This basic setup assumes mobile or web where sqflite works directly.
// If targeting desktop, additional setup for sqflite_common_ffi would be required.

class DatabaseService {
  // Store open database instances, keyed by section name or a unique ID for the DB
  final Map<String, Database> _openDatabases = {};

  Future<String> _getDbPath(String sectionName) async {
    // Sanitize sectionName to be a valid filename
    final String sanitizedSectionName = _sanitizeFileName(sectionName);
    final Directory appDocumentsDir = await getApplicationDocumentsDirectory();
    final String dbDirectoryPath = p.join(appDocumentsDir.path, 'super_database_sections');
    final String dbPath = p.join(dbDirectoryPath, '$sanitizedSectionName.db');

    // Ensure the base directory for section databases exists
    final Directory dbDir = Directory(dbDirectoryPath);
    if (!await dbDir.exists()) {
      await dbDir.create(recursive: true);
    }
    return dbPath;
  }

  String _sanitizeFileName(String input) {
    // Replace spaces and special characters with underscores
    // Allow alphanumeric, underscore, hyphen.
    String sanitized = input.replaceAll(RegExp(r'\s+'), '_');
    sanitized = sanitized.replaceAll(RegExp(r'[^\w\s-]|(\s(?=\s))'), ''); // Keep hyphens
    return sanitized.toLowerCase();
  }

  /// Opens an existing database for a section or creates it if it doesn't exist.
  /// Each section will have its own .db file.
  Future<Database> openDatabaseForSection(String sectionName) async {
    final String sanitizedSectionName = _sanitizeFileName(sectionName); // Use the original sectionName for map key
    if (_openDatabases.containsKey(sanitizedSectionName) && _openDatabases[sanitizedSectionName]!.isOpen) {
      return _openDatabases[sanitizedSectionName]!;
    }

    final String dbPath = await _getDbPath(sectionName); // sectionName here will be sanitized by _getDbPath

    if (kDebugMode) {
      print('Opening/Creating database for section "$sectionName" at path: $dbPath');
    }

    final Database database = await openDatabase(
      dbPath, // Use the path returned by _getDbPath
      version: 1, // Initial version
      onCreate: (db, version) async {
        // Called if the database did not exist prior to calling openDatabase.
        if (kDebugMode) {
          print('Database for section "$sectionName" created (path: $dbPath)');
        }
        // We don't create any tables here by default.
        // Tables are created dynamically based on Excel import or user action.
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        // Handle database schema upgrades if version increases.
        // For dynamic tables, this might involve more complex migration logic
        // if we start versioning table structures themselves.
        if (kDebugMode) {
          print('Upgrading database for "$sectionName" from $oldVersion to $newVersion');
        }
      },
    );

    _openDatabases[sanitizedSectionName] = database;
    return database;
  }

  Future<void> closeDatabaseForSection(String sectionName) async {
    final String sanitizedSectionName = _sanitizeFileName(sectionName);
    if (_openDatabases.containsKey(sanitizedSectionName)) {
      final db = _openDatabases[sanitizedSectionName]!;
      if (db.isOpen) {
        await db.close();
      }
      _openDatabases.remove(sanitizedSectionName);
      if (kDebugMode) {
        print('Database for section "$sectionName" closed.');
      }
    }
  }

  Future<void> closeAllDatabases() async {
    for (final sectionName in _openDatabases.keys.toList()) {
      await closeDatabaseForSection(sectionName);
    }
    if (kDebugMode) {
      print('All open section databases closed.');
    }
  }

  // Placeholder for listing tables (subsections) in a given section's database
  Future<List<String>> listTables(String sectionName) async {
    final db = await openDatabaseForSection(sectionName);
    // Query sqlite_master for tables
    final List<Map<String, dynamic>> tables = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'android_%' AND name NOT LIKE 'sqlite_%';"
    );
    return tables.map((row) => row['name'] as String).toList();
  }

  /// Creates a new table in the specified database.
  /// Columns is a list of maps, e.g., [{'name': 'col_name', 'type': 'TEXT'}]
  /// An '_id' INTEGER PRIMARY KEY AUTOINCREMENT column is added automatically.
  Future<void> createTable({
    required String sectionName,
    required String tableName,
    required List<Map<String, String>> columns,
  }) async {
    final db = await openDatabaseForSection(sectionName);
    final String sanitizedTableName = _sanitizeTableName(tableName);

    // Check if table already exists
    List<Map<String, dynamic>> existingTables = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
      [sanitizedTableName],
    );

    if (existingTables.isNotEmpty) {
      if (kDebugMode) {
        print("Table '$sanitizedTableName' already exists in section '$sectionName'. Skipping creation.");
      }
      // Optionally, could throw an error or return a status
      return;
    }

    StringBuffer sql = StringBuffer('CREATE TABLE "$sanitizedTableName" (_id INTEGER PRIMARY KEY AUTOINCREMENT');
    for (var column in columns) {
      final String colName = _sanitizeColumnName(column['name']!);
      final String colType = _validateSqliteType(column['type']!);
      sql.write(', "$colName" $colType');
    }
    sql.write(')');

    if (kDebugMode) {
      print('Executing SQL for section "$sectionName": ${sql.toString()}');
    }
    await db.execute(sql.toString());
    if (kDebugMode) {
      print("Table '$sanitizedTableName' created in section '$sectionName'.");
    }
  }

  String _sanitizeTableName(String input) {
    // Similar to _sanitizeFileName, but table names might have different constraints
    // For SQLite, it's generally flexible but avoid spaces and special chars for simplicity
    String sanitized = input.replaceAll(RegExp(r'\s+'), '_');
    sanitized = sanitized.replaceAll(RegExp(r'[^\w\s-]|(\s(?=\s))'), '');
    return sanitized; // Table names can be case sensitive in SQLite depending on context, often best to normalize (e.g. toLower)
  }

  String _sanitizeColumnName(String input) {
    // Column names also have constraints, avoid keywords, spaces, special chars
    String sanitized = input.replaceAll(RegExp(r'\s+'), '_');
    sanitized = sanitized.replaceAll(RegExp(r'[^\w\s-]|(\s(?=\s))'), '');
    // SQLite column names are case-insensitive in practice, but it's good to be consistent.
    return sanitized;
  }

  String _validateSqliteType(String inputType) {
    // Ensure the type is one of SQLite's supported types
    final upperType = inputType.toUpperCase();
    switch (upperType) {
      case 'TEXT':
      case 'INTEGER':
      case 'REAL':
      case 'BLOB':
      case 'NUMERIC': // NUMERIC can store any type
        return upperType;
      default:
        if (kDebugMode) {
          print("Warning: Invalid SQLite type '$inputType'. Defaulting to TEXT.");
        }
        return 'TEXT'; // Default to TEXT if type is unrecognized
    }
  }

  /// Inserts a new row into the specified table for a given section.
  /// Data is a map of column names to values.
  /// Returns the ID of the newly inserted row.
  Future<int> insertRow({
    required String sectionName,
    required String tableName,
    required Map<String, dynamic> data,
  }) async {
    final db = await openDatabaseForSection(sectionName);
    final String sanitizedTableName = _sanitizeTableName(tableName);

    // Sanitize column names in the data map
    final Map<String, dynamic> sanitizedData = {};
    data.forEach((key, value) {
      sanitizedData[_sanitizeColumnName(key)] = value;
    });

    // Ensure '_id' is not in the data to be inserted, as it's auto-incrementing
    sanitizedData.remove('_id');

    if (kDebugMode) {
      print("Inserting row into '$sanitizedTableName' in section '$sectionName': $sanitizedData");
    }

    try {
      final id = await db.insert(
        '"$sanitizedTableName"', // Ensure table name is quoted
        sanitizedData,
        conflictAlgorithm: ConflictAlgorithm.replace, // Or .ignore, .fail, etc.
      );
      if (kDebugMode) {
        print("Row inserted with ID: $id into '$sanitizedTableName'");
      }
      return id;
    } catch (e) {
      if (kDebugMode) {
        print("Error inserting row into '$sanitizedTableName': $e");
        print("Data: $sanitizedData");
      }
      // Consider re-throwing or returning an error indicator e.g. -1
      rethrow;
    }
  }

  // More methods will be added here for:
  // - get_table_schema(sectionName, tableName)
  // - update_row(sectionName, tableName, id, data)
  // - delete_row(sectionName, tableName, id)
  // - add_column(sectionName, tableName, columnName, columnType)
  // - rename_column(sectionName, tableName, oldName, newName)
  // - delete_column(sectionName, tableName, columnName)
  // - change_column_type(db, tableName, columnName, newType)

  /// Retrieves rows from a table with support for pagination, sorting, and basic filtering.
  Future<List<Map<String, dynamic>>> getRows({
    required String sectionName,
    required String tableName,
    int? limit,
    int? offset,
    String? sortByColumn,
    bool sortAscending = true,
    String? filterColumn,
    String? filterValue, // Basic 'LIKE %value%' filter for now
  }) async {
    final db = await openDatabaseForSection(sectionName);
    final String sanitizedTableName = _sanitizeTableName(tableName);
    final String? sanitizedSortByColumn = sortByColumn != null ? _sanitizeColumnName(sortByColumn) : null;
    final String? sanitizedFilterColumn = filterColumn != null ? _sanitizeColumnName(filterColumn) : null;

    StringBuffer query = StringBuffer('SELECT * FROM "$sanitizedTableName"');

    List<dynamic> queryArgs = [];

    if (sanitizedFilterColumn != null && filterValue != null && filterValue.isNotEmpty) {
      query.write(' WHERE "$sanitizedFilterColumn" LIKE ?');
      queryArgs.add('%$filterValue%');
    }

    if (sanitizedSortByColumn != null) {
      query.write(' ORDER BY "$sanitizedSortByColumn" ${sortAscending ? "ASC" : "DESC"}');
    }

    if (limit != null) {
      query.write(' LIMIT ?');
      queryArgs.add(limit);
    }

    if (offset != null) {
      query.write(' OFFSET ?');
      queryArgs.add(offset);
    }

    if (kDebugMode) {
      print("Executing query on '$sanitizedTableName': ${query.toString()} with args: $queryArgs");
    }

    try {
      final List<Map<String, dynamic>> result = await db.rawQuery(query.toString(), queryArgs);
      return result;
    } catch (e) {
      if (kDebugMode) {
        print("Error getting rows from '$sanitizedTableName': $e");
      }
      return []; // Return empty list on error or rethrow
    }
  }

  // Project management methods
  Future<List<Project>> getAllProjects() async {
    // For now, return empty list - this would need proper implementation
    // based on your project storage requirements
    return [];
  }

  Future<void> insertProject(Project project) async {
    // Placeholder implementation
    if (kDebugMode) {
      print('Insert project: ${project.name}');
    }
  }

  Future<Project?> getProject(String projectId) async {
    // Placeholder implementation
    if (kDebugMode) {
      print('Get project: $projectId');
    }
    return null;
  }

  Future<void> updateProject(Project project) async {
    // Placeholder implementation
    if (kDebugMode) {
      print('Update project: ${project.name}');
    }
  }

  Future<void> deleteProject(String projectId) async {
    // Placeholder implementation
    if (kDebugMode) {
      print('Delete project: $projectId');
    }
  }

  // Material management methods
  Future<List<MaterialItem>> getAllMaterials() async {
    // For now, return empty list - this would need proper implementation
    return [];
  }

  Future<void> insertMaterial(MaterialItem material) async {
    // Placeholder implementation
    if (kDebugMode) {
      print('Insert material: ${material.name}');
    }
  }
}
