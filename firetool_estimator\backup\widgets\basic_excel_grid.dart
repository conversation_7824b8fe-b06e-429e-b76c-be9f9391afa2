import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class BasicExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const BasicExcelGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  });

  @override
  State<BasicExcelGrid> createState() => _BasicExcelGridState();
}

class _BasicExcelGridState extends State<BasicExcelGrid> {
  // Data state
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _filteredItems = [];
  List<String> _columns = [];
  bool _isLoading = true;
  String? _error;

  // Selection state
  int? _selectedRow;
  int? _selectedCol;
  final Set<String> _selectedCells = {}; // Format: "row:col"
  bool _isSelecting = false;
  int? _selectionStartRow;
  int? _selectionStartCol;

  // Editing state
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, FocusNode> _focusNodes = {};
  final FocusNode _gridFocusNode = FocusNode();

  // Search
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Initialize columns with predefined columns if provided
    if (widget.predefinedColumns != null) {
      _columns = List.from(widget.predefinedColumns!);
    } else {
      _columns = [
        'model',
        'description',
        'manufacturer',
        'approval',
        'ex_works_price',
        'local_price',
        'installation_price',
      ];
    }

    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _gridFocusNode.dispose();

    // Dispose all controllers and focus nodes
    for (var controller in _controllers.values) {
      controller.dispose();
    }

    for (var focusNode in _focusNodes.values) {
      focusNode.dispose();
    }

    super.dispose();
  }

  // Data loading and filtering
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final loadedItems = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Discover all columns from data
      final Set<String> columnSet = {'id'};

      // Add predefined columns
      columnSet.addAll(_columns);

      // Add any additional columns from the data
      for (var item in loadedItems) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      setState(() {
        _items = loadedItems;
        _filteredItems = List.from(loadedItems);
        _columns = columnSet.toList();
        _isLoading = false;

        // Initialize controllers and focus nodes for all cells
        _initControllersAndFocusNodes();
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _initControllersAndFocusNodes() {
    // Clear existing controllers and focus nodes
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();

    for (var focusNode in _focusNodes.values) {
      focusNode.dispose();
    }
    _focusNodes.clear();

    // Create controllers and focus nodes for each cell
    for (var rowIndex = 0; rowIndex < _filteredItems.length; rowIndex++) {
      for (var colIndex = 0; colIndex < _columns.length; colIndex++) {
        final cellKey = '$rowIndex:$colIndex';
        final column = _columns[colIndex];
        final value = _filteredItems[rowIndex][column]?.toString() ?? '';

        _controllers[cellKey] = TextEditingController(text: value);

        final focusNode = FocusNode();
        focusNode.addListener(() {
          if (focusNode.hasFocus) {
            _selectCell(rowIndex, colIndex);
          }
        });
        _focusNodes[cellKey] = focusNode;
      }
    }
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(_items);
        _initControllersAndFocusNodes();
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    setState(() {
      _filteredItems = _items.where((item) {
        // Search in all columns
        for (var column in _columns) {
          final value = item[column]?.toString().toLowerCase() ?? '';
          if (value.contains(lowercaseQuery)) {
            return true;
          }
        }
        return false;
      }).toList();

      _initControllersAndFocusNodes();
    });
  }

  // Cell selection and editing
  void _selectCell(int row, int col) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _selectedRow = row;
      _selectedCol = col;

      // Clear selection if not in selection mode
      if (!_isSelecting) {
        _selectedCells.clear();
        _selectedCells.add('$row:$col');
      }
    });

    // Focus the cell
    final cellKey = '$row:$col';
    if (_focusNodes.containsKey(cellKey)) {
      _focusNodes[cellKey]!.requestFocus();
    }
  }

  void _startSelecting(int row, int col) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _selectedRow = row;
      _selectedCol = col;
      _selectionStartRow = row;
      _selectionStartCol = col;
      _isSelecting = true;

      // Clear previous selection
      _selectedCells.clear();
      _selectedCells.add('$row:$col');
    });
  }

  void _updateSelection(int row, int col) {
    if (!_isSelecting || _selectionStartRow == null || _selectionStartCol == null) {
      return;
    }

    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      // Clear previous selection
      _selectedCells.clear();

      // Calculate selection rectangle
      final startRow = _selectionStartRow! < row ? _selectionStartRow! : row;
      final endRow = _selectionStartRow! < row ? row : _selectionStartRow!;
      final startCol = _selectionStartCol! < col ? _selectionStartCol! : col;
      final endCol = _selectionStartCol! < col ? col : _selectionStartCol!;

      // Add all cells in the rectangle to selection
      for (var r = startRow; r <= endRow; r++) {
        for (var c = startCol; c <= endCol; c++) {
          _selectedCells.add('$r:$c');
        }
      }
    });
  }

  void _endSelecting() {
    setState(() {
      _isSelecting = false;
    });
  }

  void _updateCellValue(int row, int col, String newValue) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    final column = _columns[col];
    final itemId = _filteredItems[row]['id'] as String;
    final oldValue = _filteredItems[row][column]?.toString() ?? '';

    // Skip update if value hasn't changed
    if (newValue == oldValue) {
      return;
    }

    // Update in Firestore
    FirebaseFirestore.instance
        .collection(widget.collectionPath)
        .doc(itemId)
        .update({column: newValue})
        .then((_) {
          // Update local data
          setState(() {
            _filteredItems[row][column] = newValue;

            // Also update in _items
            final index = _items.indexWhere((item) => item['id'] == itemId);
            if (index >= 0) {
              _items[index][column] = newValue;
            }
          });
        })
        .catchError((error) {
          _showSnackBar('Error updating cell: $error', isError: true);
        });
  }

  void _clearSelectedCells() {
    // Prepare batch update
    final batch = FirebaseFirestore.instance.batch();
    final updates = <int, Map<String, dynamic>>{};

    // Process each selected cell
    for (var cellKey in _selectedCells) {
      final parts = cellKey.split(':');
      final row = int.parse(parts[0]);
      final col = int.parse(parts[1]);

      if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
        continue;
      }

      final column = _columns[col];
      if (column == 'id') continue;

      final itemId = _filteredItems[row]['id'] as String;

      // Add to updates
      if (!updates.containsKey(row)) {
        updates[row] = {'id': itemId};
      }
      updates[row]![column] = '';

      // Update controller
      final controllerKey = '$row:$col';
      if (_controllers.containsKey(controllerKey)) {
        _controllers[controllerKey]!.text = '';
      }
    }

    // Apply updates to Firestore
    for (var entry in updates.entries) {
      final row = entry.key;
      final rowUpdates = entry.value;
      final itemId = rowUpdates['id'] as String;
      rowUpdates.remove('id');

      if (rowUpdates.isNotEmpty) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(itemId);

        batch.update(docRef, rowUpdates);

        // Update local data
        for (var key in rowUpdates.keys) {
          _filteredItems[row][key] = '';

          // Also update in _items
          final index = _items.indexWhere((item) => item['id'] == itemId);
          if (index >= 0) {
            _items[index][key] = '';
          }
        }
      }
    }

    // Commit batch
    batch.commit().then((_) {
      _showSnackBar('Cells cleared');
    }).catchError((error) {
      _showSnackBar('Error clearing cells: $error', isError: true);
    });
  }

  void _moveSelection(int rowDelta, int colDelta) {
    if (_selectedRow == null || _selectedCol == null) {
      _selectCell(0, 0);
      return;
    }

    final newRow = (_selectedRow! + rowDelta).clamp(0, _filteredItems.length - 1);
    final newCol = (_selectedCol! + colDelta).clamp(0, _columns.length - 1);

    _selectCell(newRow, newCol);
  }

  // Add new row
  Future<void> _addNewRow() async {
    final Map<String, dynamic> newItem = {};

    // Add default empty values for all columns
    for (var column in _columns) {
      if (column != 'id') {
        newItem[column] = '';
      }
    }

    newItem['createdAt'] = FieldValue.serverTimestamp();

    try {
      final docRef = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Add to local data with the new ID
      final newItemWithId = {
        'id': docRef.id,
        ...newItem,
      };

      setState(() {
        _items.add(newItemWithId);
        _filteredItems.add(newItemWithId);

        // Add controllers and focus nodes for the new row
        final rowIndex = _filteredItems.length - 1;
        for (var colIndex = 0; colIndex < _columns.length; colIndex++) {
          final cellKey = '$rowIndex:$colIndex';
          final column = _columns[colIndex];

          _controllers[cellKey] = TextEditingController(text: newItemWithId[column]?.toString() ?? '');

          final focusNode = FocusNode();
          focusNode.addListener(() {
            if (focusNode.hasFocus) {
              _selectCell(rowIndex, colIndex);
            }
          });
          _focusNodes[cellKey] = focusNode;
        }
      });

      _showSnackBar('New row added');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  // Delete row
  Future<void> _deleteRow(int rowIndex) async {
    if (rowIndex < 0 || rowIndex >= _filteredItems.length) {
      return;
    }

    final itemId = _filteredItems[rowIndex]['id'] as String;

    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(itemId)
          .delete();

      setState(() {
        _filteredItems.removeAt(rowIndex);
        _items.removeWhere((item) => item['id'] == itemId);

        // Remove controllers and focus nodes for the deleted row and update keys for subsequent rows
        final newControllers = <String, TextEditingController>{};
        final newFocusNodes = <String, FocusNode>{};

        for (var key in _controllers.keys) {
          final parts = key.split(':');
          final row = int.parse(parts[0]);
          final col = int.parse(parts[1]);

          if (row < rowIndex) {
            // Keep controllers for rows before the deleted row
            newControllers[key] = _controllers[key]!;
            newFocusNodes[key] = _focusNodes[key]!;
          } else if (row > rowIndex) {
            // Update keys for rows after the deleted row
            final newKey = '${row - 1}:$col';
            newControllers[newKey] = _controllers[key]!;
            newFocusNodes[newKey] = _focusNodes[key]!;
          } else {
            // Dispose controllers and focus nodes for the deleted row
            _controllers[key]!.dispose();
            _focusNodes[key]!.dispose();
          }
        }

        _controllers.clear();
        _controllers.addAll(newControllers);

        _focusNodes.clear();
        _focusNodes.addAll(newFocusNodes);

        // Reset selection if the selected row was deleted
        if (_selectedRow == rowIndex) {
          _selectedRow = null;
          _selectedCol = null;
          _selectedCells.clear();
        } else if (_selectedRow != null && _selectedRow! > rowIndex) {
          // Adjust selection if a row above was deleted
          _selectedRow = _selectedRow! - 1;
        }
      });

      _showSnackBar('Row deleted');
    } catch (e) {
      _showSnackBar('Error deleting row: $e', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  String _getDisplayName(String column) {
    // Convert snake_case to Title Case
    return column.split('_').map((word) =>
      word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  // Build UI
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        actions: [
          // Add row button
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Row',
            onPressed: _addNewRow,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterData('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: _filterData,
            ),
          ),

          // Data grid
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator(color: widget.themeColor))
                : _error != null
                    ? _buildErrorWidget()
                    : _buildExcelGrid(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewRow,
        backgroundColor: widget.themeColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildExcelGrid() {
    return Focus(
      focusNode: _gridFocusNode,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
            _moveSelection(-1, 0);
            return KeyEventResult.handled;
          } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
            _moveSelection(1, 0);
            return KeyEventResult.handled;
          } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
            _moveSelection(0, -1);
            return KeyEventResult.handled;
          } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
            _moveSelection(0, 1);
            return KeyEventResult.handled;
          } else if (event.logicalKey == LogicalKeyboardKey.tab) {
            _moveSelection(0, HardwareKeyboard.instance.isShiftPressed ? -1 : 1);
            return KeyEventResult.handled;
          } else if (event.logicalKey == LogicalKeyboardKey.enter) {
            _moveSelection(1, 0);
            return KeyEventResult.handled;
          } else if (event.logicalKey == LogicalKeyboardKey.delete ||
                    event.logicalKey == LogicalKeyboardKey.backspace) {
            _clearSelectedCells();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  // Empty corner cell
                  Container(
                    width: 60,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      border: Border.all(color: Colors.grey.shade400),
                    ),
                    child: Center(
                      child: IconButton(
                        icon: const Icon(Icons.add, color: Colors.green),
                        tooltip: 'Add Row',
                        onPressed: _addNewRow,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ),
                  ),

                  // Column headers
                  ..._columns.map((column) => Container(
                    width: 150,
                    height: 40,
                    decoration: BoxDecoration(
                      color: widget.themeColor,
                      border: Border.all(color: Colors.grey.shade400),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      _getDisplayName(column),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  )),
                ],
              ),

              // Data rows
              ..._filteredItems.asMap().entries.map((entry) {
                final rowIndex = entry.key;
                final item = entry.value;

                return Row(
                  children: [
                    // Row header
                    Container(
                      width: 60,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        border: Border.all(color: Colors.grey.shade400),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 4),
                            child: Text(
                              '${rowIndex + 1}',
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                            onPressed: () => _deleteRow(rowIndex),
                            tooltip: 'Delete Row',
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                    ),

                    // Data cells
                    ..._columns.asMap().entries.map((colEntry) {
                      final colIndex = colEntry.key;
                      final column = colEntry.value;

                      return _buildCell(item, column, rowIndex, colIndex);
                    }),
                  ],
                );
              }),

              // Add row button
              Row(
                children: [
                  Container(
                    width: 60,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      border: Border.all(color: Colors.grey.shade400),
                    ),
                    child: Center(
                      child: IconButton(
                        icon: const Icon(Icons.add, color: Colors.green),
                        tooltip: 'Add Row',
                        onPressed: _addNewRow,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ),
                  ),

                  Container(
                    width: _columns.length * 150,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      border: Border.all(color: Colors.grey.shade400),
                    ),
                    child: Center(
                      child: TextButton.icon(
                        icon: const Icon(Icons.add),
                        label: const Text('Add New Row'),
                        onPressed: _addNewRow,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCell(Map<String, dynamic> item, String column, int rowIndex, int colIndex) {
    final isSelected = _selectedCells.contains('$rowIndex:$colIndex');
    final isFocused = _selectedRow == rowIndex && _selectedCol == colIndex;
    final cellKey = '$rowIndex:$colIndex';

    // Ensure we have a controller and focus node for this cell
    if (!_controllers.containsKey(cellKey)) {
      _controllers[cellKey] = TextEditingController(text: item[column]?.toString() ?? '');

      final focusNode = FocusNode();
      focusNode.addListener(() {
        if (focusNode.hasFocus) {
          _selectCell(rowIndex, colIndex);
        }
      });
      _focusNodes[cellKey] = focusNode;
    }

    return GestureDetector(
      onTap: () {
        _selectCell(rowIndex, colIndex);
        // Focus the text field immediately
        _focusNodes[cellKey]!.requestFocus();
      },
      onPanStart: (details) {
        _startSelecting(rowIndex, colIndex);
      },
      onPanUpdate: (details) {
        // Calculate which cell we're over based on the position
        final RenderBox box = context.findRenderObject() as RenderBox;
        final position = box.globalToLocal(details.globalPosition);

        // Simple calculation for cell position
        final cellWidth = 150.0;
        final cellHeight = 40.0;
        final headerWidth = 60.0;
        final headerHeight = 40.0;

        final cellRow = position.dy < headerHeight
            ? null
            : ((position.dy - headerHeight) / cellHeight).floor();

        final cellCol = position.dx < headerWidth
            ? null
            : ((position.dx - headerWidth) / cellWidth).floor();

        if (cellRow != null && cellCol != null &&
            cellRow >= 0 && cellRow < _filteredItems.length &&
            cellCol >= 0 && cellCol < _columns.length) {
          _updateSelection(cellRow, cellCol);
        }
      },
      onPanEnd: (_) {
        _endSelecting();
      },
      child: Container(
        width: 150,
        height: 40,
        decoration: BoxDecoration(
          color: isFocused
              ? widget.themeColor.withAlpha(51) // 0.2 * 255 = 51
              : isSelected
                  ? widget.themeColor.withAlpha(26) // 0.1 * 255 = 26
                  : Colors.white,
          border: Border.all(
            color: isFocused || isSelected
                ? widget.themeColor
                : Colors.grey.shade400,
            width: isFocused ? 2 : 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        alignment: Alignment.centerLeft,
        child: TextField(
          controller: _controllers[cellKey],
          focusNode: _focusNodes[cellKey],
          decoration: const InputDecoration(
            border: InputBorder.none,
            isDense: true,
            contentPadding: EdgeInsets.zero,
          ),
          style: const TextStyle(
            fontSize: 14,
          ),
          onChanged: (value) {
            _updateCellValue(rowIndex, colIndex, value);
          },
          onSubmitted: (value) {
            // Move to the next cell when Enter is pressed
            _moveSelection(1, 0);
          },
        ),
      ),
    );
  }
}
