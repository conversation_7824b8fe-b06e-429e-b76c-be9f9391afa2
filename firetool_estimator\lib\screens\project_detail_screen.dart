import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';
import '../models/project.dart';
import '../services/project_provider.dart';
import '../services/export_service.dart';
import 'system_detail_screen.dart';
import 'add_system_screen.dart';

class ProjectDetailScreen extends StatefulWidget {
  const ProjectDetailScreen({super.key});

  @override
  State<ProjectDetailScreen> createState() => _ProjectDetailScreenState();
}

class _ProjectDetailScreenState extends State<ProjectDetailScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ExportService _exportService = ExportService();

  // Get currency formatter with the correct symbol
  NumberFormat getCurrencyFormatter(Project project) {
    return NumberFormat.currency(symbol: project.currency);
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final project = projectProvider.currentProject;

        if (project == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Project Details')),
            body: const Center(child: Text('No project loaded')),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(project.name),
            actions: [
              IconButton(
                icon: const Icon(Icons.save),
                tooltip: 'Save Project',
                onPressed: () async {
                  await projectProvider.saveCurrentProject();
                  if (!mounted) return;

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Project saved')),
                  );
                },
              ),
              PopupMenuButton<String>(
                onSelected: (value) async {
                  switch (value) {
                    case 'export_pdf':
                      try {
                        final file = await _exportService.exportToPdf(project);
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('PDF exported to ${file.path}')),
                        );
                      } catch (e) {
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Error exporting PDF: $e')),
                        );
                      }
                      break;
                    case 'export_excel':
                      try {
                        final file = await _exportService.exportToExcel(project);
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Excel exported to ${file.path}')),
                        );
                      } catch (e) {
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Error exporting Excel: $e')),
                        );
                      }
                      break;
                    case 'print':
                      try {
                        await _exportService.printPdf(project);
                      } catch (e) {
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Error printing: $e')),
                        );
                      }
                      break;
                  }
                },
                itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                  const PopupMenuItem<String>(
                    value: 'export_pdf',
                    child: ListTile(
                      leading: Icon(Icons.picture_as_pdf),
                      title: Text('Export to PDF'),
                    ),
                  ),
                  const PopupMenuItem<String>(
                    value: 'export_excel',
                    child: ListTile(
                      leading: Icon(Icons.table_chart),
                      title: Text('Export to Excel'),
                    ),
                  ),
                  const PopupMenuItem<String>(
                    value: 'print',
                    child: ListTile(
                      leading: Icon(Icons.print),
                      title: Text('Print'),
                    ),
                  ),
                ],
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              indicatorWeight: 3,
              indicatorSize: TabBarIndicatorSize.tab,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 16,
              ),
              tabs: const [
                Tab(
                  icon: Icon(Icons.info_outline),
                  text: 'Overview',
                ),
                Tab(
                  icon: Icon(Icons.build),
                  text: 'Systems',
                ),
                Tab(
                  icon: Icon(Icons.summarize),
                  text: 'Summary',
                ),
              ],
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(project),
              _buildSystemsTab(project, projectProvider),
              _buildSummaryTab(project),
            ],
          ),
          floatingActionButton: _tabController.index == 1
              ? FloatingActionButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AddSystemScreen(),
                      ),
                    );
                  },
                  child: const Icon(Icons.add),
                )
              : null,
        );
      },
    );
  }

  Widget _buildOverviewTab(Project project) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Project Information',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Divider(),
                  _buildInfoRow('Project Name', project.name),
                  if (project.clientName.isNotEmpty) _buildInfoRow('Client', project.clientName),
                  _buildInfoRow('Created', DateFormat('MM/dd/yyyy').format(project.createdAt)),
                  _buildInfoRow('Last Updated', DateFormat('MM/dd/yyyy').format(project.updatedAt)),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Project Summary',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Divider(),
                  _buildInfoRow('Total Systems', project.systems.length.toString()),
                  _buildInfoRow('Currency', project.currency),
                  _buildInfoRow('Exchange Rate (USD to ${project.currency})', project.exchangeRate.toString()),
                  _buildInfoRow('Ex-Works Cost (USD)', '\$${NumberFormat("#,##0.00").format(project.totalExWorksCost)}'),
                  _buildInfoRow('Local Cost (${project.currency})', '${project.currency} ${NumberFormat("#,##0.00").format(project.totalLocalCost)}'),
                  _buildInfoRow('Installation Cost (${project.currency})', '${project.currency} ${NumberFormat("#,##0.00").format(project.totalInstallationCost)}'),
                  _buildInfoRow('Total Cost (${project.currency})', '${project.currency} ${NumberFormat("#,##0.00").format(project.totalCost)}'),
                  const SizedBox(height: 8),
                  const Text(
                    'Systems:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  ...project.systems.map((system) => Padding(
                    padding: const EdgeInsets.only(left: 16.0, bottom: 4.0),
                    child: Text('• ${system.name} (${project.currency} ${NumberFormat("#,##0.00").format(system.totalCost)})'),
                  )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemsTab(Project project, ProjectProvider projectProvider) {
    if (project.systems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.build,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'No systems added yet',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              icon: const Icon(Icons.add),
              label: const Text('Add System'),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddSystemScreen(),
                  ),
                );
              },
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: project.systems.length,
      itemBuilder: (context, index) {
        final system = project.systems[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SystemDetailScreen(systemId: system.id),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          system.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'Ex-Works: \$${NumberFormat("#,##0.00").format(system.totalExWorksCost)}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppConstants.secondaryTextColor,
                            ),
                          ),
                          Text(
                            'Local: ${project.currency} ${NumberFormat("#,##0.00").format(system.totalLocalCost)}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppConstants.secondaryTextColor,
                            ),
                          ),
                          Text(
                            'Total: ${project.currency} ${NumberFormat("#,##0.00").format(system.totalCost)}',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text('Type: ${system.type}'),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _buildCostChip('Ex-Works', system.totalExWorksCost, prefix: '\$'),
                      const SizedBox(width: 8),
                      _buildCostChip('Local', system.totalLocalCost, prefix: project.currency),
                      const SizedBox(width: 8),
                      _buildCostChip('Install', system.totalInstallationCost, prefix: project.currency),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton.icon(
                        icon: const Icon(Icons.edit, size: 16),
                        label: const Text('Edit'),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SystemDetailScreen(systemId: system.id),
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 8),
                      TextButton.icon(
                        icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                        label: const Text('Delete', style: TextStyle(color: Colors.red)),
                        onPressed: () {
                          _showDeleteSystemConfirmation(context, system, projectProvider);
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSummaryTab(Project project) {
    // Ex-Works costs (USD)
    final materialsExWorksCost = project.systems.fold(0.0, (sum, system) => sum + system.materialsExWorksCost);
    final equipmentExWorksCost = project.systems.fold(0.0, (sum, system) => sum + system.equipmentExWorksCost);
    final totalExWorksCost = project.totalExWorksCost;

    // Local costs (local currency)
    final materialsLocalCost = project.systems.fold(0.0, (sum, system) => sum + system.materialsLocalCost);
    final equipmentLocalCost = project.systems.fold(0.0, (sum, system) => sum + system.equipmentLocalCost);
    final totalLocalCost = project.totalLocalCost;

    // Installation costs (local currency)
    final laborCost = project.systems.fold(0.0, (sum, system) => sum + system.laborCost);
    final totalInstallationCost = project.totalInstallationCost;

    // Total costs
    final totalCost = project.totalCost;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Cost Summary',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Divider(),
                  const Text(
                    'Ex-Works Costs (USD)',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  _buildInfoRow('Materials', '\$${NumberFormat("#,##0.00").format(materialsExWorksCost)}'),
                  _buildInfoRow('Equipment', '\$${NumberFormat("#,##0.00").format(equipmentExWorksCost)}'),
                  _buildInfoRow('Subtotal', '\$${NumberFormat("#,##0.00").format(totalExWorksCost)}'),
                  const Divider(),
                  Text(
                    'Local Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  _buildInfoRow('Materials', '${project.currency} ${NumberFormat("#,##0.00").format(materialsLocalCost)}'),
                  _buildInfoRow('Equipment', '${project.currency} ${NumberFormat("#,##0.00").format(equipmentLocalCost)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(totalLocalCost)}'),
                  const Divider(),
                  Text(
                    'Installation Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  _buildInfoRow('Labor', '${project.currency} ${NumberFormat("#,##0.00").format(laborCost)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(totalInstallationCost)}'),
                  const Divider(),
                  _buildInfoRow('Total Cost', '${project.currency} ${NumberFormat("#,##0.00").format(totalCost)}', isBold: true),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Cost Breakdown by System',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...project.systems.map((system) => Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 8),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    system.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Divider(),
                  const Text(
                    'Ex-Works Costs (USD)',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  _buildInfoRow('Materials', '\$${NumberFormat("#,##0.00").format(system.materialsExWorksCost)}'),
                  _buildInfoRow('Equipment', '\$${NumberFormat("#,##0.00").format(system.equipmentExWorksCost)}'),
                  _buildInfoRow('Subtotal', '\$${NumberFormat("#,##0.00").format(system.totalExWorksCost)}'),
                  const Divider(),
                  Text(
                    'Local Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  _buildInfoRow('Materials', '${project.currency} ${NumberFormat("#,##0.00").format(system.materialsLocalCost)}'),
                  _buildInfoRow('Equipment', '${project.currency} ${NumberFormat("#,##0.00").format(system.equipmentLocalCost)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(system.totalLocalCost)}'),
                  const Divider(),
                  Text(
                    'Installation Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  _buildInfoRow('Labor', '${project.currency} ${NumberFormat("#,##0.00").format(system.laborCost)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(system.totalInstallationCost)}'),
                  const Divider(),
                  _buildInfoRow('System Total', '${project.currency} ${NumberFormat("#,##0.00").format(system.totalCost)}', isBold: true),
                ],
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostChip(String label, double cost, {String? prefix}) {
    final formattedCost = NumberFormat("#,##0.00").format(cost);
    final displayText = prefix != null
        ? '$label: $prefix $formattedCost'
        : '$label: $formattedCost';

    return Chip(
      label: Text(
        displayText,
        style: const TextStyle(fontSize: 12),
      ),
      backgroundColor: Colors.grey[200],
    );
  }

  void _showDeleteSystemConfirmation(BuildContext context, SystemEstimate system, ProjectProvider projectProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete System'),
        content: Text('Are you sure you want to delete "${system.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              projectProvider.removeSystem(system.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('System deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
