import 'package:flutter/material.dart';
import '../models/super_database_models.dart';
import '../services/super_database_service.dart';
import '../services/excel_import_export_service.dart';
import '../widgets/subsection_card.dart';
import '../widgets/create_subsection_dialog.dart';
import 'subsection_data_grid_screen.dart';

class SectionDetailScreen extends StatefulWidget {
  final Section section;

  const SectionDetailScreen({
    super.key,
    required this.section,
  });

  @override
  State<SectionDetailScreen> createState() => _SectionDetailScreenState();
}

class _SectionDetailScreenState extends State<SectionDetailScreen> {
  final _superDbService = SuperDatabaseService();
  final _excelService = ExcelImportExportService();

  List<Subsection> _subsections = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSubsections();
  }

  Future<void> _loadSubsections() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final subsections = await _superDbService.getSubsections(widget.section.id);

      setState(() {
        _subsections = subsections;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load subsections: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _createNewSubsection() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => CreateSubsectionDialog(sectionId: widget.section.id),
    );

    if (result != null) {
      try {
        await _superDbService.createSubsection(
          sectionId: widget.section.id,
          name: result['name'],
          displayName: result['displayName'],
          columns: result['columns'],
          description: result['description'],
        );

        _showSuccessSnackBar('Subsection "${result['displayName']}" created successfully');
        await _loadSubsections();
      } catch (e) {
        _showErrorSnackBar('Failed to create subsection: $e');
      }
    }
  }

  Future<void> _importSubsectionFromExcel() async {
    try {
      _showLoadingDialog('Importing Excel file...');

      final operation = await _excelService.importExcelAsSection(
        sectionDisplayName: '${widget.section.displayName}_Import',
        description: 'Imported data for ${widget.section.displayName}',
      );

      Navigator.of(context).pop(); // Close loading dialog

      if (operation.status == OperationStatus.completed) {
        _showSuccessSnackBar(
          'Successfully imported ${operation.processedRows} rows from Excel file'
        );
        await _loadSubsections();
      } else {
        _showErrorSnackBar(
          'Import failed: ${operation.errorMessage ?? "Unknown error"}'
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      _showErrorSnackBar('Import failed: $e');
    }
  }

  Future<void> _exportSectionToExcel() async {
    try {
      _showLoadingDialog('Exporting section to Excel...');

      final operation = await _excelService.exportSectionToExcel(
        sectionId: widget.section.id,
      );

      Navigator.of(context).pop(); // Close loading dialog

      if (operation.status == OperationStatus.completed) {
        _showSuccessSnackBar(
          'Successfully exported ${operation.processedRows} rows to Excel file'
        );
      } else {
        _showErrorSnackBar(
          'Export failed: ${operation.errorMessage ?? "Unknown error"}'
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      _showErrorSnackBar('Export failed: $e');
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final color = _parseColor(widget.section.color) ?? Theme.of(context).primaryColor;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.section.displayName),
        backgroundColor: color,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.upload_file),
            onPressed: _importSubsectionFromExcel,
            tooltip: 'Import Excel',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportSectionToExcel,
            tooltip: 'Export to Excel',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSubsections,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewSubsection,
        icon: const Icon(Icons.add),
        label: const Text('New Subsection'),
        backgroundColor: color,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading subsections...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red.shade700),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSubsections,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_subsections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.table_chart,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No subsections found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first subsection or import from Excel',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: _createNewSubsection,
                  icon: const Icon(Icons.add),
                  label: const Text('Create Subsection'),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _importSubsectionFromExcel,
                  icon: const Icon(Icons.upload_file),
                  label: const Text('Import Excel'),
                ),
              ],
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Section info header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: Colors.grey.shade50,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.section.displayName,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: _parseColor(widget.section.color),
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (widget.section.description != null && widget.section.description!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    widget.section.description!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              const SizedBox(height: 8),
              Text(
                '${_subsections.length} subsection${_subsections.length == 1 ? '' : 's'}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),

        // Subsections grid
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.2,
              ),
              itemCount: _subsections.length,
              itemBuilder: (context, index) {
                final subsection = _subsections[index];
                return SubsectionCard(
                  subsection: subsection,
                  sectionColor: _parseColor(widget.section.color),
                  onTap: () => _navigateToDataGrid(subsection),
                  onDelete: () => _deleteSubsection(subsection),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToDataGrid(Subsection subsection) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SubsectionDataGridScreen(
          section: widget.section,
          subsection: subsection,
        ),
      ),
    ).then((_) => _loadSubsections()); // Refresh when returning
  }

  Future<void> _deleteSubsection(Subsection subsection) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Subsection'),
        content: Text(
          'Are you sure you want to delete "${subsection.displayName}"? '
          'This will permanently delete all data in this subsection.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _superDbService.deleteSubsection(widget.section.id, subsection.id);
        _showSuccessSnackBar('Subsection "${subsection.displayName}" deleted');
        await _loadSubsections();
      } catch (e) {
        _showErrorSnackBar('Failed to delete subsection: $e');
      }
    }
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;

    try {
      switch (colorString.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'teal':
          return Colors.teal;
        case 'amber':
          return Colors.amber;
        case 'indigo':
          return Colors.indigo;
        default:
          return null;
      }
    } catch (e) {
      return null;
    }
  }
}
