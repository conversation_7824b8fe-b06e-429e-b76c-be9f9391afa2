import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:uuid/uuid.dart';

/// A standalone implementation of a flexible data grid with local storage
class FlexibleDataGridStandalone extends StatefulWidget {
  final String title;
  final Color themeColor;

  const FlexibleDataGridStandalone({
    super.key,
    this.title = 'Flexible Data Grid',
    this.themeColor = Colors.blue,
  });

  @override
  State<FlexibleDataGridStandalone> createState() => _FlexibleDataGridStandaloneState();
}

class _FlexibleDataGridStandaloneState extends State<FlexibleDataGridStandalone> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final FocusNode _gridFocusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  final Map<String, TextEditingController> _cellControllers = {};
  final _uuid = Uuid();

  // Table data
  List<GridColumn> _columns = [];
  List<GridRow> _rows = [];
  List<GridRow> _filteredRows = [];

  // UI state
  final bool _isLoading = false;
  String? _error;
  final bool _isEditing = false;
  String? _editingCellId;
  final bool _isCardView = false;

  // Selection state
  final Set<String> _selectedCells = {}; // Format: "rowId:columnId"
  String? _selectedRowId;
  String? _selectedColumnId;
  bool _isSelecting = false;
  String? _selectionStartRowId;
  String? _selectionStartColumnId;

  @override
  void initState() {
    super.initState();
    _initializeData();

    // Add keyboard listener for arrow navigation
    _gridFocusNode.addListener(() {
      if (_gridFocusNode.hasFocus) {
        // This ensures the grid can receive keyboard events
      }
    });
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _gridFocusNode.dispose();
    _searchController.dispose();

    // Dispose all cell controllers
    for (var controller in _cellControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  void _initializeData() {
    // Create some default columns
    _columns = [
      GridColumn(
        id: _uuid.v4(),
        name: 'ID',
        dataType: ColumnDataType.text,
        width: 100,
      ),
      GridColumn(
        id: _uuid.v4(),
        name: 'Name',
        dataType: ColumnDataType.text,
        width: 200,
      ),
      GridColumn(
        id: _uuid.v4(),
        name: 'Price',
        dataType: ColumnDataType.currency,
        width: 150,
      ),
      GridColumn(
        id: _uuid.v4(),
        name: 'Quantity',
        dataType: ColumnDataType.number,
        width: 120,
      ),
      GridColumn(
        id: _uuid.v4(),
        name: 'Date',
        dataType: ColumnDataType.date,
        width: 150,
      ),
    ];

    // Create some sample rows
    _rows = List.generate(20, (index) {
      final rowId = _uuid.v4();

      // Create cell controllers
      for (final column in _columns) {
        final cellKey = '$rowId:${column.id}';
        String value = '';

        switch (column.dataType) {
          case ColumnDataType.text:
            if (column.name == 'ID') {
              value = 'ITEM-${index + 1}';
            } else if (column.name == 'Name') {
              value = 'Product ${index + 1}';
            }
            break;
          case ColumnDataType.currency:
            value = ((index + 1) * 10.99).toStringAsFixed(2);
            break;
          case ColumnDataType.number:
            value = (index + 1).toString();
            break;
          case ColumnDataType.date:
            final date = DateTime.now().add(Duration(days: index));
            value = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
            break;
          default:
            break;
        }

        _cellControllers[cellKey] = TextEditingController(text: value);
      }

      return GridRow(
        id: rowId,
        order: index,
      );
    });

    _filteredRows = List.from(_rows);
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredRows = List.from(_rows);
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    setState(() {
      _filteredRows = _rows.where((row) {
        // Search in all cells for this row
        for (final column in _columns) {
          final cellKey = '${row.id}:${column.id}';
          final controller = _cellControllers[cellKey];
          if (controller == null) continue;

          final value = controller.text.toLowerCase();
          if (value.contains(lowercaseQuery)) return true;
        }

        return false;
      }).toList();
    });
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _selectCell(String rowId, String columnId) {
    setState(() {
      _selectedRowId = rowId;
      _selectedColumnId = columnId;

      // Clear selection if not in selection mode
      if (!_isSelecting) {
        _selectedCells.clear();
        _selectedCells.add('$rowId:$columnId');
      }
    });

    // Request focus to the grid
    _gridFocusNode.requestFocus();
  }

  void _startSelecting(String rowId, String columnId) {
    setState(() {
      _selectedRowId = rowId;
      _selectedColumnId = columnId;
      _selectionStartRowId = rowId;
      _selectionStartColumnId = columnId;
      _isSelecting = true;

      // Clear previous selection
      _selectedCells.clear();
      _selectedCells.add('$rowId:$columnId');
    });
  }

  void _updateSelection(String rowId, String columnId) {
    if (!_isSelecting || _selectionStartRowId == null || _selectionStartColumnId == null) {
      return;
    }

    setState(() {
      // Clear previous selection
      _selectedCells.clear();

      // Find indices for start and end rows/columns
      final startRowIndex = _rows.indexWhere((row) => row.id == _selectionStartRowId);
      final endRowIndex = _rows.indexWhere((row) => row.id == rowId);
      final startColIndex = _columns.indexWhere((col) => col.id == _selectionStartColumnId);
      final endColIndex = _columns.indexWhere((col) => col.id == columnId);

      if (startRowIndex < 0 || endRowIndex < 0 || startColIndex < 0 || endColIndex < 0) {
        return;
      }

      // Calculate selection rectangle
      final minRowIndex = startRowIndex < endRowIndex ? startRowIndex : endRowIndex;
      final maxRowIndex = startRowIndex < endRowIndex ? endRowIndex : startRowIndex;
      final minColIndex = startColIndex < endColIndex ? startColIndex : endColIndex;
      final maxColIndex = startColIndex < endColIndex ? endColIndex : startColIndex;

      // Add all cells in the rectangle to selection
      for (var r = minRowIndex; r <= maxRowIndex; r++) {
        for (var c = minColIndex; c <= maxColIndex; c++) {
          final rowId = _rows[r].id;
          final columnId = _columns[c].id;

          _selectedCells.add('$rowId:$columnId');
        }
      }
    });
  }

  void _endSelecting() {
    setState(() {
      _isSelecting = false;
    });
  }

  void _moveSelection(int rowDelta, int colDelta) {
    if (_selectedRowId == null || _selectedColumnId == null) {
      if (_filteredRows.isNotEmpty && _columns.isNotEmpty) {
        _selectCell(_filteredRows.first.id, _columns.first.id);
      }
      return;
    }

    // Find current indices
    final rowIndex = _filteredRows.indexWhere((row) => row.id == _selectedRowId);
    final colIndex = _columns.indexWhere((col) => col.id == _selectedColumnId);

    if (rowIndex < 0 || colIndex < 0) return;

    // Calculate new indices
    final newRowIndex = (rowIndex + rowDelta).clamp(0, _filteredRows.length - 1);
    final newColIndex = (colIndex + colDelta).clamp(0, _columns.length - 1);

    // Get new IDs
    final newRowId = _filteredRows[newRowIndex].id;
    final newColId = _columns[newColIndex].id;

    _selectCell(newRowId, newColId);
  }

  void _clearSelectedCells() {
    setState(() {
      // Clear all selected cells
      for (var cellKey in _selectedCells) {
        final parts = cellKey.split(':');
        final rowId = parts[0];
        final columnId = parts[1];

        final controllerKey = '$rowId:$columnId';
        if (_cellControllers.containsKey(controllerKey)) {
          _cellControllers[controllerKey]!.text = '';
        }
      }
    });

    _showSnackBar('Cells cleared');
  }

  // Add new row
  void _addNewRow() {
    final rowId = _uuid.v4();
    final newRow = GridRow(
      id: rowId,
      order: _rows.length,
    );

    // Create cell controllers for the new row
    for (final column in _columns) {
      final cellKey = '$rowId:${column.id}';
      _cellControllers[cellKey] = TextEditingController();
    }

    setState(() {
      _rows.add(newRow);
      _filteredRows.add(newRow);
    });

    _showSnackBar('New row added');

    // Select the new row
    _selectCell(rowId, _columns.first.id);
  }

  // Delete row
  void _deleteRow(String rowId) {
    setState(() {
      _rows.removeWhere((row) => row.id == rowId);
      _filteredRows.removeWhere((row) => row.id == rowId);

      // Remove controllers for the deleted row
      final keysToRemove = _cellControllers.keys.where((key) => key.startsWith('$rowId:')).toList();
      for (final key in keysToRemove) {
        _cellControllers[key]?.dispose();
        _cellControllers.remove(key);
      }

      // Reset selection if the selected row was deleted
      if (_selectedRowId == rowId) {
        _selectedRowId = null;
        _selectedColumnId = null;
        _selectedCells.clear();
      }
    });

    _showSnackBar('Row deleted');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        actions: [
          // Column management
          IconButton(
            icon: const Icon(Icons.view_column),
            tooltip: 'Add Column',
            onPressed: _addNewColumn,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterData('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: _filterData,
            ),
          ),

          // Data grid
          Expanded(
            child: _buildDataGrid(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewRow,
        backgroundColor: widget.themeColor,
        tooltip: 'Add Row',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildColumnHeader(GridColumn column) {
    return Container(
      width: column.width,
      height: 40,
      decoration: BoxDecoration(
        color: widget.themeColor,
        border: Border.all(color: Colors.grey.shade400),
      ),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                column.name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          PopupMenuButton<String>(
            padding: EdgeInsets.zero,
            icon: const Icon(Icons.more_vert, color: Colors.white, size: 16),
            tooltip: 'Column Options',
            onSelected: (value) {
              switch (value) {
                case 'delete':
                  _deleteColumn(column.id);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: Colors.red),
                  title: Text('Delete Column'),
                  dense: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCell(String rowId, String columnId) {
    final isSelected = _selectedCells.contains('$rowId:$columnId');
    final isFocused = _selectedRowId == rowId && _selectedColumnId == columnId;
    final cellKey = '$rowId:$columnId';
    final column = _columns.firstWhere((col) => col.id == columnId);

    // Ensure we have a controller for this cell
    if (!_cellControllers.containsKey(cellKey)) {
      _cellControllers[cellKey] = TextEditingController();
    }

    return GestureDetector(
      onTap: () => _selectCell(rowId, columnId),
      onPanStart: (details) {
        _startSelecting(rowId, columnId);
      },
      onPanUpdate: (details) {
        // This would need more complex logic to determine which cell we're over
        // For simplicity, we're not implementing this now
      },
      onPanEnd: (_) {
        _endSelecting();
      },
      child: Container(
        width: column.width,
        height: 40,
        decoration: BoxDecoration(
          color: isFocused
              ? widget.themeColor.withAlpha(51) // 0.2 * 255 = 51
              : isSelected
                  ? widget.themeColor.withAlpha(26) // 0.1 * 255 = 26
                  : Colors.white,
          border: Border.all(
            color: isFocused || isSelected
                ? widget.themeColor
                : Colors.grey.shade400,
            width: isFocused ? 2 : 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        alignment: Alignment.centerLeft,
        child: TextField(
          controller: _cellControllers[cellKey],
          decoration: const InputDecoration(
            border: InputBorder.none,
            isDense: true,
            contentPadding: EdgeInsets.zero,
          ),
          style: const TextStyle(
            fontSize: 14,
          ),
          autofocus: isFocused, // Auto-focus when selected
        ),
      ),
    );
  }

  Widget _buildDataGrid() {
    return KeyboardListener(
      focusNode: _gridFocusNode,
      onKeyEvent: (keyEvent) {
        if (keyEvent is KeyDownEvent) {
          if (keyEvent.logicalKey == LogicalKeyboardKey.arrowUp) {
            _moveSelection(-1, 0);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.arrowDown) {
            _moveSelection(1, 0);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.arrowLeft) {
            _moveSelection(0, -1);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.arrowRight) {
            _moveSelection(0, 1);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.tab) {
            _moveSelection(0, HardwareKeyboard.instance.isShiftPressed ? -1 : 1);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.enter) {
            _moveSelection(1, 0);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.delete ||
                    keyEvent.logicalKey == LogicalKeyboardKey.backspace) {
            _clearSelectedCells();
          }
        }
      },
      child: Scrollbar(
        controller: _verticalController,
        thumbVisibility: true,
        child: Scrollbar(
          controller: _horizontalController,
          thumbVisibility: true,
          notificationPredicate: (notification) => notification.depth == 1,
          child: SingleChildScrollView(
            controller: _verticalController,
            child: SingleChildScrollView(
              controller: _horizontalController,
              scrollDirection: Axis.horizontal,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row
                  Row(
                    children: [
                      // Empty corner cell
                      Container(
                        width: 60,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                        child: Center(
                          child: IconButton(
                            icon: const Icon(Icons.add, color: Colors.green),
                            tooltip: 'Add Row',
                            onPressed: _addNewRow,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ),
                      ),

                      // Column headers
                      ..._columns.map((column) => _buildColumnHeader(column)),
                    ],
                  ),

                  // Data rows
                  ..._filteredRows.asMap().entries.map((entry) {
                    final rowIndex = entry.key;
                    final row = entry.value;

                    return Row(
                      children: [
                        // Row header
                        Container(
                          width: 60,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            border: Border.all(color: Colors.grey.shade400),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left: 4),
                                child: Text(
                                  '${rowIndex + 1}',
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                                onPressed: () => _deleteRow(row.id),
                                tooltip: 'Delete Row',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ),

                        // Data cells
                        ..._columns.map((column) => _buildCell(row.id, column.id)),
                      ],
                    );
                  }),

                  // Add row button
                  Row(
                    children: [
                      Container(
                        width: 60,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                        child: Center(
                          child: IconButton(
                            icon: const Icon(Icons.add, color: Colors.green),
                            tooltip: 'Add Row',
                            onPressed: _addNewRow,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ),
                      ),

                      Container(
                        width: _columns.length * 150,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                        child: Center(
                          child: TextButton.icon(
                            icon: const Icon(Icons.add),
                            label: const Text('Add New Row'),
                            onPressed: _addNewRow,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Add new column
  void _addNewColumn() {
    final nameController = TextEditingController();
    ColumnDataType selectedType = ColumnDataType.text;

    showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setStateDialog) {
          return AlertDialog(
            title: const Text('Add New Column'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Column Name',
                    hintText: 'Enter a name for the column',
                  ),
                  autofocus: true,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<ColumnDataType>(
                  value: selectedType,
                  decoration: const InputDecoration(
                    labelText: 'Column Type',
                  ),
                  items: ColumnDataType.values.map((type) {
                    return DropdownMenuItem<ColumnDataType>(
                      value: type,
                      child: Text(type.toString().split('.').last),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setStateDialog(() {
                        selectedType = value;
                      });
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (nameController.text.trim().isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Please enter a column name')),
                    );
                    return;
                  }

                  Navigator.of(context).pop({
                    'name': nameController.text.trim(),
                    'type': selectedType,
                  });
                },
                child: const Text('Add'),
              ),
            ],
          );
        },
      ),
    ).then((result) {
      if (result != null) {
        final columnId = _uuid.v4();
        final newColumn = GridColumn(
          id: columnId,
          name: result['name'],
          dataType: result['type'],
          width: 150,
        );

        // Create cell controllers for the new column
        for (final row in _rows) {
          final cellKey = '${row.id}:$columnId';
          _cellControllers[cellKey] = TextEditingController();
        }

        setState(() {
          _columns.add(newColumn);
        });

        _showSnackBar('Column "${result['name']}" added');
      }
    });
  }

  // Delete column
  void _deleteColumn(String columnId) {
    final column = _columns.firstWhere((col) => col.id == columnId);

    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: Text('Are you sure you want to delete the column "${column.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    ).then((confirmed) {
      if (confirmed == true) {
        setState(() {
          _columns.removeWhere((col) => col.id == columnId);

          // Remove controllers for this column
          for (final row in _rows) {
            final cellKey = '${row.id}:$columnId';
            _cellControllers[cellKey]?.dispose();
            _cellControllers.remove(cellKey);
          }

          // Reset selection if the selected column was deleted
          if (_selectedColumnId == columnId) {
            _selectedRowId = null;
            _selectedColumnId = null;
            _selectedCells.clear();
          }
        });

        _showSnackBar('Column deleted');
      }
    });
  }
}

// Model classes for the standalone implementation
class GridColumn {
  final String id;
  String name;
  ColumnDataType dataType;
  double width;
  String? format;
  String? prefix;
  String? suffix;
  int? decimalPlaces;

  GridColumn({
    required this.id,
    required this.name,
    required this.dataType,
    this.width = 150,
    this.format,
    this.prefix,
    this.suffix,
    this.decimalPlaces,
  });
}

class GridRow {
  final String id;
  int order;

  GridRow({
    required this.id,
    required this.order,
  });
}

enum ColumnDataType {
  text,
  number,
  currency,
  percentage,
  date,
  time,
  datetime,
  boolean,
  email,
  url,
  phone,
  dropdown,
  multiselect,
}