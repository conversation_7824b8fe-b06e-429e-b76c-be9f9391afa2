import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart' hide Border, BorderStyle;
import 'dart:io';
import 'dart:math';

class CompleteExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const CompleteExcelGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  });

  @override
  State<CompleteExcelGrid> createState() => _CompleteExcelGridState();
}

class _CompleteExcelGridState extends State<CompleteExcelGrid> {
  // Data state
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _filteredItems = [];
  List<String> _columns = [];
  bool _isLoading = true;
  String? _error;
  bool _pendingChanges = false; // Track if there are unsaved changes

  // Selection state
  int? _selectedRow;
  int? _selectedCol;
  Set<String> _selectedCells = {}; // Format: "row:col"
  bool _isSelecting = false;
  int? _selectionStartRow;
  int? _selectionStartCol;
  bool _isEditMode = false;

  // Editing state
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, FocusNode> _focusNodes = {};
  final FocusNode _gridFocusNode = FocusNode();

  // Search
  final TextEditingController _searchController = TextEditingController();

  // Cell size constants
  final double _defaultColumnWidth = 150.0;
  final double _headerHeight = 40.0;
  final double _rowHeight = 40.0;
  final double _rowHeaderWidth = 60.0;

  // Column widths - can be resized
  final Map<int, double> _columnWidths = {};

  // Row heights - can be resized
  final Map<int, double> _rowHeights = {};

  // Resizing state
  bool _isResizingColumn = false;
  bool _isResizingRow = false;
  int? _resizingIndex;
  double _resizeStartPosition = 0.0;
  double _resizeStartSize = 0.0;

  // Scroll Controllers
  final ScrollController _verticalController = ScrollController();
  final ScrollController _horizontalController = ScrollController();


  @override
  void initState() {
    super.initState();

    // Initialize columns with predefined columns if provided
    if (widget.predefinedColumns != null) {
      _columns = List.from(widget.predefinedColumns!);
    } else {
      _columns = [
        'model',
        'description',
        'manufacturer',
        'approval',
        'ex_works_price',
        'local_price',
        'installation_price',
      ];
    }

    _loadData();

    // Add keyboard listener for key events
    _gridFocusNode.addListener(() {
      if (_gridFocusNode.hasFocus) {
        // This ensures the grid can receive keyboard events
      }
    });

     // Listen to scroll controllers to update selection position if needed
     _verticalController.addListener(_updateSelectionDisplay);
     _horizontalController.addListener(_updateSelectionDisplay);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _gridFocusNode.dispose();
    _verticalController.dispose();
    _horizontalController.dispose();


    // Dispose all controllers and focus nodes
    for (var controller in _controllers.values) {
      controller.dispose();
    }

    for (var focusNode in _focusNodes.values) {
      focusNode.dispose();
    }

    super.dispose();
  }

  // Data loading and filtering
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final loadedItems = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Discover all columns from data
      final Set<String> columnSet = {'id'};

      // Add predefined columns
      columnSet.addAll(_columns);

      // Add any additional columns from the data
      for (var item in loadedItems) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      setState(() {
        _items = loadedItems;
        _filteredItems = List.from(loadedItems);
        _columns = columnSet.toList();
        _isLoading = false;

        // Initialize controllers and focus nodes for all cells
        _initControllersAndFocusNodes();
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
      print('Error loading data: $e'); // Log the error
    }
  }

  void _initControllersAndFocusNodes() {
    // Dispose and clear existing controllers and focus nodes
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();

    for (var focusNode in _focusNodes.values) {
      focusNode.dispose();
    }
    _focusNodes.clear();

    // Create controllers and focus nodes for each cell in filtered items
    for (var rowIndex = 0; rowIndex < _filteredItems.length; rowIndex++) {
      for (var colIndex = 0; colIndex < _columns.length; colIndex++) {
        final cellKey = '$rowIndex:$colIndex';
        final column = _columns[colIndex];
        // Get the value from the filtered item, defaulting to empty string
        final value = _filteredItems[rowIndex][column]?.toString() ?? '';

        _controllers[cellKey] = TextEditingController(text: value);

        final focusNode = FocusNode();
        focusNode.addListener(() {
          if (focusNode.hasFocus) {
            setState(() {
              _isEditMode = true;
              _selectedRow = rowIndex;
              _selectedCol = colIndex;
            });
          } else {
            // When focus is lost, update the cell value in local data and Firestore
            final controllerValue = _controllers[cellKey]?.text ?? '';
            _updateCellValue(rowIndex, colIndex, controllerValue);
            setState(() {
              _isEditMode = false;
            });
          }
        });
        _focusNodes[cellKey] = focusNode;
      }
    }
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(_items);
        _initControllersAndFocusNodes(); // Reinitialize controllers for all items
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    setState(() {
      _filteredItems = _items.where((item) {
        // Search in all columns
        for (var column in _columns) {
          final value = item[column]?.toString().toLowerCase() ?? '';
          if (value.contains(lowercaseQuery)) {
            return true;
          }
        }
        return false;
      }).toList();

      _initControllersAndFocusNodes(); // Reinitialize controllers for filtered items
    });
  }

  // Cell selection and editing
  void _selectCell(int row, int col, {bool startEditing = false}) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _selectedRow = row;
      _selectedCol = col;

      // Clear selection if not in selection mode
      if (!_isSelecting) {
        _selectedCells.clear();
        _selectedCells.add('$row:$col');
      }

      // Set edit mode based on startEditing parameter
      _isEditMode = startEditing;
    });

    // Focus the cell if startEditing is true
    if (startEditing) {
      final cellKey = '$row:$col';
      if (_focusNodes.containsKey(cellKey)) {
        _focusNodes[cellKey]!.requestFocus();
      }
    } else {
      // Otherwise, focus the grid to enable keyboard navigation
      _gridFocusNode.requestFocus();
    }
  }

  void _startSelecting(int row, int col) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _selectedRow = row;
      _selectedCol = col;
      _selectionStartRow = row;
      _selectionStartCol = col;
      _isSelecting = true;
      _isEditMode = false;

      // Clear previous selection and add the starting cell
      _selectedCells.clear();
      _selectedCells.add('$row:$col');
    });

    // Focus the grid to enable keyboard navigation
    _gridFocusNode.requestFocus();
  }

  void _updateSelection(int row, int col) {
    if (!_isSelecting || _selectionStartRow == null || _selectionStartCol == null) {
      return;
    }

    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    // Only update if the selection has changed
    if (_selectedRow == row && _selectedCol == col) {
      return;
    }

    setState(() {
      // Update the current selection point
      _selectedRow = row;
      _selectedCol = col;

      // Clear previous selection
      _selectedCells.clear();

      // Calculate selection rectangle
      final startRow = _selectionStartRow! < row ? _selectionStartRow! : row;
      final endRow = _selectionStartRow! < row ? row : _selectionStartRow!;
      final startCol = _selectionStartCol! < col ? _selectionStartCol! : col;
      final endCol = _selectionStartCol! < col ? col : _selectionStartCol!;

      // Add all cells in the rectangle to selection
      for (var r = startRow; r <= endRow; r++) {
        for (var c = startCol; c <= endCol; c++) {
          // Ensure the cell exists in the current filtered data before adding
          if (r < _filteredItems.length && c < _columns.length) {
             _selectedCells.add('$r:$c');
          }
        }
      }
    });
  }

  // Function to update selection display when scrolling
   void _updateSelectionDisplay() {
      // This function is a placeholder. Manually updating the selection overlay
      // based on scroll position is complex with this manual grid approach.
      // A real-world scenario would require more sophisticated layout and rendering.
      // For now, we rely on the cell widgets rebuilding when state changes.
   }


  // Column resizing - improved to prevent freezing
  void _startResizingColumn(int colIndex, double startX) {
    // Immediately end any ongoing selection to avoid conflicts
    if (_isSelecting) {
      _endSelecting();
    }

    // Store the initial values without triggering a full rebuild
    _isResizingColumn = true;
    _isResizingRow = false;
    _resizingIndex = colIndex;
    _resizeStartPosition = startX;
    _resizeStartSize = _getColumnWidth(colIndex);

    // Only rebuild what's necessary
    setState(() {});
  }

  void _updateColumnResize(double dx) {
    if (!_isResizingColumn || _resizingIndex == null) return;

    // Calculate new width based on initial size and drag distance
    // Use absolute position difference instead of cumulative delta to avoid drift
    final newWidth = (_resizeStartSize + dx).clamp(50.0, 500.0);

    // Update the width without triggering a full rebuild
    _columnWidths[_resizingIndex!] = newWidth;

    // Use a more targeted rebuild approach
    setState(() {});
  }

  void _endResizing() {
    // Reset resizing state
    _isResizingColumn = false;
    _isResizingRow = false;
    _resizingIndex = null;

    // Only rebuild what's necessary
    setState(() {});
  }

  // Row resizing - improved to prevent freezing
  void _startResizingRow(int rowIndex, double startY) {
    // Immediately end any ongoing selection to avoid conflicts
    if (_isSelecting) {
      _endSelecting();
    }

    // Store the initial values without triggering a full rebuild
    _isResizingRow = true;
    _isResizingColumn = false;
    _resizingIndex = rowIndex;
    _resizeStartPosition = startY;
    _resizeStartSize = _getRowHeight(rowIndex);

    // Only rebuild what's necessary
    setState(() {});
  }

  void _updateRowResize(double dy) {
    if (!_isResizingRow || _resizingIndex == null) return;

    // Calculate new height based on initial size and drag distance
    // Use absolute position difference instead of cumulative delta to avoid drift
    final newHeight = (_resizeStartSize + dy).clamp(30.0, 200.0);

    // Update the height without triggering a full rebuild
    _rowHeights[_resizingIndex!] = newHeight;

    // Use a more targeted rebuild approach
    setState(() {});
  }

  // Get column width (with default fallback)
  double _getColumnWidth(int colIndex) {
    return _columnWidths[colIndex] ?? _defaultColumnWidth;
  }

  // Get row height (with default fallback)
  double _getRowHeight(int rowIndex) {
    return _rowHeights[rowIndex] ?? _rowHeight;
  }

  // Edit column header
  void _editColumnHeader(String column) {
    if (column == 'id') {
      _showSnackBar('Cannot edit ID column', isError: true);
      return;
    }

    final TextEditingController controller = TextEditingController(text: column);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Column Name'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Column Name',
            hintText: 'Enter column name',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final newColumnName = controller.text.trim();
              if (newColumnName.isEmpty) {
                return;
              }

              if (newColumnName == column) {
                Navigator.of(context).pop();
                return;
              }

              if (_columns.contains(newColumnName)) {
                _showSnackBar('Column name already exists', isError: true);
                return;
              }

              _renameColumn(column, newColumnName);
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: widget.themeColor),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // Rename column
  Future<void> _renameColumn(String oldName, String newName) async {
    try {
      // We need to update all documents to rename this field
      final batch = FirebaseFirestore.instance.batch();

      for (var item in _items) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(item['id'] as String);

        // Copy the value from the old field to the new field
        final value = item[oldName];
        if (value != null) {
          batch.update(docRef, {
            newName: value,
            oldName: FieldValue.delete(),
          });
        }
      }

      await batch.commit();

      if (!mounted) return;

      // Update local data
      setState(() {
        final colIndex = _columns.indexOf(oldName);
        if (colIndex != -1) {
           _columns[colIndex] = newName;

           // Update column width mapping if it exists
           if (_columnWidths.containsKey(colIndex)) {
             final width = _columnWidths[colIndex]!;
             _columnWidths.remove(colIndex);
             _columnWidths[colIndex] = width;
           }
        }


        // Update data in items
        for (var item in _items) {
          if (item.containsKey(oldName)) {
            item[newName] = item[oldName];
            item.remove(oldName);
          }
        }

        for (var item in _filteredItems) {
          if (item.containsKey(oldName)) {
            item[newName] = item[oldName];
            item.remove(oldName);
          }
        }

        // Reinitialize controllers and focus nodes to reflect new column names
        _initControllersAndFocusNodes();
      });

      _showSnackBar('Column renamed to "$newName"');
    } catch (e) {
      _showSnackBar('Error renaming column: $e', isError: true);
      print('Error renaming column $oldName to $newName: $e'); // Log the error
    }
  }

  void _endSelecting() {
    setState(() {
      _isSelecting = false;
      _selectionStartRow = null;
      _selectionStartCol = null;
    });
  }

  void _updateCellValue(int row, int col, String newValue) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    final column = _columns[col];
    // Cannot update the ID column
    if (column == 'id') return;

    final itemId = _filteredItems[row]['id'] as String;
    final oldValue = _filteredItems[row][column]?.toString() ?? '';

    // Skip update if value hasn't changed
    if (newValue == oldValue) {
      return;
    }

    // Update local data immediately for better responsiveness
    setState(() {
      _filteredItems[row][column] = newValue;

      // Also update in _items
      final index = _items.indexWhere((item) => item['id'] == itemId);
      if (index >= 0) {
        _items[index][column] = newValue;
      }

      // Mark that we have pending changes
      _pendingChanges = true;
    });
  }

  void _clearSelectedCells() {
    // Process each selected cell
    final updates = <String, Map<String, dynamic>>{}; // Map by doc ID to collect updates per document

    for (var cellKey in _selectedCells) {
      final parts = cellKey.split(':');
      final row = int.parse(parts[0]);
      final col = int.parse(parts[1]);

      if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
        continue;
      }

      final column = _columns[col];
      if (column == 'id') continue; // Cannot clear the ID column

      final itemId = _filteredItems[row]['id'] as String;

      // Add to updates, grouped by document ID
      if (!updates.containsKey(itemId)) {
        updates[itemId] = {};
      }
      updates[itemId]![column] = '';

      // Update controller immediately for visual feedback
      final controllerKey = '$row:$col';
      if (_controllers.containsKey(controllerKey)) {
        _controllers[controllerKey]!.text = '';
      }
    }

    // Apply updates to local data
    setState(() {
      for (var entry in updates.entries) {
        final itemId = entry.key;
        final docUpdates = entry.value;

        if (docUpdates.isNotEmpty) {
          // Update local data for filtered items
          final filteredItemIndex = _filteredItems.indexWhere((item) => item['id'] == itemId);
          if (filteredItemIndex >= 0) {
             for (var key in docUpdates.keys) {
                _filteredItems[filteredItemIndex][key] = '';
             }
          }

          // Update local data for all items
          final itemIndex = _items.indexWhere((item) => item['id'] == itemId);
          if (itemIndex >= 0) {
             for (var key in docUpdates.keys) {
                _items[itemIndex][key] = '';
             }
          }
        }
      }

      // Mark that we have pending changes
      if (updates.isNotEmpty) {
        _pendingChanges = true;
      }

      // Clear selection
      _selectedCells.clear();
    });

    if (updates.isNotEmpty) {
      _showSnackBar('Cells cleared (locally)');
    }
  }


  void _moveSelection(int rowDelta, int colDelta) {
    if (_selectedRow == null || _selectedCol == null) {
      // If no cell is selected, select the first cell
      if (_filteredItems.isNotEmpty && _columns.isNotEmpty) {
         _selectCell(0, 0);
      }
      return;
    }

    final newRow = (_selectedRow! + rowDelta).clamp(0, _filteredItems.length - 1);
    final newCol = (_selectedCol! + colDelta).clamp(0, _columns.length - 1);

    _selectCell(newRow, newCol);

    // Optional: Scroll to the new selected cell
    // This requires calculating the position and scrolling the SingleChildScrollView
    // This is complex with manual grid building and variable sizes.
    // For simplicity, this is omitted, but would be needed for a full Excel-like experience.
  }

  // Handle keyboard events
  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    // Only handle key down events
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    // If in edit mode, let the TextField handle most keys
    if (_isEditMode) {
      // Handle arrow keys in edit mode to allow navigation without pressing Enter
      if (event.logicalKey == LogicalKeyboardKey.arrowUp ||
          event.logicalKey == LogicalKeyboardKey.arrowDown ||
          event.logicalKey == LogicalKeyboardKey.arrowLeft ||
          event.logicalKey == LogicalKeyboardKey.arrowRight) {

        // Save the current value first
        if (_selectedRow != null && _selectedCol != null) {
          final cellKey = '$_selectedRow:$_selectedCol';
          if (_controllers.containsKey(cellKey)) {
            final value = _controllers[cellKey]!.text;
            _updateCellValue(_selectedRow!, _selectedCol!, value);
          }
        }

        // Complete editing by unfocusing the TextField
        if (_selectedRow != null && _selectedCol != null) {
          _focusNodes['$_selectedRow:$_selectedCol']?.unfocus();
        }

        // Move selection based on arrow key
        if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
          _moveSelection(-1, 0);
        } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
          _moveSelection(1, 0);
        } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
          _moveSelection(0, -1);
        } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
          _moveSelection(0, 1);
        }

        // Start editing the new cell
        if (_selectedRow != null && _selectedCol != null) {
          _selectCell(_selectedRow!, _selectedCol!, startEditing: true);
        }

        return KeyEventResult.handled;
      }
      // Allow Enter for new line within the TextField if Shift is pressed
      else if (event.logicalKey == LogicalKeyboardKey.enter && !HardwareKeyboard.instance.isShiftPressed) {
        // Enter key completes editing and moves to the next row
        if (_selectedRow != null && _selectedCol != null) {
          // Complete editing by unfocusing the TextField
          _focusNodes['$_selectedRow:$_selectedCol']?.unfocus();
          // The unfocus listener will handle saving the value.
          // Move to the next row after a short delay to allow unfocus to complete
          Future.delayed(const Duration(milliseconds: 50), () {
             _moveSelection(1, 0);
          });
        }
        return KeyEventResult.handled; // Mark as handled to prevent default behavior
      } else if (event.logicalKey == LogicalKeyboardKey.tab) {
        // Tab key completes editing and moves to the next column
        if (_selectedRow != null && _selectedCol != null) {
          // Complete editing by unfocusing the TextField
          _focusNodes['$_selectedRow:$_selectedCol']?.unfocus();
           // Move to the next column after a short delay
          Future.delayed(const Duration(milliseconds: 50), () {
             _moveSelection(0, HardwareKeyboard.instance.isShiftPressed ? -1 : 1);
          });
        }
        return KeyEventResult.handled; // Mark as handled
      } else if (event.logicalKey == LogicalKeyboardKey.escape) {
        // Escape key cancels editing
        if (_selectedRow != null && _selectedCol != null) {
          // Cancel editing by unfocusing the TextField
          _focusNodes['$_selectedRow:$_selectedCol']?.unfocus();
          // Restore focus to the grid
          _gridFocusNode.requestFocus();
        }
        return KeyEventResult.handled; // Mark as handled
      }

      // Let other keys be handled by the text field when in edit mode
      return KeyEventResult.ignored;
    }

    // Handle navigation keys when not in edit mode
    if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
      _moveSelection(-1, 0);
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
      _moveSelection(1, 0);
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
      _moveSelection(0, -1);
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
      _moveSelection(0, 1);
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.tab) {
       _moveSelection(0, HardwareKeyboard.instance.isShiftPressed ? -1 : 1);
       return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.enter) {
      // Enter key starts editing the current cell
      if (_selectedRow != null && _selectedCol != null) {
        _selectCell(_selectedRow!, _selectedCol!, startEditing: true);
      }
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.delete ||
               event.logicalKey == LogicalKeyboardKey.backspace) {
      // Delete/Backspace clears the selected cells when not in edit mode
      _clearSelectedCells();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.keyC &&
               HardwareKeyboard.instance.isControlPressed) {
      _copySelection();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.keyV &&
               HardwareKeyboard.instance.isControlPressed) {
      _pasteFromClipboard();
      return KeyEventResult.handled;
    } else if (event.character != null &&
               event.character!.isNotEmpty &&
               !HardwareKeyboard.instance.isControlPressed &&
               !HardwareKeyboard.instance.isMetaPressed && // Avoid handling Command key on macOS
               !HardwareKeyboard.instance.isAltPressed) // Avoid handling Alt key
               {
      // If a printable character key is pressed, start editing and insert the character
      if (_selectedRow != null && _selectedCol != null) {
        final cellKey = '$_selectedRow:$_selectedCol';
        if (_controllers.containsKey(cellKey)) {
          // First select the cell and start editing mode
          _selectCell(_selectedRow!, _selectedCol!, startEditing: true);

          // Then set the text to the pressed character
          // This fixes the issue with single character input
          Future.microtask(() {
            if (_controllers.containsKey(cellKey)) {
              _controllers[cellKey]!.text = event.character!;

              // Position cursor at the end
              _controllers[cellKey]!.selection = TextSelection.fromPosition(
                TextPosition(offset: _controllers[cellKey]!.text.length),
              );
            }
          });
        }
      }
      return KeyEventResult.handled;
    }

    return KeyEventResult.ignored;
  }

  // Clipboard operations - completely revised to handle multiline content properly
  Future<void> _copySelection() async {
    if (_selectedCells.isEmpty) return;

    // Find the bounds of the selection
    int minRow = _filteredItems.length;
    int maxRow = 0;
    int minCol = _columns.length;
    int maxCol = 0;

    for (var cellKey in _selectedCells) {
      final parts = cellKey.split(':');
      final row = int.parse(parts[0]);
      final col = int.parse(parts[1]);

      minRow = minRow < row ? minRow : row;
      maxRow = maxRow > row ? maxRow : row;
      minCol = minCol < col ? minCol : col;
      maxCol = maxCol > col ? maxCol : col;
    }

    // Check if we're copying a single cell
    final isSingleCell = _selectedCells.length == 1;

    if (isSingleCell) {
      // For a single cell, just copy the raw content without any processing
      final parts = _selectedCells.first.split(':');
      final row = int.parse(parts[0]);
      final col = int.parse(parts[1]);
      final column = _columns[col];
      final cellValue = _filteredItems[row][column]?.toString() ?? '';

      // Copy directly to clipboard without any processing
      await Clipboard.setData(ClipboardData(text: cellValue));
      _showSnackBar('Copied cell to clipboard');
      return;
    }

    // For multiple cells, we need to create a tab-delimited format
    // Build a 2D array of the selected cells
    final List<List<String>> data = [];

    // Add a special marker at the beginning to identify this as our multi-cell format
    // This will help us distinguish between our format and external formats when pasting
    final specialMarker = "FIRETOOL_MULTICELL_DATA";

    for (var r = minRow; r <= maxRow; r++) {
      final rowData = <String>[];

      for (var c = minCol; c <= maxCol; c++) {
        // Only add data for cells that are actually selected
        if (_selectedCells.contains('$r:$c')) {
          final column = _columns[c];
          final cellValue = _filteredItems[r][column]?.toString() ?? '';

          // For multiple cells, preserve multi-line content by replacing newlines
          // with a special marker that won't conflict with tab/newline delimiters
          final sanitizedValue = cellValue.replaceAll('\n', '\\n');
          rowData.add(sanitizedValue);
        } else {
          // Add an empty string for cells within the selection rectangle but not selected
          rowData.add('');
        }
      }

      data.add(rowData);
    }

    // Convert to tab-separated values for Excel-like format with our special marker
    final clipboard = "$specialMarker\n${data.map((row) => row.join('\t')).join('\n')}";
    await Clipboard.setData(ClipboardData(text: clipboard));

    _showSnackBar('Copied ${_selectedCells.length} cells to clipboard');
  }

  Future<void> _pasteFromClipboard() async {
    if (_selectedRow == null || _selectedCol == null) {
      _showSnackBar('Please select a cell first', isError: true);
      return;
    }

    final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data == null || data.text == null || data.text!.trim().isEmpty) {
      _showSnackBar('No data in clipboard', isError: true);
      return;
    }

    final String clipboardText = data.text!;

    // Check if this is our special multi-cell format
    final bool isOurFormat = clipboardText.startsWith("FIRETOOL_MULTICELL_DATA");

    // Determine if this is multi-cell data or single-cell data
    bool isMultiCell;

    // If it's our format, it's definitely multi-cell
    if (isOurFormat) {
      isMultiCell = true;
    }
    // If it contains tabs, it's likely multi-cell data from Excel
    else if (clipboardText.contains('\t')) {
      isMultiCell = true;
    }
    // If multiple cells are selected, we'll treat it as multi-cell paste
    // but only if the data appears to be in a multi-cell format
    else if (_selectedCells.length > 1) {
      // For multi-cell selection, we need to check if the data looks like it came from Excel
      // This is a heuristic - if it has newlines and no tabs, it might be multi-row, single-column data
      isMultiCell = clipboardText.contains('\n');
    }
    // If only one cell is selected and no tabs, always treat as single-cell paste
    // regardless of whether it contains newlines - this preserves multiline content
    else {
      isMultiCell = false;
    }


    if (!isMultiCell) {
      // Handle as single cell with potentially multi-line content
      if (_selectedRow != null && _selectedCol != null) {
         final column = _columns[_selectedCol!];
         // Cannot paste into the ID column
         if (column == 'id') {
            _showSnackBar('Cannot paste into ID column', isError: true);
            return;
         }

         final itemId = _filteredItems[_selectedRow!]['id'] as String;

         // Restore any newlines that were encoded during copy
         // This handles both our own escaped newlines and regular newlines
         String pastedValue = clipboardText;
         if (pastedValue.contains('\\n')) {
           pastedValue = pastedValue.replaceAll('\\n', '\n');
         }

         // Mark that we have pending changes
         _pendingChanges = true;

         // Update local data immediately for better responsiveness
         setState(() {
           _filteredItems[_selectedRow!][column] = pastedValue;

           // Also update in _items
           final index = _items.indexWhere((item) => item['id'] == itemId);
           if (index >= 0) {
             _items[index][column] = pastedValue;
           }

           // Update controller
           final cellKey = '$_selectedRow:$_selectedCol';
           if (_controllers.containsKey(cellKey)) {
             _controllers[cellKey]!.text = pastedValue;
           }
         });

         // Update in Firestore
         FirebaseFirestore.instance
             .collection(widget.collectionPath)
             .doc(itemId)
             .update({column: pastedValue})
             .then((_) {
               _showSnackBar('Pasted content to cell');
             })
             .catchError((error) {
               _showSnackBar('Error updating cell: $error', isError: true);
               // Log the error but don't use print in production
               // print('Error pasting to cell $itemId/$column: $error');
             });
      }
    } else {
      // Handle as multi-cell Excel data (tab-separated values)

      // Parse clipboard data
      List<List<String>> parsedData = [];

      // Check if this is data from our special format
      bool isFromOurFormat = clipboardText.startsWith("FIRETOOL_MULTICELL_DATA");
      bool isFromExcel = false;

      // Process the clipboard text
      String textToProcess = clipboardText;

      // If it's our special format, remove the marker line
      if (isFromOurFormat) {
        final lines = clipboardText.split('\n');
        if (lines.length > 1) {
          // Remove the first line (our marker)
          textToProcess = lines.sublist(1).join('\n');
        }
      } else {
        // Check if this might be from Excel
        // Excel typically uses tab-delimited format for columns and newlines for rows
        isFromExcel = textToProcess.contains('\t');
      }

      if (isFromExcel) {
        // For Excel data, we need to be careful with newlines inside cells
        // Excel wraps multiline cell content in quotes when copying to clipboard

        // First, try to detect if this is Excel data with quoted multiline cells
        bool hasQuotedCells = textToProcess.contains('"');

        if (hasQuotedCells) {
          // This is a more complex case - we need to parse the Excel format properly
          // Excel format: cells are separated by tabs, rows by newlines
          // Multiline content in cells is wrapped in quotes

          // Split by rows first (but be careful with quoted newlines)
          List<String> excelRows = [];
          StringBuffer currentRow = StringBuffer();
          bool insideQuotes = false;

          for (int i = 0; i < textToProcess.length; i++) {
            String char = textToProcess[i];

            if (char == '"') {
              insideQuotes = !insideQuotes;
              currentRow.write(char); // Keep the quotes for now
            } else if (char == '\n' && !insideQuotes) {
              // End of row (only if not inside quotes)
              excelRows.add(currentRow.toString());
              currentRow = StringBuffer();
            } else {
              currentRow.write(char);
            }
          }

          // Add the last row if not empty
          if (currentRow.toString().isNotEmpty) {
            excelRows.add(currentRow.toString());
          }

          // Now process each row
          for (var row in excelRows) {
            // Skip empty rows
            if (row.trim().isEmpty) continue;

            // Split by tabs, but be careful with tabs inside quoted cells
            List<String> cells = [];
            StringBuffer currentCell = StringBuffer();
            insideQuotes = false;

            for (int i = 0; i < row.length; i++) {
              String char = row[i];

              if (char == '"') {
                insideQuotes = !insideQuotes;
                // Don't add the quote character to our final cell value
              } else if (char == '\t' && !insideQuotes) {
                // End of cell (only if not inside quotes)
                cells.add(currentCell.toString());
                currentCell = StringBuffer();
              } else {
                currentCell.write(char);
              }
            }

            // Add the last cell
            cells.add(currentCell.toString());

            // Process each cell to clean up and handle multiline content
            final processedCells = cells.map((cell) {
              // Remove surrounding quotes and replace escaped quotes
              String processed = cell;
              if (processed.startsWith('"') && processed.endsWith('"')) {
                processed = processed.substring(1, processed.length - 1);
              }
              processed = processed.replaceAll('""', '"'); // Excel escapes quotes by doubling them

              return processed;
            }).toList();

            parsedData.add(processedCells);
          }
        } else {
          // Simpler case - no quoted cells with newlines
          // Split by rows
          final rows = textToProcess.split('\n');

          // Process each row
          for (var row in rows) {
            // Skip empty rows
            if (row.trim().isEmpty) continue;

            // Split by tabs to get cells
            final cells = row.split('\t');
            parsedData.add(cells);
          }
        }
      } else {
        // Our own format or simple text
        // Split by rows
        final rows = textToProcess.split('\n');
        if (rows.isEmpty) {
          _showSnackBar('No rows found in clipboard data', isError: true);
          return;
        }

        // Process each row
        for (var row in rows) {
          // Skip empty rows
          if (row.trim().isEmpty) continue;

          // Split by tabs to get cells
          final cells = row.split('\t');

          // Process each cell
          final processedCells = cells.map((cell) {
            // If this is from our own copy operation or contains escaped newlines,
            // restore the newlines
            if (isFromOurFormat || cell.contains('\\n')) {
              return cell.replaceAll('\\n', '\n');
            }
            // Otherwise, just use the cell value as is
            return cell;
          }).toList();

          parsedData.add(processedCells);
        }
      }

      // If no valid data was found, exit
      if (parsedData.isEmpty) {
        _showSnackBar('No valid data found in clipboard', isError: true);
        return;
      }

      // Start pasting from the selected cell
      final startRow = _selectedRow!;
      final startCol = _selectedCol!;

      // Calculate how many rows and columns we need
      final int requiredRows = startRow + parsedData.length;
      int requiredCols = startCol;
      for (var row in parsedData) {
        if (startCol + row.length > requiredCols) {
          requiredCols = startCol + row.length;
        }
      }

      // Check if we need to add more rows
      final int rowsToAdd = requiredRows - _filteredItems.length;
      if (rowsToAdd > 0) {
        // Add the necessary rows
        for (var i = 0; i < rowsToAdd; i++) {
          await _addNewRow();
        }
      }

      // Check if we need to add more columns
      final int colsToAdd = requiredCols - _columns.length;
      if (colsToAdd > 0) {
        // Add the necessary columns
        for (var i = 0; i < colsToAdd; i++) {
          final newColName = 'column_${_columns.length}';
          await _addColumn(newColName);
        }
      }

      // Prepare batch update
      final batch = FirebaseFirestore.instance.batch();
      final updates = <String, Map<String, dynamic>>{}; // Map by doc ID

      // Track which cells were updated for visual feedback
      final updatedCells = <String>{}; // Format: "row:col"

      // Process each row from parsed data
      for (var i = 0; i < parsedData.length; i++) {
        final rowIndex = startRow + i;
        // Now we should have enough rows
        if (rowIndex >= _filteredItems.length) {
          _showSnackBar('Error: Not enough rows after adding new rows', isError: true);
          continue;
        }

        final itemId = _filteredItems[rowIndex]['id'] as String;
        final rowData = parsedData[i];

        // Add to updates, grouped by document ID
        if (!updates.containsKey(itemId)) {
          updates[itemId] = {};
        }

        for (var j = 0; j < rowData.length; j++) {
          final colIndex = startCol + j;
          // Now we should have enough columns
          if (colIndex >= _columns.length) {
            _showSnackBar('Error: Not enough columns after adding new columns', isError: true);
            continue;
          }

          final column = _columns[colIndex];
          if (column == 'id') continue; // Cannot paste into the ID column

          final cellValue = rowData[j];
          updates[itemId]![column] = cellValue;

          // Track this cell as updated
          updatedCells.add('$rowIndex:$colIndex');
        }
      }

      // Apply updates to local data immediately for better responsiveness
      setState(() {
        // Mark that we have pending changes
        _pendingChanges = true;

        // Update controllers and data for all affected cells
        for (var cellKey in updatedCells) {
          final parts = cellKey.split(':');
          final rowIndex = int.parse(parts[0]);
          final colIndex = int.parse(parts[1]);

          if (rowIndex < 0 || rowIndex >= _filteredItems.length ||
              colIndex < 0 || colIndex >= _columns.length) {
            continue;
          }

          final column = _columns[colIndex];
          final itemId = _filteredItems[rowIndex]['id'] as String;
          final value = updates[itemId]![column];

          if (value != null) {
            // Update filtered items
            _filteredItems[rowIndex][column] = value;

            // Update all items
            final itemIndex = _items.indexWhere((item) => item['id'] == itemId);
            if (itemIndex >= 0) {
              _items[itemIndex][column] = value;
            }

            // Update controller
            if (_controllers.containsKey(cellKey)) {
              _controllers[cellKey]!.text = value;
            }
          }
        }
      });

      // Apply updates to Firestore using a batch
      for (var entry in updates.entries) {
        final itemId = entry.key;
        final docUpdates = entry.value;

        if (docUpdates.isNotEmpty) {
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(itemId);

          batch.update(docRef, docUpdates);
        }
      }

      // Commit batch
      batch.commit().then((_) {
        _showSnackBar('Pasted ${updatedCells.length} cells from clipboard');
      }).catchError((error) {
        _showSnackBar('Error pasting data: $error', isError: true);
        // Log the error but don't use print in production
        // print('Error pasting data: $error');
      });
    }
  }


  // Sync changes to Firebase
  Future<void> _syncChangesToFirebase() async {
    if (!_pendingChanges) {
      _showSnackBar('No changes to sync');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create a batch for all updates
      final batch = FirebaseFirestore.instance.batch();

      // Update all items in Firestore
      for (var item in _items) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(item['id'] as String);

        // Create a copy of the item without the 'id' field
        final itemData = Map<String, dynamic>.from(item);
        itemData.remove('id');

        batch.update(docRef, itemData);
      }

      // Commit the batch
      await batch.commit();

      setState(() {
        _pendingChanges = false;
        _isLoading = false;
      });

      _showSnackBar('All changes synced to database');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('Error syncing changes: $e', isError: true);
      print('Error syncing changes: $e'); // Log the error
    }
  }

  // Add new row
  Future<void> _addNewRow() async {
    final Map<String, dynamic> newItemData = {};

    // Add default empty values for all columns (except 'id')
    for (var column in _columns) {
      if (column != 'id') {
        newItemData[column] = '';
      }
    }

    try {
      // Generate a temporary ID for local use
      final tempId = DateTime.now().millisecondsSinceEpoch.toString();

      // Add to local data with the temporary ID
      final newItemWithId = {
        'id': tempId,
        ...newItemData,
      };

      setState(() {
        _items.add(newItemWithId);
        // Add to filtered items if the current filter matches (or if filter is empty)
        if (_searchController.text.isEmpty || _matchesFilter(newItemWithId, _searchController.text)) {
            _filteredItems.add(newItemWithId);

             // Add controllers and focus nodes for the new row in filtered items
             final rowIndex = _filteredItems.length - 1;
             for (var colIndex = 0; colIndex < _columns.length; colIndex++) {
               final cellKey = '$rowIndex:$colIndex';
               final column = _columns[colIndex];

               _controllers[cellKey] = TextEditingController(text: newItemWithId[column]?.toString() ?? '');

               final focusNode = FocusNode();
               focusNode.addListener(() {
                 if (focusNode.hasFocus) {
                   setState(() {
                     _isEditMode = true;
                     _selectedRow = rowIndex;
                     _selectedCol = colIndex;
                   });
                 } else {
                    final controllerValue = _controllers[cellKey]?.text ?? '';
                    _updateCellValue(rowIndex, colIndex, controllerValue);
                    setState(() {
                      _isEditMode = false;
                    });
                 }
               });
               _focusNodes[cellKey] = focusNode;
             }
        }

        // Mark that we have pending changes
        _pendingChanges = true;
      });

      _showSnackBar('New row added (locally)');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
      print('Error adding row: $e'); // Log the error
    }
  }

   // Helper to check if an item matches the current filter
   bool _matchesFilter(Map<String, dynamic> item, String query) {
      if (query.isEmpty) return true;
      final lowercaseQuery = query.toLowerCase();
      for (var column in _columns) {
         final value = item[column]?.toString().toLowerCase() ?? '';
         if (value.contains(lowercaseQuery)) {
            return true;
         }
      }
      return false;
   }


  // Delete row
  void _deleteRow(int rowIndex) {
    if (rowIndex < 0 || rowIndex >= _filteredItems.length) {
      return;
    }

    final itemId = _filteredItems[rowIndex]['id'] as String;

    setState(() {
      // Remove from filtered items
      _filteredItems.removeAt(rowIndex);
      // Remove from all items
      _items.removeWhere((item) => item['id'] == itemId);

      // Reinitialize controllers and focus nodes after deletion
      _initControllersAndFocusNodes();

      // Reset selection if the selected row was deleted or adjust index
      if (_selectedRow != null) {
         if (_selectedRow == rowIndex) {
           _selectedRow = null;
           _selectedCol = null;
           _selectedCells.clear();
         } else if (_selectedRow! > rowIndex) {
           _selectedRow = _selectedRow! - 1;
         }
      }
       // Adjust selected cells indices
      final Set<String> newSelectedCells = {};
      for (var cellKey in _selectedCells) {
         final parts = cellKey.split(':');
         final r = int.parse(parts[0]);
         final c = int.parse(parts[1]);
         if (r < rowIndex) {
           newSelectedCells.add(cellKey);
         } else if (r > rowIndex) {
           newSelectedCells.add('${r - 1}:$c');
         }
      }
      _selectedCells = newSelectedCells;

      // Mark that we have pending changes
      _pendingChanges = true;
    });

    _showSnackBar('Row deleted (locally)');
  }

  // Column operations
  void _showAddColumnDialog() {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Column'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Column Name (field_name)',
            hintText: 'Enter column name (e.g., new_field)',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final columnName = controller.text.trim();
              if (columnName.isNotEmpty) {
                _addColumn(columnName);
                Navigator.of(context).pop();
              }
            },
            style: TextButton.styleFrom(foregroundColor: widget.themeColor),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }


  Future<void> _addColumn(String columnName) async {
     // Ensure column name is a valid Firestore field name (no spaces, special chars)
     final validColumnName = columnName.replaceAll(RegExp(r'[^\w_]'), '_').toLowerCase();

     if (validColumnName.isEmpty || validColumnName == 'id') {
       _showSnackBar('Invalid column name', isError: true);
       return;
     }

     if (_columns.contains(validColumnName)) {
       _showSnackBar('Column "$columnName" already exists', isError: true);
       return;
     }

     setState(() {
       _columns.add(validColumnName);

       // Add controllers and focus nodes for the new column for all filtered rows
       for (var rowIndex = 0; rowIndex < _filteredItems.length; rowIndex++) {
         final colIndex = _columns.length - 1;
         final cellKey = '$rowIndex:$colIndex';

         _controllers[cellKey] = TextEditingController(text: '');

         final focusNode = FocusNode();
         focusNode.addListener(() {
           if (focusNode.hasFocus) {
             setState(() {
               _isEditMode = true;
               _selectedRow = rowIndex;
               _selectedCol = colIndex;
             });
           } else {
              final controllerValue = _controllers[cellKey]?.text ?? '';
              _updateCellValue(rowIndex, colIndex, controllerValue);
              setState(() {
                _isEditMode = false;
              });
           }
         });
         _focusNodes[cellKey] = focusNode;
       }

        // Add the new column with empty string value to all existing items
        for (var item in _items) {
           item[validColumnName] = '';
        }
         for (var item in _filteredItems) {
           item[validColumnName] = '';
        }
     });

     // Note: Adding a column locally doesn't automatically add the field
     // to existing Firestore documents. The field will be added to a document
     // the first time a cell in that column for that row is edited and saved.
     // If you need to add the field with a default value to all existing documents,
     // you would need to perform a batch update here.

     _showSnackBar('Column "$columnName" added');
   }


  Future<void> _deleteColumn(String column) async {
    // Don't allow deleting the ID column
    if (column == 'id') {
      _showSnackBar('Cannot delete ID column', isError: true);
      return;
    }

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Column'),
        content: Text('Are you sure you want to delete the "${_getDisplayName(column)}" column? This will remove this data from all items.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    try {
      // First update local data to ensure UI is responsive
      final colIndex = _columns.indexOf(column);
      if (colIndex == -1) {
        _showSnackBar('Column not found', isError: true);
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Update local data first
      setState(() {
        // Remove the column from the columns list
        _columns.removeAt(colIndex);

        // Remove column width mapping
        _columnWidths.remove(colIndex);

        // Adjust column width mappings for columns after the deleted one
        final Map<int, double> newColumnWidths = {};
        _columnWidths.forEach((key, value) {
          if (key < colIndex) {
            newColumnWidths[key] = value;
          } else if (key > colIndex) {
            newColumnWidths[key - 1] = value;
          }
        });
        _columnWidths.clear();
        _columnWidths.addAll(newColumnWidths);

        // Remove column data from items
        for (var item in _items) {
          item.remove(column);
        }

        for (var item in _filteredItems) {
          item.remove(column);
        }

        // Reset selection if the selected column was deleted or adjust index
        if (_selectedCol != null) {
          if (_selectedCol == colIndex) {
            _selectedRow = null;
            _selectedCol = null;
            _selectedCells.clear();
          } else if (_selectedCol! > colIndex) {
            _selectedCol = _selectedCol! - 1;
          }
        }

        // Adjust selected cells indices
        final Set<String> newSelectedCells = {};
        for (var cellKey in _selectedCells) {
          final parts = cellKey.split(':');
          final r = int.parse(parts[0]);
          final c = int.parse(parts[1]);
          if (c < colIndex) {
            newSelectedCells.add(cellKey);
          } else if (c > colIndex) {
            newSelectedCells.add('$r:${c - 1}');
          }
        }
        _selectedCells = newSelectedCells;

        // Mark that we have pending changes
        _pendingChanges = true;
      });

      // Reinitialize controllers and focus nodes after deletion
      _initControllersAndFocusNodes();

      // Now update Firestore if there are items
      if (_items.isNotEmpty) {
        final batch = FirebaseFirestore.instance.batch();

        for (var item in _items) {
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(item['id'] as String);

          // Firebase doesn't have a direct "remove field" operation,
          // so we set it to FieldValue.delete()
          batch.update(docRef, {column: FieldValue.delete()});
        }

        await batch.commit();
      }

      _showSnackBar('Column "${_getDisplayName(column)}" deleted');
    } catch (e) {
      // Even if Firestore update fails, we've already updated the local data
      // so the UI will reflect the column deletion
      _showSnackBar('Column deleted locally, but there was an error updating the database: $e', isError: true);
      // Don't use print in production
      // print('Error deleting column $column: $e');
    } finally {
      // Hide loading indicator
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Import/Export
  Future<void> _importFromExcel() async {
    try {
      // Use FilePicker for picking the file
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result == null || result.files.isEmpty) return;

      Uint8List? bytes;

      // Read file bytes
      if (result.files.first.path != null) {
        bytes = await File(result.files.first.path!).readAsBytes();
      } else if (result.files.first.bytes != null) {
        // Direct bytes from web platform
        bytes = result.files.first.bytes;
      }

      if (bytes == null) {
        _showSnackBar('Could not read file bytes', isError: true);
        return;
      }

      final excel = Excel.decodeBytes(bytes);

      if (excel.tables.isEmpty) {
        _showSnackBar('No data found in Excel file', isError: true);
        return;
      }

      // Use the first sheet
      final sheet = excel.tables.entries.first.value;

      // Get headers from first row
      final headers = <String>[];
      final headerRow = sheet.rows.first;

      for (var cell in headerRow) {
        if (cell?.value != null) {
          headers.add(cell!.value.toString().trim());
        }
      }

      if (headers.isEmpty) {
        _showSnackBar('No headers found in Excel file', isError: true);
        return;
      }

      // Option to clear existing data
      final clearExisting = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Import Options'),
          content: const Text('Do you want to clear existing data before importing?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('No, Add to Existing'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Yes, Clear Existing'),
            ),
          ],
        ),
      ) ?? false;

      if (!mounted) return;

      // Clear existing data in Firestore and local state if requested
      if (clearExisting) {
        setState(() {
           _isLoading = true; // Show loading indicator during deletion
        });
        final batch = FirebaseFirestore.instance.batch();
        for (var item in _items) {
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(item['id'] as String);
          batch.delete(docRef);
        }
        await batch.commit();

        // Clear local data and state
        setState(() {
           _items = [];
           _filteredItems = [];
           _controllers.clear();
           _focusNodes.clear();
           _selectedCells.clear();
           _selectedRow = null;
           _selectedCol = null;
        });
      }

      // Process data rows and prepare for batch write
      final batch = FirebaseFirestore.instance.batch();
      final List<Map<String, dynamic>> newItems = [];
      final Set<String> importedColumns = {'id'}; // Start with 'id'
      importedColumns.addAll(headers); // Add columns from Excel headers

      int count = 0;

      // Iterate over data rows (skip header row)
      for (var i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final Map<String, dynamic> itemData = {};

        // Populate item data based on headers
        for (var j = 0; j < headers.length && j < row.length; j++) {
          final header = headers[j];
          final cellValue = row[j]?.value?.toString() ?? '';
          itemData[header] = cellValue;
        }

        if (itemData.isNotEmpty) {
          itemData['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(); // Let Firestore generate a new ID

          batch.set(docRef, itemData);

          newItems.add({
            'id': docRef.id,
            ...itemData,
          });

          count++;
        }
      }

      // Commit the batch write to Firestore
      await batch.commit();

      if (!mounted) return;

      // Update columns in state based on imported headers and existing columns
      final Set<String> finalColumnSet = {};
      finalColumnSet.addAll(importedColumns);
      // Add any columns from existing data that weren't in the import headers
      for (var existingItem in _items) {
         finalColumnSet.addAll(existingItem.keys);
      }
       finalColumnSet.remove('createdAt');
       finalColumnSet.remove('updatedAt');


      // Update state with new columns and items
      setState(() {
        _columns = finalColumnSet.toList();
        _items.addAll(newItems);
        // Reapply filter to update filtered items
        _filterData(_searchController.text);
        // Reinitialize controllers and focus nodes for all items
        _initControllersAndFocusNodes();
      });


      _showSnackBar('Imported $count items from Excel');
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
      print('Error importing from Excel: $e'); // Log the error
    } finally {
       // Ensure loading indicator is hidden
       if (mounted) {
          setState(() {
             _isLoading = false;
          });
       }
    }
  }


  Future<void> _exportToExcel() async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1']; // Get the default sheet

      // Add headers (using display titles)
      for (var i = 0; i < _columns.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value =
            TextCellValue(_getDisplayName(_columns[i]));
      }

      // Add data from filtered items
      for (var i = 0; i < _filteredItems.length; i++) {
        final item = _filteredItems[i];

        for (var j = 0; j < _columns.length; j++) {
          final column = _columns[j];
          final cellValue = item[column]?.toString() ?? '';
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1)).value =
              TextCellValue(cellValue);
        }
      }

      // Save file based on platform
      final fileName = '${widget.title.replaceAll(' ', '_')}_export.xlsx';
      final bytes = excel.encode(); // Get file bytes

      if (bytes == null) {
         _showSnackBar('Error encoding Excel file', isError: true);
         return;
      }

      // Use FilePicker to save the file
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Excel File',
        fileName: fileName,
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
      );

      if (result != null) {
        // Write the bytes to the file
        await File(result).writeAsBytes(bytes);
        _showSnackBar('Data exported to Excel');
      }
    } catch (e) {
      _showSnackBar('Error exporting to Excel: $e', isError: true);
    }
  }


  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Helper to convert snake_case to Title Case
  String _getDisplayName(String column) {
    if (column == 'id') return 'ID'; // Special case for ID
    return column.split('_').map((word) =>
        word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  // Build UI
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        actions: [
          // Import/Export buttons
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: 'Import Excel',
            onPressed: _importFromExcel,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: 'Export Excel',
            onPressed: _exportToExcel,
          ),
        ],
      ),
      body: Column(
        children: [
          // Action buttons row
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                // Search field
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                _filterData('');
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onChanged: _filterData,
                  ),
                ),

                const SizedBox(width: 8),

                // Add row button
                ElevatedButton.icon(
                  icon: const Icon(Icons.add_box),
                  label: const Text('Add Row'),
                  onPressed: _addNewRow,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.themeColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                ),

                const SizedBox(width: 8),

                // Add column button
                ElevatedButton.icon(
                  icon: const Icon(Icons.add_chart),
                  label: const Text('Add Column'),
                  onPressed: _showAddColumnDialog,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.themeColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                ),

                const SizedBox(width: 8),

                // Sync button
                ElevatedButton.icon(
                  icon: const Icon(Icons.sync),
                  label: Text(_pendingChanges ? 'Sync Changes' : 'No Changes'),
                  onPressed: _pendingChanges ? _syncChangesToFirebase : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _pendingChanges ? Colors.green : Colors.grey,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                ),
              ],
            ),
          ),

          // Data grid
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator(color: widget.themeColor))
                : _error != null
                    ? _buildErrorWidget()
                    : _buildExcelGrid(),
          ),
        ],
      ),
       // No Floating Action Button needed as Add/Delete Row are in the action row
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildExcelGrid() {
    // Get total width needed for columns
    final totalColumnWidth = _columns.asMap().entries.fold<double>(
      0.0,
      (total, entry) => total + _getColumnWidth(entry.key)
    );

    // Calculate total width including row headers and end padding
    final totalGridWidth = _rowHeaderWidth + totalColumnWidth + 30;

    return Focus(
      focusNode: _gridFocusNode,
      onKeyEvent: _handleKeyEvent,
      child: Scrollbar(
        thickness: 16.0, // Thicker scrollbar
        thumbVisibility: true, // Always show scrollbar
        radius: const Radius.circular(8.0),
        controller: _verticalController,
        child: SingleChildScrollView(
          controller: _verticalController,
          scrollDirection: Axis.vertical,
          child: Scrollbar(
            thickness: 16.0, // Thicker scrollbar
            thumbVisibility: true, // Always show scrollbar
            radius: const Radius.circular(8.0),
            controller: _horizontalController,
            child: SingleChildScrollView(
              controller: _horizontalController,
              scrollDirection: Axis.horizontal,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row
                  Row(
                    children: [
                      // Empty corner cell
                      Container(
                        width: _rowHeaderWidth,
                        height: _headerHeight,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                      ),

                      // Column headers
                      ..._columns.asMap().entries.map((entry) {
                        final colIndex = entry.key;
                        final column = entry.value;

                        return Stack(
                          children: [
                            // Column header
                            GestureDetector(
                              onDoubleTap: () => _editColumnHeader(column),
                              child: Container(
                                width: _getColumnWidth(colIndex),
                                height: _headerHeight,
                                decoration: BoxDecoration(
                                  color: widget.themeColor,
                                  border: Border.all(color: Colors.grey.shade400),
                                ),
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                alignment: Alignment.centerLeft,
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        _getDisplayName(column),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    if (column != 'id')
                                      IconButton(
                                        icon: const Icon(Icons.close, color: Colors.white, size: 16),
                                        onPressed: () => _deleteColumn(column),
                                        tooltip: 'Delete Column',
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(),
                                      ),
                                  ],
                                ),
                              ),
                            ),

                            // Column resize handle - improved for better responsiveness
                            Positioned(
                              right: 0,
                              top: 0,
                              bottom: 0,
                              child: MouseRegion(
                                cursor: SystemMouseCursors.resizeLeftRight,
                                child: GestureDetector(
                                  onHorizontalDragStart: (details) {
                                    _startResizingColumn(colIndex, details.globalPosition.dx);
                                  },
                                  onHorizontalDragUpdate: (details) {
                                    // Calculate the drag distance from the start position
                                    final dragDistance = details.globalPosition.dx - _resizeStartPosition;
                                    _updateColumnResize(dragDistance);
                                  },
                                  onHorizontalDragEnd: (_) {
                                    _endResizing();
                                  },
                                  // Wider handle for easier grabbing
                                  child: Container(
                                    width: 12,
                                    decoration: BoxDecoration(
                                      // Visual indicator for the resize handle
                                      color: Colors.transparent,
                                      border: Border(
                                        right: BorderSide(
                                          color: Colors.grey.shade400,
                                          width: 2,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      }),

                      // End of header row padding
                      Container(
                        width: 30,
                        height: _headerHeight,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                      ),
                    ],
                  ),

                  // Data rows
                  ..._filteredItems.asMap().entries.map((entry) {
                    final rowIndex = entry.key;
                    final item = entry.value;

                    return Stack(
                      children: [
                        Row(
                          children: [
                            // Row header
                            Container(
                              width: _rowHeaderWidth,
                              height: _getRowHeight(rowIndex),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                border: Border.all(color: Colors.grey.shade400),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left: 4),
                                    child: Text(
                                      '${rowIndex + 1}',
                                      style: TextStyle(
                                        color: Colors.grey.shade700,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                                    onPressed: () => _deleteRow(rowIndex),
                                    tooltip: 'Delete Row',
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                  ),
                                ],
                              ),
                            ),

                            // Data cells
                            ..._columns.asMap().entries.map((colEntry) {
                              final colIndex = colEntry.key;
                              final column = colEntry.value;

                              return _buildCell(item, column, rowIndex, colIndex);
                            }),

                            // End of row padding
                            Container(
                              width: 30,
                              height: _getRowHeight(rowIndex),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade100,
                                border: Border.all(color: Colors.grey.shade400),
                              ),
                            ),
                          ],
                        ),

                        // Row resize handle - improved for better responsiveness
                        Positioned(
                          left: 0,
                          right: 0,
                          bottom: 0,
                          child: MouseRegion(
                            cursor: SystemMouseCursors.resizeUpDown,
                            child: GestureDetector(
                              onVerticalDragStart: (details) {
                                _startResizingRow(rowIndex, details.globalPosition.dy);
                              },
                              onVerticalDragUpdate: (details) {
                                // Calculate the drag distance from the start position
                                final dragDistance = details.globalPosition.dy - _resizeStartPosition;
                                _updateRowResize(dragDistance);
                              },
                              onVerticalDragEnd: (_) {
                                _endResizing();
                              },
                              // Taller handle for easier grabbing
                              child: Container(
                                height: 12,
                                decoration: BoxDecoration(
                                  // Visual indicator for the resize handle
                                  color: Colors.transparent,
                                  border: Border(
                                    bottom: BorderSide(
                                      color: Colors.grey.shade400,
                                      width: 2,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),

                  // Empty space at the bottom
                  Container(
                     // Match the total width of the grid rows
                    width: totalGridWidth,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      border: Border.all(color: Colors.grey.shade400),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCell(Map<String, dynamic> item, String column, int rowIndex, int colIndex) {
    final isSelected = _selectedCells.contains('$rowIndex:$colIndex');
    final isFocused = _selectedRow == rowIndex && _selectedCol == colIndex;
    final isEditing = isFocused && _isEditMode;
    final cellKey = '$rowIndex:$colIndex';

    // Ensure we have a controller and focus node for this cell
    if (!_controllers.containsKey(cellKey)) {
      // Initialize controller with the current value
      _controllers[cellKey] = TextEditingController(text: item[column]?.toString() ?? '');

      final focusNode = FocusNode();
      focusNode.addListener(() {
        if (focusNode.hasFocus) {
          setState(() {
            _isEditMode = true;
            _selectedRow = rowIndex;
            _selectedCol = colIndex;
          });
        } else {
          // When focus is lost, update the cell value in local data and Firestore
          final controllerValue = _controllers[cellKey]?.text ?? '';
          _updateCellValue(rowIndex, colIndex, controllerValue);
          setState(() {
            _isEditMode = false;
          });
        }
      });
      _focusNodes[cellKey] = focusNode;
    }

    // Update the controller's text if the underlying data changes while not editing
    if (!isEditing) {
      final currentValue = item[column]?.toString() ?? '';
      if ((_controllers[cellKey]?.text ?? '') != currentValue) {
        _controllers[cellKey]?.text = currentValue;
      }
    }

    // Build the cell content
    Widget cellContent;
    if (isEditing) {
      cellContent = TextField(
        controller: _controllers[cellKey],
        focusNode: _focusNodes[cellKey],
        decoration: const InputDecoration(
          border: InputBorder.none,
          isDense: true,
          contentPadding: EdgeInsets.zero,
        ),
        style: const TextStyle(fontSize: 14),
        maxLines: null, // Allows multiple lines
        keyboardType: TextInputType.multiline,
        textInputAction: TextInputAction.newline,
        onSubmitted: (value) {
          _focusNodes[cellKey]?.unfocus();
          Future.delayed(const Duration(milliseconds: 50), () {
            _moveSelection(1, 0);
          });
        },
      );
    } else {
      cellContent = SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Text(
          item[column]?.toString() ?? '',
          style: const TextStyle(fontSize: 14),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      );
    }

    // Build the cell container
    final cellContainer = Container(
      width: _getColumnWidth(colIndex),
      height: _getRowHeight(rowIndex),
      decoration: BoxDecoration(
        color: isEditing
            ? Colors.white
            : isFocused
                ? widget.themeColor.withAlpha(51)
                : isSelected
                    ? widget.themeColor.withAlpha(26)
                    : Colors.white,
        border: Border.all(
          color: isFocused
              ? widget.themeColor
              : isSelected
                  ? widget.themeColor.withAlpha(128)
                  : Colors.grey.shade400,
          width: isFocused ? 2 : 1,
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      alignment: Alignment.centerLeft,
      child: cellContent,
    );

    // Wrap with gesture detector for interaction
    final gestureDetector = GestureDetector(
      onTap: () => _selectCell(rowIndex, colIndex),
      onDoubleTap: () => _selectCell(rowIndex, colIndex, startEditing: true),
      onPanStart: (details) => _startSelecting(rowIndex, colIndex),
      onPanEnd: (_) => _endSelecting(),
      child: cellContainer,
    );

    // Wrap with mouse region for hover detection
    return MouseRegion(
      onEnter: (_) {
        if (_isSelecting) {
          _updateSelection(rowIndex, colIndex);
        }
      },
      child: gestureDetector,
    );
  }
}
