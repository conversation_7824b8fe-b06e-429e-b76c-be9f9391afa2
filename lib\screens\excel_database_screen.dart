import 'package:flutter/material.dart';
import '../screens/complete_excel_grid_factory.dart';

class ExcelDatabaseScreen extends StatefulWidget {
  const ExcelDatabaseScreen({Key? key}) : super(key: key);

  @override
  State<ExcelDatabaseScreen> createState() => _ExcelDatabaseScreenState();
}

class _ExcelDatabaseScreenState extends State<ExcelDatabaseScreen> {
  String _selectedSystem = 'alarm';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Management'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _selectedSystem = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'alarm',
                child: Text('Fire Alarm Systems'),
              ),
              const PopupMenuItem(
                value: 'water',
                child: Text('Water Systems'),
              ),
              const PopupMenuItem(
                value: 'foam',
                child: Text('Foam Systems'),
              ),
              const PopupMenuItem(
                value: 'fm200',
                child: Text('FM200 Systems'),
              ),
              const PopupMenuItem(
                value: 'novec',
                child: Text('Novec Systems'),
              ),
              const PopupMenuItem(
                value: 'co2',
                child: Text('CO2 Systems'),
              ),
              const PopupMenuItem(
                value: 'materials',
                child: Text('Materials'),
              ),
            ],
          ),
        ],
      ),
      body: CompleteExcelGridFactory.createScreen(_selectedSystem),
    );
  }
}
