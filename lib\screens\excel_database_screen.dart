import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/database_service.dart';
import '../services/excel_service.dart';
import '../services/supabase_service.dart';
import '../widgets/excel_grid_widget.dart';

class ExcelDatabaseScreen extends StatefulWidget {
  const ExcelDatabaseScreen({Key? key}) : super(key: key);

  @override
  State<ExcelDatabaseScreen> createState() => _ExcelDatabaseScreenState();
}

class _ExcelDatabaseScreenState extends State<ExcelDatabaseScreen> {
  String? _selectedTable;
  List<String> _tableNames = [];
  bool _isLoading = true;
  String? _error;

  late DatabaseService _databaseService;
  late ExcelService _excelService;
  late SupabaseService _supabaseService;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    _databaseService = Provider.of<DatabaseService>(context, listen: false);
    _excelService = Provider.of<ExcelService>(context, listen: false);
    _supabaseService = Provider.of<SupabaseService>(context, listen: false);

    await _databaseService.initialize();
    await _loadTables();
  }

  Future<void> _loadTables() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final tables = await _databaseService.getTableNames();
      setState(() {
        _tableNames = tables;
        if (tables.isNotEmpty && _selectedTable == null) {
          _selectedTable = tables.first;
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _importExcel() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _excelService.importExcelFile();

      if (result['success']) {
        await _loadTables();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error importing file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _exportTable() async {
    if (_selectedTable == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _excelService.exportTableToExcel(_selectedTable!);

      if (result['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error exporting table: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _uploadToSupabase() async {
    if (_selectedTable == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _supabaseService.uploadTable(_selectedTable!);

      if (result['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error uploading to Supabase: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _syncWithSupabase() async {
    if (_selectedTable == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _supabaseService.syncTable(_selectedTable!);

      if (result['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error syncing with Supabase: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Excel Grid Database'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: 'Import Excel/CSV',
            onPressed: _isLoading ? null : _importExcel,
          ),
          if (_selectedTable != null) ...[
            IconButton(
              icon: const Icon(Icons.file_download),
              tooltip: 'Export to Excel',
              onPressed: _isLoading ? null : _exportTable,
            ),
            IconButton(
              icon: const Icon(Icons.cloud_upload),
              tooltip: 'Upload to Supabase',
              onPressed: _isLoading ? null : _uploadToSupabase,
            ),
            IconButton(
              icon: const Icon(Icons.sync),
              tooltip: 'Sync with Supabase',
              onPressed: _isLoading ? null : _syncWithSupabase,
            ),
          ],
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _isLoading ? null : _loadTables,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, size: 64, color: Colors.red.shade300),
                      const SizedBox(height: 16),
                      Text('Error: $_error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadTables,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _tableNames.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.table_chart, size: 64, color: Colors.grey.shade400),
                          const SizedBox(height: 16),
                          const Text(
                            'No tables found',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          const Text('Import an Excel or CSV file to get started'),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: _importExcel,
                            icon: const Icon(Icons.file_upload),
                            label: const Text('Import Excel/CSV'),
                          ),
                        ],
                      ),
                    )
                  : Column(
                      children: [
                        // Table selector
                        Container(
                          padding: const EdgeInsets.all(16),
                          color: Colors.grey.shade100,
                          child: Row(
                            children: [
                              const Text(
                                'Select Table: ',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Expanded(
                                child: DropdownButton<String>(
                                  value: _selectedTable,
                                  isExpanded: true,
                                  items: _tableNames.map((tableName) {
                                    return DropdownMenuItem(
                                      value: tableName,
                                      child: Text(tableName),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedTable = value;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Excel Grid
                        Expanded(
                          child: _selectedTable != null
                              ? ExcelGridWidget(
                                  tableName: _selectedTable!,
                                  databaseService: _databaseService,
                                )
                              : const Center(child: Text('Select a table to view')),
                        ),
                      ],
                    ),
    );
  }
}
