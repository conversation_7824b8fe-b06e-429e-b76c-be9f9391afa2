// This is a mock implementation of the Firebase service for testing purposes
// It doesn't actually connect to Firebase, but provides the same interface

class MockFirebaseUser {
  final String? email;
  final String? uid;

  MockFirebaseUser({this.email, this.uid});
}

class MockFirebaseService {
  MockFirebaseUser? _currentUser;

  // Get the current user
  MockFirebaseUser? getCurrentUser() {
    return _currentUser;
  }

  // Sign in with email and password
  Future<MockFirebaseUser?> signIn(String email, String password) async {
    // For testing, we'll accept any email/password combination
    _currentUser = MockFirebaseUser(
      email: email,
      uid: 'mock-uid-${DateTime.now().millisecondsSinceEpoch}',
    );
    return _currentUser;
  }

  // Sign up with email and password
  Future<MockFirebaseUser?> signUp(String email, String password) async {
    // For testing, we'll accept any email/password combination
    _currentUser = MockFirebaseUser(
      email: email,
      uid: 'mock-uid-${DateTime.now().millisecondsSinceEpoch}',
    );
    return _currentUser;
  }

  // Sign out
  Future<void> signOut() async {
    _currentUser = null;
  }
}
