import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:csv/csv.dart';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

/// A utility class for managing SQLite database schema and data at runtime
class SQLiteSchemaManager {
  /// Adds a new column to an existing table
  ///
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to modify
  /// - [columnName]: The name of the column to add
  /// - [columnType]: The SQLite data type (TEXT, INTEGER, REAL, etc.)
  ///
  /// Returns: true if successful, false otherwise
  static Future<bool> addColumn(
    Database db,
    String tableName,
    String columnName,
    String columnType,
  ) async {
    try {
      // Validate inputs to prevent SQL injection
      _validateIdentifier(tableName);
      _validateIdentifier(columnName);
      _validateColumnType(columnType);

      // Execute the ALTER TABLE statement
      await db.execute(
        'ALTER TABLE "$tableName" ADD COLUMN "$columnName" $columnType',
      );

      return true;
    } catch (e) {
      debugPrint('Error adding column: $e');
      return false;
    }
  }

  /// Updates a single cell value in the database
  ///
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to update
  /// - [columnName]: The name of the column to update
  /// - [value]: The new value for the column
  /// - [where]: The WHERE clause to identify rows to update
  /// - [whereArgs]: Arguments for the WHERE clause
  ///
  /// Returns: The number of rows updated
  static Future<int> updateCell(
    Database db,
    String tableName,
    String columnName,
    dynamic value,
    String where,
    List<dynamic> whereArgs,
  ) async {
    try {
      // Validate inputs to prevent SQL injection
      _validateIdentifier(tableName);
      _validateIdentifier(columnName);

      // Create a map with just the one column to update
      final Map<String, dynamic> values = {columnName: value};

      // Execute the update
      return await db.update(
        tableName,
        values,
        where: where,
        whereArgs: whereArgs,
      );
    } catch (e) {
      debugPrint('Error updating cell: $e');
      return 0;
    }
  }

  /// Drops a column from a table by recreating the table without that column
  ///
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to modify
  /// - [columnToRemove]: The name of the column to remove
  ///
  /// Returns: true if successful, false otherwise
  static Future<bool> dropColumn(
    Database db,
    String tableName,
    String columnToRemove,
  ) async {
    try {
      // Validate inputs to prevent SQL injection
      _validateIdentifier(tableName);
      _validateIdentifier(columnToRemove);

      // Start a transaction for atomicity
      return await db.transaction((txn) async {
        // 1. Get table info
        final List<Map<String, dynamic>> tableInfo = await txn.rawQuery(
          'PRAGMA table_info("$tableName")',
        );

        // 2. Check if the column exists
        final columnExists = tableInfo.any(
          (column) => column['name'] == columnToRemove,
        );

        if (!columnExists) {
          debugPrint('Column $columnToRemove does not exist in table $tableName');
          return false;
        }

        // 3. Create a list of columns excluding the one to remove
        final List<Map<String, dynamic>> columnsToKeep = tableInfo
            .where((column) => column['name'] != columnToRemove)
            .toList();

        if (columnsToKeep.isEmpty) {
          debugPrint('Cannot remove the only column from the table');
          return false;
        }

        // 4. Generate column definitions for the new table
        final List<String> columnDefs = columnsToKeep.map((column) {
          final name = column['name'];
          final type = column['type'];
          final notNull = column['notnull'] == 1 ? 'NOT NULL' : '';
          final defaultValue = column['dflt_value'] != null
              ? "DEFAULT '${column['dflt_value']}'"
              : '';
          final primaryKey = column['pk'] == 1 ? 'PRIMARY KEY' : '';

          return '"$name" $type $notNull $defaultValue $primaryKey'.trim();
        }).toList();

        // 5. Generate column names for data transfer
        final List<String> columnNames = columnsToKeep
            .map((column) => '"${column['name']}"')
            .toList();

        // 6. Create a temporary table with the new schema
        final String tempTableName = '${tableName}_temp';
        await txn.execute(
          'CREATE TABLE "$tempTableName" (${columnDefs.join(', ')})',
        );

        // 7. Copy data from the original table to the temporary table
        await txn.execute(
          'INSERT INTO "$tempTableName" (${columnNames.join(', ')}) '
          'SELECT ${columnNames.join(', ')} FROM "$tableName"',
        );

        // 8. Drop the original table
        await txn.execute('DROP TABLE "$tableName"');

        // 9. Rename the temporary table to the original table name
        await txn.execute(
          'ALTER TABLE "$tempTableName" RENAME TO "$tableName"',
        );

        return true;
      });
    } catch (e) {
      debugPrint('Error dropping column: $e');
      return false;
    }
  }

  /// Imports data from a CSV file into a SQLite table
  ///
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to import into
  /// - [filePath]: The path to the CSV file
  /// - [replaceExisting]: Whether to replace existing data or append
  ///
  /// Returns: The number of rows imported
  static Future<int> importFromCSV(
    Database db,
    String tableName,
    String filePath,
    {bool replaceExisting = false}
  ) async {
    try {
      // Validate inputs
      _validateIdentifier(tableName);

      // Read the CSV file
      final File file = File(filePath);
      final String csvString = await file.readAsString();
      final List<List<dynamic>> csvTable = const CsvToListConverter().convert(csvString);

      if (csvTable.isEmpty) {
        return 0;
      }

      // Get the header row (column names)
      final List<String> headers = csvTable[0].map((e) => e.toString()).toList();

      // Start a transaction
      return await db.transaction((txn) async {
        // If replacing existing data, delete all rows
        if (replaceExisting) {
          await txn.delete(tableName);
        }

        // Check if all columns exist, create them if they don't
        final List<Map<String, dynamic>> tableInfo = await txn.rawQuery(
          'PRAGMA table_info("$tableName")',
        );

        final List<String> existingColumns = tableInfo
            .map((column) => column['name'].toString())
            .toList();

        // Add any missing columns
        for (final header in headers) {
          if (!existingColumns.contains(header) && header != 'id') {
            await addColumn(db, tableName, header, 'TEXT');
          }
        }

        // Insert the data rows
        int count = 0;
        for (int i = 1; i < csvTable.length; i++) {
          final Map<String, dynamic> row = {};

          for (int j = 0; j < headers.length && j < csvTable[i].length; j++) {
            row[headers[j]] = csvTable[i][j];
          }

          await txn.insert(tableName, row);
          count++;
        }

        return count;
      });
    } catch (e) {
      debugPrint('Error importing from CSV: $e');
      return 0;
    }
  }

  /// Exports data from a SQLite table to a CSV file
  ///
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to export
  /// - [filePath]: The path where the CSV file should be saved
  ///
  /// Returns: The number of rows exported
  static Future<int> exportToCSV(
    Database db,
    String tableName,
    String filePath,
  ) async {
    try {
      // Validate inputs
      _validateIdentifier(tableName);

      // Query all data from the table
      final List<Map<String, dynamic>> data = await db.query(tableName);

      if (data.isEmpty) {
        return 0;
      }

      // Get column names from the first row
      final List<String> headers = data[0].keys.toList();

      // Convert data to CSV format
      final List<List<dynamic>> csvData = [headers];

      for (final row in data) {
        final List<dynamic> csvRow = headers.map((header) => row[header]).toList();
        csvData.add(csvRow);
      }

      // Convert to CSV string
      final String csv = const ListToCsvConverter().convert(csvData);

      // Write to file
      final File file = File(filePath);
      await file.writeAsString(csv);

      return data.length;
    } catch (e) {
      debugPrint('Error exporting to CSV: $e');
      return 0;
    }
  }

  /// Imports data from an Excel file into a SQLite table
  ///
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to import into
  /// - [filePath]: The path to the Excel file
  /// - [sheetName]: The name of the sheet to import (optional)
  /// - [replaceExisting]: Whether to replace existing data or append
  ///
  /// Returns: The number of rows imported
  static Future<int> importFromExcel(
    Database db,
    String tableName,
    String filePath,
    {String? sheetName, bool replaceExisting = false}
  ) async {
    try {
      // Validate inputs
      _validateIdentifier(tableName);

      // Read the Excel file
      final File file = File(filePath);
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      // Get the sheet to import
      final sheet = sheetName != null && excel.tables.containsKey(sheetName)
          ? excel.tables[sheetName]!
          : excel.tables.values.first;

      if (sheet.rows.isEmpty) {
        return 0;
      }

      // Get the header row (column names)
      final List<String> headers = [];
      for (final cell in sheet.rows[0]) {
        if (cell?.value != null) {
          headers.add(cell!.value.toString());
        }
      }

      // Start a transaction
      return await db.transaction((txn) async {
        // If replacing existing data, delete all rows
        if (replaceExisting) {
          await txn.delete(tableName);
        }

        // Check if all columns exist, create them if they don't
        final List<Map<String, dynamic>> tableInfo = await txn.rawQuery(
          'PRAGMA table_info("$tableName")',
        );

        final List<String> existingColumns = tableInfo
            .map((column) => column['name'].toString())
            .toList();

        // Add any missing columns
        for (final header in headers) {
          if (!existingColumns.contains(header) && header != 'id') {
            await addColumn(db, tableName, header, 'TEXT');
          }
        }

        // Insert the data rows
        int count = 0;
        for (int i = 1; i < sheet.rows.length; i++) {
          final Map<String, dynamic> row = {};

          for (int j = 0; j < headers.length && j < sheet.rows[i].length; j++) {
            final cell = sheet.rows[i][j];
            if (cell?.value != null) {
              row[headers[j]] = cell!.value.toString();
            }
          }

          if (row.isNotEmpty) {
            await txn.insert(tableName, row);
            count++;
          }
        }

        return count;
      });
    } catch (e) {
      debugPrint('Error importing from Excel: $e');
      return 0;
    }
  }

  /// Exports data from a SQLite table to an Excel file
  ///
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to export
  /// - [filePath]: The path where the Excel file should be saved
  /// - [sheetName]: The name of the sheet to create (optional)
  ///
  /// Returns: The number of rows exported
  static Future<int> exportToExcel(
    Database db,
    String tableName,
    String filePath,
    {String? sheetName}
  ) async {
    try {
      // Validate inputs
      _validateIdentifier(tableName);

      // Query all data from the table
      final List<Map<String, dynamic>> data = await db.query(tableName);

      if (data.isEmpty) {
        return 0;
      }

      // Get column names from the first row
      final List<String> headers = data[0].keys.toList();

      // Create Excel workbook
      final excel = Excel.createExcel();

      // Use the provided sheet name or default to the table name
      final sheetKey = sheetName ?? tableName;

      // Use the default sheet or create a new one
      final defaultSheet = excel.tables.keys.first;
      if (sheetKey != defaultSheet) {
        // Rename the default sheet
        excel.rename(defaultSheet, sheetKey);
      }

      // Add headers
      for (int i = 0; i < headers.length; i++) {
        excel.tables[sheetKey]!.cell(CellIndex.indexByColumnRow(
          columnIndex: i,
          rowIndex: 0,
        )).value = TextCellValue(headers[i]);
      }

      // Add data rows
      for (int i = 0; i < data.length; i++) {
        for (int j = 0; j < headers.length; j++) {
          final value = data[i][headers[j]];
          if (value != null) {
            excel.tables[sheetKey]!.cell(CellIndex.indexByColumnRow(
              columnIndex: j,
              rowIndex: i + 1,
            )).value = TextCellValue(value.toString());
          }
        }
      }

      // Save to file
      final File file = File(filePath);
      await file.writeAsBytes(excel.encode()!);

      return data.length;
    } catch (e) {
      debugPrint('Error exporting to Excel: $e');
      return 0;
    }
  }

  /// Shows a file picker dialog and imports data from the selected file
  ///
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to import into
  /// - [replaceExisting]: Whether to replace existing data or append
  ///
  /// Returns: The number of rows imported
  static Future<int> importFromFile(
    Database db,
    String tableName,
    {bool replaceExisting = false}
  ) async {
    try {
      // Show file picker
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'xlsx', 'xls'],
      );

      if (result == null || result.files.isEmpty) {
        return 0;
      }

      final file = result.files.first;
      final filePath = file.path;

      if (filePath == null) {
        return 0;
      }

      // Determine file type and import accordingly
      if (filePath.endsWith('.csv')) {
        return await importFromCSV(db, tableName, filePath, replaceExisting: replaceExisting);
      } else if (filePath.endsWith('.xlsx') || filePath.endsWith('.xls')) {
        return await importFromExcel(db, tableName, filePath, replaceExisting: replaceExisting);
      }

      return 0;
    } catch (e) {
      debugPrint('Error importing from file: $e');
      return 0;
    }
  }

  /// Shows a file picker dialog for saving and exports data to the selected file
  ///
  /// Parameters:
  /// - [db]: The database instance
  /// - [tableName]: The name of the table to export
  ///
  /// Returns: The number of rows exported
  static Future<int> exportToFile(
    Database db,
    String tableName,
  ) async {
    try {
      // Get the documents directory for saving
      final directory = await getApplicationDocumentsDirectory();
      final downloadsPath = path.join(directory.path, 'exports');

      // Create the directory if it doesn't exist
      final dir = Directory(downloadsPath);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      // Generate a filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final csvPath = path.join(downloadsPath, '${tableName}_$timestamp.csv');
      final excelPath = path.join(downloadsPath, '${tableName}_$timestamp.xlsx');

      // Export to both formats
      final csvCount = await exportToCSV(db, tableName, csvPath);
      await exportToExcel(db, tableName, excelPath);

      // Return the CSV count
      return csvCount;
    } catch (e) {
      debugPrint('Error exporting to file: $e');
      return 0;
    }
  }

  // Helper methods for validation

  /// Validates an SQL identifier (table or column name)
  static void _validateIdentifier(String identifier) {
    if (identifier.isEmpty) {
      throw ArgumentError('Identifier cannot be empty');
    }

    if (!RegExp(r'^[a-zA-Z_][a-zA-Z0-9_]*$').hasMatch(identifier)) {
      throw ArgumentError('Invalid identifier: $identifier');
    }
  }

  /// Validates a column type
  static void _validateColumnType(String columnType) {
    final validTypes = [
      'TEXT', 'INTEGER', 'REAL', 'BLOB', 'NUMERIC',
      'BOOLEAN', 'DATE', 'DATETIME', 'TIME',
    ];

    final upperType = columnType.toUpperCase();

    if (!validTypes.contains(upperType) &&
        !upperType.startsWith('VARCHAR') &&
        !upperType.startsWith('CHAR')) {
      throw ArgumentError('Invalid column type: $columnType');
    }
  }
}
