import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../models/project.dart';
import '../services/project_provider.dart';
import 'system_detail_screen.dart';

class AddSystemScreen extends StatefulWidget {
  const AddSystemScreen({super.key});

  @override
  State<AddSystemScreen> createState() => _AddSystemScreenState();
}

class _AddSystemScreenState extends State<AddSystemScreen> {
  // Map of system types to their icons
  final Map<String, IconData> _systemIcons = {
    'Fire Alarm': Icons.notifications_active,
    'Water Sprinkler': Icons.water_drop,
    'Foam System': Icons.bubble_chart,
    'Novec 1230': Icons.science,
    'FM-200': Icons.science,
    'CO2 System': Icons.gas_meter,
    'Emergency Lighting': Icons.lightbulb,
    'Fire Extinguishers': Icons.fire_extinguisher,
    'Fire Hydrant': Icons.water,
    'Fire Hose Reel': Icons.water,
    'Deluge System': Icons.water_drop,
    'Pre-Action System': Icons.water_drop,
    'Dry Chemical System': Icons.science,
  };

  // Get default icon if not found in the map
  IconData _getIconForSystem(String systemType) {
    return _systemIcons[systemType] ?? Icons.build;
  }

  // Generate a system name based on the selected type
  String _getSystemName(String systemType) {
    // Get count of existing systems of this type
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
    final project = projectProvider.currentProject;

    if (project == null) return systemType;

    // Count existing systems of the same type
    int count = project.systems.where((s) => s.type == systemType).length + 1;

    // Return the system type with a number if there are multiple systems of the same type
    return count > 1 ? '$systemType $count' : systemType;
  }

  void _addSystem(String systemType) {
    // Generate system name based on selected type
    final systemName = _getSystemName(systemType);

    final system = SystemEstimate(
      name: systemName,
      type: systemType,
    );

    Provider.of<ProjectProvider>(context, listen: false).addSystem(system);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$systemName system added')),
    );

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => SystemDetailScreen(systemId: system.id),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add System'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Select System Type',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Tap on a system type to add it to your project',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: AppConstants.systemTypes.length,
                itemBuilder: (context, index) {
                  final systemType = AppConstants.systemTypes[index];
                  final systemName = _getSystemName(systemType);

                  return _buildSystemListItem(
                    systemType: systemType,
                    systemName: systemName,
                    icon: _getIconForSystem(systemType),
                    onTap: () => _addSystem(systemType),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemListItem({
    required String systemType,
    required String systemName,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 24,
            color: AppConstants.primaryColor,
          ),
        ),
        title: Text(
          systemType,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: systemName != systemType
            ? Text(
                'Will be named: $systemName',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              )
            : null,

        onTap: onTap,
      ),
    );
  }
}
