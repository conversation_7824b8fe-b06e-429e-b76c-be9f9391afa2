import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'database_service.dart';

class ExcelService {
  final DatabaseService _databaseService = DatabaseService();
  
  // Import Excel file and create tables
  Future<Map<String, dynamic>> importExcelFile() async {
    try {
      // Pick Excel file
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
        allowMultiple: false,
      );
      
      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;
        
        if (fileName.endsWith('.csv')) {
          return await _importCsvFile(file, fileName);
        } else {
          return await _importExcelFile(file, fileName);
        }
      }
      
      return {'success': false, 'message': 'No file selected'};
    } catch (e) {
      return {'success': false, 'message': 'Error importing file: $e'};
    }
  }
  
  // Import Excel file (.xlsx, .xls)
  Future<Map<String, dynamic>> _importExcelFile(File file, String fileName) async {
    try {
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);
      
      int tablesCreated = 0;
      int totalRows = 0;
      List<String> tableNames = [];
      
      for (String tableName in excel.tables.keys) {
        final table = excel.tables[tableName];
        if (table == null || table.rows.isEmpty) continue;
        
        // Get headers from first row
        final headerRow = table.rows.first;
        final headers = <String>[];
        final columnTypes = <String, String>{};
        
        for (int i = 0; i < headerRow.length; i++) {
          final cellValue = headerRow[i]?.value?.toString() ?? 'Column_$i';
          final cleanHeader = _cleanColumnName(cellValue);
          headers.add(cleanHeader);
          columnTypes[cleanHeader] = 'TEXT'; // Default to TEXT, will be inferred
        }
        
        // Infer column types from data
        if (table.rows.length > 1) {
          _inferColumnTypes(table.rows.skip(1).toList(), headers, columnTypes);
        }
        
        // Create table with unique name
        final uniqueTableName = await _getUniqueTableName(tableName);
        await _databaseService.createTable(uniqueTableName, columnTypes);
        
        // Import data rows
        final dataRows = <Map<String, dynamic>>[];
        for (int rowIndex = 1; rowIndex < table.rows.length; rowIndex++) {
          final row = table.rows[rowIndex];
          final rowData = <String, dynamic>{};
          
          for (int colIndex = 0; colIndex < headers.length && colIndex < row.length; colIndex++) {
            final cellValue = row[colIndex]?.value;
            rowData[headers[colIndex]] = _convertCellValue(cellValue, columnTypes[headers[colIndex]] ?? 'TEXT');
          }
          
          if (rowData.isNotEmpty) {
            dataRows.add(rowData);
          }
        }
        
        if (dataRows.isNotEmpty) {
          await _databaseService.importData(uniqueTableName, dataRows);
          totalRows += dataRows.length;
        }
        
        tablesCreated++;
        tableNames.add(uniqueTableName);
      }
      
      return {
        'success': true,
        'message': 'Successfully imported $tablesCreated tables with $totalRows total rows',
        'tables_created': tablesCreated,
        'total_rows': totalRows,
        'table_names': tableNames,
      };
    } catch (e) {
      return {'success': false, 'message': 'Error importing Excel file: $e'};
    }
  }
  
  // Import CSV file
  Future<Map<String, dynamic>> _importCsvFile(File file, String fileName) async {
    try {
      final content = await file.readAsString();
      final lines = content.split('\n').where((line) => line.trim().isNotEmpty).toList();
      
      if (lines.isEmpty) {
        return {'success': false, 'message': 'CSV file is empty'};
      }
      
      // Parse headers
      final headers = lines.first.split(',').map((h) => _cleanColumnName(h.trim())).toList();
      final columnTypes = <String, String>{};
      
      // Initialize column types
      for (String header in headers) {
        columnTypes[header] = 'TEXT';
      }
      
      // Parse data and infer types
      final dataRows = <List<String>>[];
      for (int i = 1; i < lines.length; i++) {
        final values = lines[i].split(',').map((v) => v.trim()).toList();
        if (values.length == headers.length) {
          dataRows.add(values);
        }
      }
      
      // Infer column types from data
      _inferColumnTypesFromStrings(dataRows, headers, columnTypes);
      
      // Create table
      final tableName = await _getUniqueTableName(path.basenameWithoutExtension(fileName));
      await _databaseService.createTable(tableName, columnTypes);
      
      // Import data
      final importData = <Map<String, dynamic>>[];
      for (final row in dataRows) {
        final rowData = <String, dynamic>{};
        for (int i = 0; i < headers.length && i < row.length; i++) {
          rowData[headers[i]] = _convertStringValue(row[i], columnTypes[headers[i]] ?? 'TEXT');
        }
        if (rowData.isNotEmpty) {
          importData.add(rowData);
        }
      }
      
      if (importData.isNotEmpty) {
        await _databaseService.importData(tableName, importData);
      }
      
      return {
        'success': true,
        'message': 'Successfully imported CSV with ${importData.length} rows',
        'tables_created': 1,
        'total_rows': importData.length,
        'table_names': [tableName],
      };
    } catch (e) {
      return {'success': false, 'message': 'Error importing CSV file: $e'};
    }
  }
  
  // Export table to Excel
  Future<Map<String, dynamic>> exportTableToExcel(String tableName) async {
    try {
      final schema = await _databaseService.getTableSchema(tableName);
      final data = await _databaseService.getTableData(tableName);
      
      if (schema == null || data.isEmpty) {
        return {'success': false, 'message': 'Table is empty or does not exist'};
      }
      
      // Create Excel workbook
      final excel = Excel.createExcel();
      final sheet = excel[excel.getDefaultSheet()!];
      
      // Add headers
      final headers = schema.keys.where((key) => !key.startsWith('_')).toList();
      for (int i = 0; i < headers.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value = headers[i];
      }
      
      // Add data rows
      for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
        final row = data[rowIndex];
        for (int colIndex = 0; colIndex < headers.length; colIndex++) {
          final value = row[headers[colIndex]];
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex, rowIndex: rowIndex + 1)).value = value;
        }
      }
      
      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final filePath = path.join(directory.path, '$tableName.xlsx');
      final file = File(filePath);
      await file.writeAsBytes(excel.encode()!);
      
      return {
        'success': true,
        'message': 'Successfully exported to $filePath',
        'file_path': filePath,
      };
    } catch (e) {
      return {'success': false, 'message': 'Error exporting to Excel: $e'};
    }
  }
  
  // Clean column name to be database-friendly
  String _cleanColumnName(String name) {
    return name
        .replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .replaceAll(RegExp(r'^_|_$'), '')
        .toLowerCase();
  }
  
  // Get unique table name
  Future<String> _getUniqueTableName(String baseName) async {
    final existingTables = await _databaseService.getTableNames();
    String uniqueName = _cleanColumnName(baseName);
    int counter = 1;
    
    while (existingTables.contains(uniqueName)) {
      uniqueName = '${_cleanColumnName(baseName)}_$counter';
      counter++;
    }
    
    return uniqueName;
  }
  
  // Infer column types from Excel data
  void _inferColumnTypes(List<List<Data?>> rows, List<String> headers, Map<String, String> columnTypes) {
    for (int colIndex = 0; colIndex < headers.length; colIndex++) {
      final header = headers[colIndex];
      bool isInteger = true;
      bool isDouble = true;
      bool isDate = true;
      
      for (final row in rows) {
        if (colIndex < row.length && row[colIndex]?.value != null) {
          final value = row[colIndex]!.value;
          
          if (value is! int) isInteger = false;
          if (value is! double && value is! int) isDouble = false;
          if (value is! DateTime) isDate = false;
          
          if (!isInteger && !isDouble && !isDate) break;
        }
      }
      
      if (isInteger) {
        columnTypes[header] = 'INTEGER';
      } else if (isDouble) {
        columnTypes[header] = 'DOUBLE';
      } else if (isDate) {
        columnTypes[header] = 'DATE';
      } else {
        columnTypes[header] = 'TEXT';
      }
    }
  }
  
  // Infer column types from string data (CSV)
  void _inferColumnTypesFromStrings(List<List<String>> rows, List<String> headers, Map<String, String> columnTypes) {
    for (int colIndex = 0; colIndex < headers.length; colIndex++) {
      final header = headers[colIndex];
      bool isInteger = true;
      bool isDouble = true;
      
      for (final row in rows) {
        if (colIndex < row.length && row[colIndex].isNotEmpty) {
          final value = row[colIndex];
          
          if (int.tryParse(value) == null) isInteger = false;
          if (double.tryParse(value) == null) isDouble = false;
          
          if (!isInteger && !isDouble) break;
        }
      }
      
      if (isInteger) {
        columnTypes[header] = 'INTEGER';
      } else if (isDouble) {
        columnTypes[header] = 'DOUBLE';
      } else {
        columnTypes[header] = 'TEXT';
      }
    }
  }
  
  // Convert cell value based on type
  dynamic _convertCellValue(dynamic value, String type) {
    if (value == null) return null;
    
    switch (type) {
      case 'INTEGER':
        if (value is int) return value;
        if (value is double) return value.toInt();
        return int.tryParse(value.toString()) ?? 0;
      case 'DOUBLE':
        if (value is double) return value;
        if (value is int) return value.toDouble();
        return double.tryParse(value.toString()) ?? 0.0;
      case 'DATE':
        if (value is DateTime) return value.toIso8601String();
        return value.toString();
      default:
        return value.toString();
    }
  }
  
  // Convert string value based on type
  dynamic _convertStringValue(String value, String type) {
    if (value.isEmpty) return null;
    
    switch (type) {
      case 'INTEGER':
        return int.tryParse(value) ?? 0;
      case 'DOUBLE':
        return double.tryParse(value) ?? 0.0;
      default:
        return value;
    }
  }
}
