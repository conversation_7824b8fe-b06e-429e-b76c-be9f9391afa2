import 'package:flutter/material.dart';
import '../models/project.dart';
import 'pricing_item_card.dart';

class MaterialPricingCard extends StatelessWidget {
  final MaterialItem material;
  final double exchangeRate;
  final Function(double) onQuantityChanged;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const MaterialPricingCard({
    super.key,
    required this.material,
    required this.exchangeRate,
    required this.onQuantityChanged,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return PricingItemCard(
      model: material.name,
      description: material.description,
      quantity: material.quantity,
      manufacturer: material.vendor,
      approval: material.approval,
      unitCostUSD: material.exWorksUnitCost,
      localCostSAR: material.localUnitCost,
      installationCostSAR: material.installationUnitCost,
      exchangeRate: exchangeRate,
      unit: material.unit,
      onQuantityChanged: onQuantityChanged,
      onEdit: onEdit,
      onDelete: onDelete,
    );
  }
}

class EquipmentPricingCard extends StatelessWidget {
  final EquipmentItem equipment;
  final double exchangeRate;
  final Function(double) onQuantityChanged;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const EquipmentPricingCard({
    super.key,
    required this.equipment,
    required this.exchangeRate,
    required this.onQuantityChanged,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return PricingItemCard(
      model: equipment.name,
      description: equipment.category,
      quantity: equipment.quantity,
      manufacturer: equipment.vendor,
      approval: equipment.approval,
      unitCostUSD: equipment.exWorksUnitCost,
      localCostSAR: equipment.localUnitCost,
      installationCostSAR: equipment.installationUnitCost,
      exchangeRate: exchangeRate,
      unit: 'pcs',
      onQuantityChanged: onQuantityChanged,
      onEdit: onEdit,
      onDelete: onDelete,
    );
  }
}
