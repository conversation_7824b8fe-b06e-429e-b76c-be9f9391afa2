import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart' hide Border, BorderStyle;
import 'dart:io';
import 'dart:async';
import 'dart:math' as math;

class FinalExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const FinalExcelGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  });

  @override
  State<FinalExcelGrid> createState() => _FinalExcelGridState();
}

class _FinalExcelGridState extends State<FinalExcelGrid> {
  // Controllers
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _gridFocusNode = FocusNode();

  // Cell editing controllers
  final Map<String, TextEditingController> _cellControllers = {};

  // Data state
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _filteredItems = [];
  List<String> _columns = [];
  final Map<String, double> _columnWidths = {};
  List<double> _rowHeights = [];
  bool _isLoading = true;
  String? _error;
  bool _isCardView = false;

  // Selection state
  int? _focusedRow;
  int? _focusedCol;
  Set<String> _selectedCells = {}; // Format: "row:col"
  bool _isSelecting = false;
  int? _selectionStartRow;
  int? _selectionStartCol;

  // Column sorting
  String? _sortColumn;
  bool _sortAscending = true;

  // Cell size constants
  final double _defaultColumnWidth = 150.0;
  final double _defaultRowHeight = 40.0;
  final double _headerHeight = 50.0;
  final double _rowHeaderWidth = 60.0;
  final double _resizeHandleWidth = 5.0;
  final double _addButtonSize = 30.0;

  @override
  void initState() {
    super.initState();

    // Initialize columns with predefined columns if provided
    if (widget.predefinedColumns != null) {
      _columns = List.from(widget.predefinedColumns!);
    } else {
      _columns = [
        'model',
        'description',
        'manufacturer',
        'approval',
        'ex_works_price',
        'local_price',
        'installation_price',
      ];
    }

    // Initialize column widths
    for (var column in _columns) {
      _columnWidths[column] = _defaultColumnWidth;
    }

    _loadData();

    // Add keyboard listener
    _gridFocusNode.addListener(() {
      if (_gridFocusNode.hasFocus) {
        // This ensures the grid can receive keyboard events
      }
    });
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _searchController.dispose();
    _gridFocusNode.dispose();

    // Dispose all cell controllers
    for (var controller in _cellControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  // Data loading and filtering
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final loadedItems = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Discover all columns from data
      final Set<String> columnSet = {'id'};

      // Add predefined columns
      columnSet.addAll(_columns);

      // Add any additional columns from the data
      for (var item in loadedItems) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Initialize row heights
      _rowHeights = List.generate(loadedItems.length, (_) => _defaultRowHeight);

      setState(() {
        _items = loadedItems;
        _filteredItems = List.from(loadedItems);
        _columns = columnSet.toList();

        // Initialize any new column widths
        for (var column in _columns) {
          if (!_columnWidths.containsKey(column)) {
            _columnWidths[column] = _defaultColumnWidth;
          }
        }

        // Initialize cell controllers
        _initCellControllers();

        _isLoading = false;
      });

      // Apply sorting if active
      if (_sortColumn != null) {
        _sortData();
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _initCellControllers() {
    // Clear existing controllers
    for (var controller in _cellControllers.values) {
      controller.dispose();
    }
    _cellControllers.clear();

    // Create controllers for each cell
    for (var rowIndex = 0; rowIndex < _filteredItems.length; rowIndex++) {
      for (var colIndex = 0; colIndex < _columns.length; colIndex++) {
        final cellKey = '$rowIndex:$colIndex';
        final column = _columns[colIndex];
        final value = _filteredItems[rowIndex][column]?.toString() ?? '';

        _cellControllers[cellKey] = TextEditingController(text: value);
      }
    }
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(_items);
        _initCellControllers();
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    setState(() {
      _filteredItems = _items.where((item) {
        // Search in all columns
        for (var column in _columns) {
          final value = item[column]?.toString().toLowerCase() ?? '';
          if (value.contains(lowercaseQuery)) {
            return true;
          }
        }
        return false;
      }).toList();

      _initCellControllers();
    });
  }

  void _sortData() {
    if (_sortColumn == null) return;

    setState(() {
      _filteredItems.sort((a, b) {
        final aValue = a[_sortColumn]?.toString() ?? '';
        final bValue = b[_sortColumn]?.toString() ?? '';

        // Try to parse as numbers if possible
        final aNum = double.tryParse(aValue);
        final bNum = double.tryParse(bValue);

        if (aNum != null && bNum != null) {
          return _sortAscending ? aNum.compareTo(bNum) : bNum.compareTo(aNum);
        }

        return _sortAscending ? aValue.compareTo(bValue) : bValue.compareTo(aValue);
      });

      _initCellControllers();
    });
  }

  // Cell selection and focus
  void _focusCell(int row, int col) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _focusedRow = row;
      _focusedCol = col;

      // Clear selection if not in selection mode
      if (!_isSelecting) {
        _selectedCells.clear();
        _selectedCells.add('$row:$col');
      }
    });

    // Request focus to the grid
    _gridFocusNode.requestFocus();
  }

  void _startSelecting(int row, int col) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _focusedRow = row;
      _focusedCol = col;
      _selectionStartRow = row;
      _selectionStartCol = col;
      _isSelecting = true;

      // Clear previous selection
      _selectedCells.clear();
      _selectedCells.add('$row:$col');
    });
  }

  void _updateSelection(int row, int col) {
    if (!_isSelecting || _selectionStartRow == null || _selectionStartCol == null) {
      return;
    }

    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      // Clear previous selection
      _selectedCells.clear();

      // Calculate selection rectangle
      final startRow = math.min(_selectionStartRow!, row);
      final endRow = math.max(_selectionStartRow!, row);
      final startCol = math.min(_selectionStartCol!, col);
      final endCol = math.max(_selectionStartCol!, col);

      // Add all cells in the rectangle to selection
      for (var r = startRow; r <= endRow; r++) {
        for (var c = startCol; c <= endCol; c++) {
          _selectedCells.add('$r:$c');
        }
      }
    });
  }

  void _endSelecting() {
    setState(() {
      _isSelecting = false;
    });
  }

  // Cell editing
  void _updateCellValue(int row, int col, String newValue) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    final column = _columns[col];
    final itemId = _filteredItems[row]['id'] as String;
    final oldValue = _filteredItems[row][column]?.toString() ?? '';

    // Skip update if value hasn't changed
    if (newValue == oldValue) {
      return;
    }

    // Update in Firestore
    FirebaseFirestore.instance
        .collection(widget.collectionPath)
        .doc(itemId)
        .update({column: newValue})
        .then((_) {
          // Update local data
          setState(() {
            _filteredItems[row][column] = newValue;

            // Also update in _items
            final index = _items.indexWhere((item) => item['id'] == itemId);
            if (index >= 0) {
              _items[index][column] = newValue;
            }
          });

          // Re-sort if needed
          if (_sortColumn != null) {
            _sortData();
          }
        })
        .catchError((error) {
          _showSnackBar('Error updating cell: $error', isError: true);
        });
  }

  void _clearSelectedCells() {
    // Prepare batch update
    final batch = FirebaseFirestore.instance.batch();
    final updates = <int, Map<String, dynamic>>{};

    // Process each selected cell
    for (var cellKey in _selectedCells) {
      final parts = cellKey.split(':');
      final row = int.parse(parts[0]);
      final col = int.parse(parts[1]);

      if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
        continue;
      }

      final column = _columns[col];
      if (column == 'id') continue;

      final itemId = _filteredItems[row]['id'] as String;

      // Add to updates
      if (!updates.containsKey(row)) {
        updates[row] = {'id': itemId};
      }
      updates[row]![column] = '';

      // Update controller
      final cellControllerKey = '$row:$col';
      if (_cellControllers.containsKey(cellControllerKey)) {
        _cellControllers[cellControllerKey]!.text = '';
      }
    }

    // Apply updates to Firestore
    for (var entry in updates.entries) {
      final row = entry.key;
      final updates = entry.value;
      final itemId = updates['id'] as String;
      updates.remove('id');

      if (updates.isNotEmpty) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(itemId);

        batch.update(docRef, updates);

        // Update local data
        for (var key in updates.keys) {
          _filteredItems[row][key] = '';

          // Also update in _items
          final index = _items.indexWhere((item) => item['id'] == itemId);
          if (index >= 0) {
            _items[index][key] = '';
          }
        }
      }
    }

    // Commit batch
    batch.commit().then((_) {
      _showSnackBar('Cells cleared');

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }
    }).catchError((error) {
      _showSnackBar('Error clearing cells: $error', isError: true);
    });
  }

  // Keyboard handling
  void _handleKeyEvent(RawKeyEvent event) {
    if (event is! RawKeyDownEvent) return;

    // Handle navigation keys
    if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
      _moveSelection(-1, 0);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
      _moveSelection(1, 0);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
      _moveSelection(0, -1);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
      _moveSelection(0, 1);
    } else if (event.logicalKey == LogicalKeyboardKey.tab) {
      _moveSelection(0, event.isShiftPressed ? -1 : 1);
    } else if (event.logicalKey == LogicalKeyboardKey.enter) {
      _moveSelection(1, 0);
    } else if (event.logicalKey == LogicalKeyboardKey.keyC &&
              event.isControlPressed) {
      _copySelection();
    } else if (event.logicalKey == LogicalKeyboardKey.keyV &&
              event.isControlPressed) {
      _pasteFromClipboard();
    } else if (event.logicalKey == LogicalKeyboardKey.delete ||
              event.logicalKey == LogicalKeyboardKey.backspace) {
      _clearSelectedCells();
    }
  }

  void _moveSelection(int rowDelta, int colDelta) {
    if (_focusedRow == null || _focusedCol == null) {
      _focusCell(0, 0);
      return;
    }

    final newRow = math.max(0, math.min(_filteredItems.length - 1, _focusedRow! + rowDelta));
    final newCol = math.max(0, math.min(_columns.length - 1, _focusedCol! + colDelta));

    _focusCell(newRow, newCol);
  }

  // Clipboard operations
  Future<void> _copySelection() async {
    if (_selectedCells.isEmpty) return;

    // Find the bounds of the selection
    int minRow = _filteredItems.length;
    int maxRow = 0;
    int minCol = _columns.length;
    int maxCol = 0;

    for (var cellKey in _selectedCells) {
      final parts = cellKey.split(':');
      final row = int.parse(parts[0]);
      final col = int.parse(parts[1]);

      minRow = math.min(minRow, row);
      maxRow = math.max(maxRow, row);
      minCol = math.min(minCol, col);
      maxCol = math.max(maxCol, col);
    }

    // Build a 2D array of the selected cells
    final List<List<String>> data = [];

    for (var r = minRow; r <= maxRow; r++) {
      final rowData = <String>[];

      for (var c = minCol; c <= maxCol; c++) {
        if (_selectedCells.contains('$r:$c')) {
          final column = _columns[c];
          rowData.add(_filteredItems[r][column]?.toString() ?? '');
        } else {
          rowData.add('');
        }
      }

      data.add(rowData);
    }

    // Convert to tab-separated values
    final clipboard = data.map((row) => row.join('\t')).join('\n');

    // Copy to clipboard
    await Clipboard.setData(ClipboardData(text: clipboard));

    _showSnackBar('Copied to clipboard');
  }

  Future<void> _pasteFromClipboard() async {
    if (_focusedRow == null || _focusedCol == null) {
      _showSnackBar('Please select a cell first', isError: true);
      return;
    }

    final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data == null || data.text == null) {
      _showSnackBar('No data in clipboard', isError: true);
      return;
    }

    // Parse clipboard data
    final rows = data.text!.split('\n');
    if (rows.isEmpty) {
      _showSnackBar('No rows found in clipboard data', isError: true);
      return;
    }

    // Start from the focused cell
    final startRow = _focusedRow!;
    final startCol = _focusedCol!;

    // Prepare batch update
    final batch = FirebaseFirestore.instance.batch();
    final updates = <int, Map<String, dynamic>>{};

    // Process each row from clipboard
    for (var i = 0; i < rows.length; i++) {
      final rowIndex = startRow + i;
      if (rowIndex >= _filteredItems.length) continue;

      final itemId = _filteredItems[rowIndex]['id'] as String;
      final values = rows[i].split('\t');

      // Add to updates
      if (!updates.containsKey(rowIndex)) {
        updates[rowIndex] = {'id': itemId};
      }

      for (var j = 0; j < values.length; j++) {
        final colIndex = startCol + j;
        if (colIndex >= _columns.length) continue;

        final column = _columns[colIndex];
        if (column == 'id') continue;

        updates[rowIndex]![column] = values[j];

        // Update controller
        final cellKey = '$rowIndex:$colIndex';
        if (_cellControllers.containsKey(cellKey)) {
          _cellControllers[cellKey]!.text = values[j];
        }
      }
    }

    // Apply updates to Firestore
    for (var entry in updates.entries) {
      final row = entry.key;
      final rowUpdates = entry.value;
      final itemId = rowUpdates['id'] as String;
      rowUpdates.remove('id');

      if (rowUpdates.isNotEmpty) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(itemId);

        batch.update(docRef, rowUpdates);

        // Update local data
        for (var key in rowUpdates.keys) {
          _filteredItems[row][key] = rowUpdates[key];

          // Also update in _items
          final index = _items.indexWhere((item) => item['id'] == itemId);
          if (index >= 0) {
            _items[index][key] = rowUpdates[key];
          }
        }
      }
    }

    // Commit batch
    batch.commit().then((_) {
      _showSnackBar('Pasted from clipboard');

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }
    }).catchError((error) {
      _showSnackBar('Error pasting data: $error', isError: true);
    });
  }

  // Import/Export
  Future<void> _exportToExcel() async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      // Add headers
      for (var i = 0; i < _columns.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value =
            TextCellValue(_columns[i]);
      }

      // Add data
      for (var i = 0; i < _filteredItems.length; i++) {
        final item = _filteredItems[i];

        for (var j = 0; j < _columns.length; j++) {
          final column = _columns[j];
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1)).value =
              TextCellValue(item[column]?.toString() ?? '');
        }
      }

      // Save file
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Excel File',
        fileName: '${widget.title.replaceAll(' ', '_')}_export.xlsx',
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
      );

      if (result != null) {
        final file = File(result);
        await file.writeAsBytes(excel.encode()!);
        _showSnackBar('Data exported to Excel');
      }
    } catch (e) {
      _showSnackBar('Error exporting to Excel: $e', isError: true);
    }
  }

  Future<void> _importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result == null || result.files.isEmpty) return;

      final file = File(result.files.first.path!);
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      if (excel.tables.isEmpty) {
        _showSnackBar('No data found in Excel file', isError: true);
        return;
      }

      // Use the first sheet
      final sheet = excel.tables.entries.first.value;

      // Get headers from first row
      final headers = <String>[];
      final headerRow = sheet.rows.first;

      for (var cell in headerRow) {
        if (cell?.value != null) {
          headers.add(cell!.value.toString());
        }
      }

      if (headers.isEmpty) {
        _showSnackBar('No headers found in Excel file', isError: true);
        return;
      }

      // Clear existing data if needed
      final dialogContext = context;
      final clearExisting = await showDialog<bool>(
        context: dialogContext,
        builder: (context) => AlertDialog(
          title: const Text('Import Options'),
          content: const Text('Do you want to clear existing data before importing?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('No, Add to Existing'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Yes, Clear Existing'),
            ),
          ],
        ),
      ) ?? false;

      if (!mounted) return;

      if (clearExisting) {
        // Delete all existing documents
        final batch = FirebaseFirestore.instance.batch();
        for (var item in _items) {
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(item['id'] as String);
          batch.delete(docRef);
        }
        await batch.commit();

        // Clear local data
        setState(() {
          _items = [];
          _filteredItems = [];
          _rowHeights = [];
        });
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      final newItems = <Map<String, dynamic>>[];
      int count = 0;

      for (var i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final Map<String, dynamic> item = {};

        for (var j = 0; j < row.length && j < headers.length; j++) {
          final cell = row[j];
          if (cell?.value != null) {
            item[headers[j]] = cell!.value.toString();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc();
          batch.set(docRef, item);

          newItems.add({
            'id': docRef.id,
            ...item,
          });

          count++;
        }
      }

      await batch.commit();

      if (!mounted) return;

      // Update columns to match imported data
      final Set<String> columnSet = {'id'};

      // Add all headers from the Excel file
      columnSet.addAll(headers);

      // Add any additional columns from existing data
      for (var item in _items) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Update local data
      setState(() {
        _columns = columnSet.toList();

        // Initialize any new column widths
        for (var column in _columns) {
          if (!_columnWidths.containsKey(column)) {
            _columnWidths[column] = _defaultColumnWidth;
          }
        }

        _items.addAll(newItems);
        _filteredItems.addAll(newItems);

        // Update row heights
        _rowHeights = List.generate(_filteredItems.length, (_) => _defaultRowHeight);

        // Reinitialize cell controllers
        _initCellControllers();
      });

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }

      _showSnackBar('Imported $count items from Excel');
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
    }
  }

  // Row operations
  Future<void> _addNewRow() async {
    final Map<String, dynamic> newItem = {};

    // Add default empty values for all columns
    for (var column in _columns) {
      if (column != 'id') {
        newItem[column] = '';
      }
    }

    newItem['createdAt'] = FieldValue.serverTimestamp();

    try {
      final docRef = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Add to local data with the new ID
      final newItemWithId = {
        'id': docRef.id,
        ...newItem,
      };

      setState(() {
        _items.add(newItemWithId);
        _filteredItems.add(newItemWithId);
        _rowHeights.add(_defaultRowHeight);

        // Add controllers for the new row
        final rowIndex = _filteredItems.length - 1;
        for (var colIndex = 0; colIndex < _columns.length; colIndex++) {
          final cellKey = '$rowIndex:$colIndex';
          final column = _columns[colIndex];
          _cellControllers[cellKey] = TextEditingController(text: newItemWithId[column]?.toString() ?? '');
        }
      });

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }

      _showSnackBar('New row added');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  Future<void> _deleteRow(int rowIndex) async {
    if (rowIndex < 0 || rowIndex >= _filteredItems.length) {
      return;
    }

    final itemId = _filteredItems[rowIndex]['id'] as String;

    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(itemId)
          .delete();

      setState(() {
        _filteredItems.removeAt(rowIndex);
        _items.removeWhere((item) => item['id'] == itemId);
        _rowHeights.removeAt(rowIndex);

        // Remove controllers for the deleted row and update keys for subsequent rows
        final newControllers = <String, TextEditingController>{};

        for (var key in _cellControllers.keys) {
          final parts = key.split(':');
          final row = int.parse(parts[0]);
          final col = int.parse(parts[1]);

          if (row < rowIndex) {
            // Keep controllers for rows before the deleted row
            newControllers[key] = _cellControllers[key]!;
          } else if (row > rowIndex) {
            // Update keys for rows after the deleted row
            newControllers['${row - 1}:$col'] = _cellControllers[key]!;
          } else {
            // Dispose controllers for the deleted row
            _cellControllers[key]!.dispose();
          }
        }

        _cellControllers.clear();
        _cellControllers.addAll(newControllers);

        // Reset selection if the selected row was deleted
        if (_focusedRow == rowIndex) {
          _focusedRow = null;
          _focusedCol = null;
          _selectedCells.clear();
        } else if (_focusedRow != null && _focusedRow! > rowIndex) {
          // Adjust selection if a row above was deleted
          _focusedRow = _focusedRow! - 1;

          // Update selected cells
          final newSelectedCells = <String>{};
          for (var cellKey in _selectedCells) {
            final parts = cellKey.split(':');
            final row = int.parse(parts[0]);
            final col = parts[1];

            if (row < rowIndex) {
              newSelectedCells.add(cellKey);
            } else if (row > rowIndex) {
              newSelectedCells.add('${row - 1}:$col');
            }
          }
          _selectedCells = newSelectedCells;
        }
      });

      _showSnackBar('Row deleted');
    } catch (e) {
      _showSnackBar('Error deleting row: $e', isError: true);
    }
  }

  // Column operations
  Future<void> _addColumn(String columnName) async {
    if (columnName.isEmpty) {
      _showSnackBar('Column name cannot be empty', isError: true);
      return;
    }

    if (_columns.contains(columnName)) {
      _showSnackBar('Column already exists', isError: true);
      return;
    }

    setState(() {
      _columns.add(columnName);
      _columnWidths[columnName] = _defaultColumnWidth;

      // Add controllers for the new column
      for (var rowIndex = 0; rowIndex < _filteredItems.length; rowIndex++) {
        final colIndex = _columns.length - 1;
        final cellKey = '$rowIndex:$colIndex';
        _cellControllers[cellKey] = TextEditingController(text: '');
      }
    });

    _showSnackBar('Column "$columnName" added');
  }

  Future<void> _deleteColumn(String column) async {
    // Don't allow deleting the ID column
    if (column == 'id') {
      _showSnackBar('Cannot delete ID column', isError: true);
      return;
    }

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Column'),
        content: Text('Are you sure you want to delete the "$column" column? This will remove this data from all items.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    try {
      // We need to update all documents to remove this field
      final batch = FirebaseFirestore.instance.batch();

      for (var item in _items) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(item['id'] as String);

        // Firebase doesn't have a direct "remove field" operation,
        // so we set it to FieldValue.delete()
        batch.update(docRef, {column: FieldValue.delete()});
      }

      await batch.commit();

      if (!mounted) return;

      // Update local data
      setState(() {
        final colIndex = _columns.indexOf(column);
        _columns.remove(column);
        _columnWidths.remove(column);

        // Remove controllers for the deleted column and update keys for subsequent columns
        final newControllers = <String, TextEditingController>{};

        for (var key in _cellControllers.keys) {
          final parts = key.split(':');
          final row = parts[0];
          final col = int.parse(parts[1]);

          if (col < colIndex) {
            // Keep controllers for columns before the deleted column
            newControllers[key] = _cellControllers[key]!;
          } else if (col > colIndex) {
            // Update keys for columns after the deleted column
            newControllers['$row:${col - 1}'] = _cellControllers[key]!;
          } else {
            // Dispose controllers for the deleted column
            _cellControllers[key]!.dispose();
          }
        }

        _cellControllers.clear();
        _cellControllers.addAll(newControllers);

        // Remove column data from items
        for (var item in _items) {
          item.remove(column);
        }

        for (var item in _filteredItems) {
          item.remove(column);
        }

        // Reset selection if the selected column was deleted
        if (_focusedCol == colIndex) {
          _focusedRow = null;
          _focusedCol = null;
          _selectedCells.clear();
        } else if (_focusedCol != null && _focusedCol! > colIndex) {
          // Adjust selection if a column to the left was deleted
          _focusedCol = _focusedCol! - 1;

          // Update selected cells
          final newSelectedCells = <String>{};
          for (var cellKey in _selectedCells) {
            final parts = cellKey.split(':');
            final row = parts[0];
            final col = int.parse(parts[1]);

            if (col < colIndex) {
              newSelectedCells.add(cellKey);
            } else if (col > colIndex) {
              newSelectedCells.add('$row:${col - 1}');
            }
          }
          _selectedCells = newSelectedCells;
        }
      });

      _showSnackBar('Column "$column" deleted');
    } catch (e) {
      _showSnackBar('Error deleting column: $e', isError: true);
    }
  }

  // Resize columns and rows
  void _resizeColumn(String column, double newWidth) {
    setState(() {
      _columnWidths[column] = math.max(50.0, newWidth);
    });
  }

  void _resizeRow(int rowIndex, double newHeight) {
    if (rowIndex < 0 || rowIndex >= _rowHeights.length) return;

    setState(() {
      _rowHeights[rowIndex] = math.max(30.0, newHeight);
    });
  }

  void _showAddColumnDialog() {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Column'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Column Name',
            hintText: 'Enter column name',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final columnName = controller.text.trim();
              if (columnName.isNotEmpty) {
                _addColumn(columnName);
                Navigator.of(context).pop();
              }
            },
            style: TextButton.styleFrom(foregroundColor: widget.themeColor),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  String _getDisplayName(String column) {
    // Convert snake_case to Title Case
    return column.split('_').map((word) =>
      word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  // Helper methods for grid positioning
  double _getColumnPosition(int colIndex) {
    double position = _rowHeaderWidth;
    for (int i = 0; i < colIndex; i++) {
      position += _columnWidths[_columns[i]] ?? _defaultColumnWidth;
    }
    return position;
  }

  double _getRowPosition(int rowIndex) {
    double position = 0;
    for (int i = 0; i < rowIndex; i++) {
      position += _rowHeights[i];
    }
    return position;
  }

  int? _getCellRowFromPosition(double y) {
    if (y < _headerHeight) return null;

    double position = _headerHeight;
    for (int i = 0; i < _rowHeights.length; i++) {
      position += _rowHeights[i];
      if (y < position) return i;
    }

    return null;
  }

  int? _getCellColFromPosition(double x) {
    if (x < _rowHeaderWidth) return null;

    double position = _rowHeaderWidth;
    for (int i = 0; i < _columns.length; i++) {
      position += _columnWidths[_columns[i]] ?? _defaultColumnWidth;
      if (x < position) return i;
    }

    return null;
  }

  // UI Building
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildExcelGrid() {
    return Focus(
      focusNode: _gridFocusNode,
      child: Stack(
        children: [
          // Excel-like grid
          Scrollbar(
            controller: _verticalController,
            thumbVisibility: true,
            thickness: 16.0, // Thicker scrollbar
            radius: const Radius.circular(8.0),
            child: Scrollbar(
              controller: _horizontalController,
              thumbVisibility: true,
              thickness: 16.0, // Thicker scrollbar
              radius: const Radius.circular(8.0),
              notificationPredicate: (notification) => notification.depth == 1,
              child: SingleChildScrollView(
                controller: _verticalController,
                child: SingleChildScrollView(
                  controller: _horizontalController,
                  scrollDirection: Axis.horizontal,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Column headers
                      Row(
                        children: [
                          // Row header (empty corner)
                          Container(
                            width: _rowHeaderWidth,
                            height: _headerHeight,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              border: Border.all(color: Colors.grey.shade400),
                            ),
                            child: Center(
                              child: IconButton(
                                icon: const Icon(Icons.add, color: Colors.green),
                                tooltip: 'Add Column',
                                onPressed: _showAddColumnDialog,
                              ),
                            ),
                          ),

                          // Column headers
                          ..._columns.asMap().entries.map((entry) {
                            final colIndex = entry.key;
                            final column = entry.value;

                            return _buildColumnHeader(column, colIndex);
                          }),

                          // Add column button
                          Container(
                            width: _addButtonSize,
                            height: _headerHeight,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              border: Border.all(color: Colors.grey.shade400),
                            ),
                            child: Center(
                              child: IconButton(
                                icon: const Icon(Icons.add, color: Colors.green),
                                tooltip: 'Add Column',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                onPressed: _showAddColumnDialog,
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Data rows
                      ..._filteredItems.asMap().entries.map((entry) {
                        final rowIndex = entry.key;
                        final item = entry.value;

                        return _buildDataRow(item, rowIndex);
                      }),

                      // Add row button
                      Row(
                        children: [
                          Container(
                            width: _rowHeaderWidth,
                            height: _addButtonSize,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              border: Border.all(color: Colors.grey.shade400),
                            ),
                            child: Center(
                              child: IconButton(
                                icon: const Icon(Icons.add, color: Colors.green),
                                tooltip: 'Add Row',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                onPressed: _addNewRow,
                              ),
                            ),
                          ),

                          Container(
                            width: _columns.fold<double>(0.0, (sum, column) => sum + (_columnWidths[column] ?? _defaultColumnWidth)),
                            height: _addButtonSize,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              border: Border.all(color: Colors.grey.shade400),
                            ),
                            child: Center(
                              child: TextButton.icon(
                                icon: const Icon(Icons.add),
                                label: const Text('Add New Row'),
                                onPressed: _addNewRow,
                              ),
                            ),
                          ),

                          Container(
                            width: _addButtonSize,
                            height: _addButtonSize,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              border: Border.all(color: Colors.grey.shade400),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeader(String column, int colIndex) {
    final isSorted = _sortColumn == column;

    return Stack(
      children: [
        // Column header
        GestureDetector(
          onTap: () {
            setState(() {
              if (_sortColumn == column) {
                _sortAscending = !_sortAscending;
              } else {
                _sortColumn = column;
                _sortAscending = true;
              }
              _sortData();
            });
          },
          child: Container(
            width: _columnWidths[column] ?? _defaultColumnWidth,
            height: _headerHeight,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  widget.themeColor.withAlpha(179), // 0.7 * 255 = 179
                  widget.themeColor.withAlpha(128), // 0.5 * 255 = 128
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              border: Border.all(color: Colors.grey.shade400),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8),
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _getDisplayName(column),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (isSorted)
                  Icon(
                    _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                    color: Colors.white,
                    size: 16,
                  ),
                if (column != 'id')
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white, size: 16),
                    onPressed: () => _deleteColumn(column),
                    tooltip: 'Delete Column',
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
              ],
            ),
          ),
        ),

        // Resize handle
        Positioned(
          right: 0,
          top: 0,
          bottom: 0,
          width: _resizeHandleWidth,
          child: MouseRegion(
            cursor: SystemMouseCursors.resizeLeftRight,
            child: GestureDetector(
              onHorizontalDragUpdate: (details) {
                _resizeColumn(column, (_columnWidths[column] ?? _defaultColumnWidth) + details.delta.dx);
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDataRow(Map<String, dynamic> item, int rowIndex) {
    return Stack(
      children: [
        Row(
          children: [
            // Row header (row number and delete button)
            Container(
              width: _rowHeaderWidth,
              height: _rowHeights[rowIndex],
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                border: Border.all(color: Colors.grey.shade400),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 4),
                    child: Text(
                      '${rowIndex + 1}',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                    onPressed: () => _deleteRow(rowIndex),
                    tooltip: 'Delete Row',
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // Data cells
            ..._columns.asMap().entries.map((entry) {
              final colIndex = entry.key;
              final column = entry.value;

              return _buildDataCell(item, column, rowIndex, colIndex);
            }),

            // End of row
            Container(
              width: _addButtonSize,
              height: _rowHeights[rowIndex],
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                border: Border.all(color: Colors.grey.shade400),
              ),
            ),
          ],
        ),

        // Resize handle
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          height: _resizeHandleWidth,
          child: MouseRegion(
            cursor: SystemMouseCursors.resizeUpDown,
            child: GestureDetector(
              onVerticalDragUpdate: (details) {
                _resizeRow(rowIndex, _rowHeights[rowIndex] + details.delta.dy);
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDataCell(Map<String, dynamic> item, String column, int rowIndex, int colIndex) {
    final isSelected = _selectedCells.contains('$rowIndex:$colIndex');
    final isFocused = _focusedRow == rowIndex && _focusedCol == colIndex;
    final cellKey = '$rowIndex:$colIndex';

    // Ensure we have a controller for this cell
    if (!_cellControllers.containsKey(cellKey)) {
      _cellControllers[cellKey] = TextEditingController(text: item[column]?.toString() ?? '');
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () => _focusCell(rowIndex, colIndex),
        onPanStart: (details) {
          _startSelecting(rowIndex, colIndex);
        },
        onPanUpdate: (details) {
          final RenderBox box = context.findRenderObject() as RenderBox;
          final position = box.globalToLocal(details.globalPosition);

          // Calculate which cell we're over
          final cellRowIndex = _getCellRowFromPosition(position.dy);
          final cellColIndex = _getCellColFromPosition(position.dx);

          if (cellRowIndex != null && cellColIndex != null) {
            _updateSelection(cellRowIndex, cellColIndex);
          }
        },
        onPanEnd: (_) {
          _endSelecting();
        },
        child: Container(
          width: _columnWidths[column] ?? _defaultColumnWidth,
          height: _rowHeights[rowIndex],
          decoration: BoxDecoration(
            color: isFocused
                ? widget.themeColor.withAlpha(51) // 0.2 * 255 = 51
                : isSelected
                    ? widget.themeColor.withAlpha(26) // 0.1 * 255 = 26
                    : Colors.white,
            border: Border.all(
              color: isFocused || isSelected
                  ? widget.themeColor
                  : Colors.grey.shade400,
              width: isFocused ? 2 : 1,
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          alignment: Alignment.centerLeft,
          child: TextField(
            controller: _cellControllers[cellKey],
            decoration: const InputDecoration(
              border: InputBorder.none,
              isDense: true,
              contentPadding: EdgeInsets.zero,
            ),
            style: const TextStyle(
              fontSize: 14,
            ),
            onChanged: (value) {
              _updateCellValue(rowIndex, colIndex, value);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCardView() {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: 400,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        return _buildCard(item, index);
      },
    );
  }

  Widget _buildCard(Map<String, dynamic> item, int index) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: widget.themeColor.withAlpha(77), // 0.3 * 255 = 77
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: widget.themeColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    item['model']?.toString() ?? 'No Model',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  onPressed: () => _deleteRow(index),
                  tooltip: 'Delete Item',
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(12),
              children: [
                // ID
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      const Text(
                        'ID: ',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          item['id']?.toString() ?? '',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),

                // Other fields
                ..._columns.where((col) => col != 'id').map((column) {
                  return _buildCardField(item, column, index);
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardField(Map<String, dynamic> item, String column, int rowIndex) {
    final cellKey = '$rowIndex:${_columns.indexOf(column)}';

    // Ensure we have a controller for this cell
    if (!_cellControllers.containsKey(cellKey)) {
      _cellControllers[cellKey] = TextEditingController(text: item[column]?.toString() ?? '');
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '${_getDisplayName(column)}:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: _cellControllers[cellKey],
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                isDense: true,
              ),
              style: TextStyle(
                color: item[column]?.toString().isEmpty ?? true ? Colors.grey : Colors.black,
                fontStyle: item[column]?.toString().isEmpty ?? true ? FontStyle.italic : FontStyle.normal,
              ),
              onChanged: (value) {
                _updateCellValue(rowIndex, _columns.indexOf(column), value);
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        actions: [
          // View toggle
          ToggleButtons(
            isSelected: [!_isCardView, _isCardView],
            onPressed: (index) {
              setState(() {
                _isCardView = index == 1;
              });
            },
            borderRadius: BorderRadius.circular(4),
            children: const [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Icon(Icons.grid_on),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Icon(Icons.view_module),
              ),
            ],
          ),
          const SizedBox(width: 16),

          // Import/Export buttons
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: 'Import Excel',
            onPressed: _importFromExcel,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: 'Export Excel',
            onPressed: _exportToExcel,
          ),

          // Add row/column buttons
          IconButton(
            icon: const Icon(Icons.add_box),
            tooltip: 'Add Row',
            onPressed: _addNewRow,
          ),
          IconButton(
            icon: const Icon(Icons.add_chart),
            tooltip: 'Add Column',
            onPressed: _showAddColumnDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterData('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: _filterData,
            ),
          ),

          // Data grid or card view
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator(color: widget.themeColor))
                : _error != null
                    ? _buildErrorWidget()
                    : _isCardView
                        ? _buildCardView()
                        : _buildExcelGrid(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewRow,
        backgroundColor: widget.themeColor,
        child: const Icon(Icons.add),
      ),
    );
  }
}
