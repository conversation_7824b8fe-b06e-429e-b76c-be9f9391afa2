^D:\FIRETOOL\FIRETOOL_ESTIMATOR\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\APP_LINKS\WINDOWS\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Firetool/firetool_estimator/windows -BD:/Firetool/firetool_estimator/build/windows/x64 --check-stamp-file D:/Firetool/firetool_estimator/build/windows/x64/plugins/app_links/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
