import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class SyncfusionExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const SyncfusionExcelGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  });

  @override
  State<SyncfusionExcelGrid> createState() => _SyncfusionExcelGridState();
}

class _SyncfusionExcelGridState extends State<SyncfusionExcelGrid> {
  late ExcelDataSource _excelDataSource;
  List<GridColumn> _columns = [];
  List<Map<String, dynamic>> _items = [];
  bool _isLoading = true;
  String? _error;
  final DataGridController _dataGridController = DataGridController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Generate mock data based on collection path
      final loadedItems = _getMockData(widget.collectionPath);

      // Determine columns
      final Set<String> columnSet = {'id'};

      // Add predefined columns if provided
      if (widget.predefinedColumns != null) {
        columnSet.addAll(widget.predefinedColumns!);
      }

      // Add any additional columns from the data
      for (var item in loadedItems) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Create GridColumns
      final gridColumns = columnSet.map((field) {
        return GridColumn(
          columnName: field,
          label: Container(
            padding: const EdgeInsets.all(8.0),
            alignment: Alignment.center,
            color: Color.fromRGBO(
              widget.themeColor.red,
              widget.themeColor.green,
              widget.themeColor.blue,
              0.1,
            ),
            child: Text(
              _getDisplayName(field),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.themeColor,
              ),
            ),
          ),
        );
      }).toList();

      setState(() {
        _columns = gridColumns;
        _items = loadedItems;
        _excelDataSource = ExcelDataSource(
          items: _items,
          columns: columnSet.toList(),
          onCellSubmitted: _updateCell,
          onRowAdded: _addNewRow,
          onRowDeleted: _deleteRow,
        );
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // Helper methods
  String _getDisplayName(String column) {
    // Convert snake_case to Title Case
    return column.split('_').map((word) =>
      word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Mock data generator
  List<Map<String, dynamic>> _getMockData(String collectionPath) {
    // Generate different mock data based on collection path
    final List<Map<String, dynamic>> mockData = [];

    // Generate 10 items with appropriate fields
    for (int i = 1; i <= 10; i++) {
      final Map<String, dynamic> item = {
        'id': 'mock-$i',
        'model': 'Model $i',
        'description': 'Description for item $i',
        'manufacturer': 'Manufacturer ${i % 3 + 1}',
        'approval': 'UL Listed',
      };

      // Add pricing fields
      item['ex_works_price'] = '${i * 100}';
      item['local_price'] = '${i * 150}';
      item['installation_price'] = '${i * 50}';

      // Add any predefined columns
      if (widget.predefinedColumns != null) {
        for (var column in widget.predefinedColumns!) {
          if (!item.containsKey(column)) {
            item[column] = 'Value for $column';
          }
        }
      }

      mockData.add(item);
    }

    return mockData;
  }

  // CRUD operations
  Future<void> _updateCell(String itemId, String column, dynamic value) async {
    try {
      // Don't update ID column
      if (column == 'id') return;

      // Update local data only (no Firestore)
      setState(() {
        final itemIndex = _items.indexWhere((item) => item['id'] == itemId);
        if (itemIndex != -1) {
          _items[itemIndex][column] = value;
        }
      });

      _showSnackBar('Cell updated successfully');
    } catch (e) {
      _showSnackBar('Error updating cell: $e', isError: true);
    }
  }

  Future<void> _addNewRow() async {
    try {
      // Create an empty row with default values
      final Map<String, dynamic> newItem = {};

      // Add default empty values for each column
      for (var column in _columns) {
        if (column.columnName != 'id') {
          newItem[column.columnName] = '';
        }
      }

      // Generate a unique ID
      final String newId = 'mock-${DateTime.now().millisecondsSinceEpoch}';

      // Add to local data
      setState(() {
        _items.add({
          'id': newId,
          ...newItem,
          'createdAt': DateTime.now().toString(),
        });
        _excelDataSource.items = _items;
        _excelDataSource.buildDataGridRows();
        _excelDataSource.notifyListeners();
      });

      _showSnackBar('Row added successfully');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  Future<void> _deleteRow(String itemId) async {
    try {
      // Delete from local data only (no Firestore)
      setState(() {
        _items.removeWhere((item) => item['id'] == itemId);
        _excelDataSource.items = _items;
        _excelDataSource.buildDataGridRows();
        _excelDataSource.notifyListeners();
      });

      _showSnackBar('Row deleted successfully');
    } catch (e) {
      _showSnackBar('Error deleting row: $e', isError: true);
    }
  }

  // Import/Export methods
  Future<void> _importFromExcel() async {
    try {
      // Mock implementation - just add some mock data
      final newItems = <Map<String, dynamic>>[];
      int count = 5; // Pretend we imported 5 items

      for (int i = 0; i < count; i++) {
        final String newId = 'import-${DateTime.now().millisecondsSinceEpoch}-$i';

        final Map<String, dynamic> item = {
          'id': newId,
          'model': 'Imported Model $i',
          'description': 'Imported from Excel',
          'manufacturer': 'Import Manufacturer',
          'approval': 'UL Listed',
          'ex_works_price': '${(i + 1) * 200}',
          'local_price': '${(i + 1) * 250}',
          'installation_price': '${(i + 1) * 75}',
          'createdAt': DateTime.now().toString(),
        };

        newItems.add(item);
      }

      // Add to local data and update columns if needed
      final Set<String> columnSet = {'id'};
      for (var item in [..._items, ...newItems]) {
        columnSet.addAll(item.keys);
      }
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Create GridColumns
      final gridColumns = columnSet.map((field) {
        return GridColumn(
          columnName: field,
          label: Container(
            padding: const EdgeInsets.all(8.0),
            alignment: Alignment.center,
            color: Color.fromRGBO(
              widget.themeColor.red,
              widget.themeColor.green,
              widget.themeColor.blue,
              0.1,
            ),
            child: Text(
              _getDisplayName(field),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.themeColor,
              ),
            ),
          ),
        );
      }).toList();

      setState(() {
        _columns = gridColumns;
        _items.addAll(newItems);
        _excelDataSource = ExcelDataSource(
          items: _items,
          columns: columnSet.toList(),
          onCellSubmitted: _updateCell,
          onRowAdded: _addNewRow,
          onRowDeleted: _deleteRow,
        );
      });

      _showSnackBar('Imported $count items from Excel');
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
    }
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final text = clipboardData?.text;

      if (text == null || text.isEmpty) {
        _showSnackBar('Clipboard is empty', isError: true);
        return;
      }

      // Parse clipboard data (tab-delimited format from Excel)
      final rows = text.split('\n');
      if (rows.isEmpty) {
        _showSnackBar('No data found in clipboard', isError: true);
        return;
      }

      // Get the selected cell indices
      if (!_dataGridController.selectedRows.any((row) => true)) {
        _showSnackBar('Please select a cell first', isError: true);
        return;
      }
      final selectedCell = _dataGridController.currentCell;

      final startRowIndex = selectedCell.rowIndex;
      final startColumnIndex = selectedCell.columnIndex;

      int cellsUpdated = 0;

      // Process each row from clipboard
      for (var i = 0; i < rows.length; i++) {
        final rowIndex = startRowIndex + i;
        if (rowIndex >= _items.length) {
          // Need to add new rows
          continue; // Skip for now, we'll add row creation later
        }

        final values = rows[i].split('\t');
        for (var j = 0; j < values.length; j++) {
          final columnIndex = startColumnIndex + j;
          if (columnIndex >= _columns.length) continue;

          final column = _columns[columnIndex].columnName;
          if (column == 'id') continue; // Don't update ID column

          // Update local data only
          _items[rowIndex][column] = values[j];
          cellsUpdated++;
        }
      }

      // Update the grid
      setState(() {
        _excelDataSource.items = _items;
        _excelDataSource.buildDataGridRows();
        _excelDataSource.notifyListeners();
      });

      _showSnackBar('Updated $cellsUpdated cells from clipboard');
    } catch (e) {
      _showSnackBar('Error pasting from clipboard: $e', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildToolbar(),
          Expanded(
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: widget.themeColor,
                    ),
                  )
                : _error != null
                    ? _buildErrorWidget()
                    : _buildDataGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.add),
                label: const Text('Add Row'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _addNewRow,
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.file_upload),
                label: const Text('Import Excel'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _importFromExcel,
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.paste),
                label: const Text('Paste'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _pasteFromClipboard,
              ),
              const Spacer(),
              Text('${_items.length} items'),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _loadData,
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Click to edit • Select cells and click Paste to paste from clipboard • Import Excel to load data from Excel file',
            style: TextStyle(fontStyle: FontStyle.italic, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildDataGrid() {
    return KeyboardListener(
      focusNode: _focusNode,
      onKeyEvent: (KeyEvent event) {
        if (event is KeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.keyV &&
              HardwareKeyboard.instance.isControlPressed) {
            _pasteFromClipboard();
          }
        }
      },
      child: SfDataGrid(
        source: _excelDataSource,
        columns: _columns,
        controller: _dataGridController,
        allowEditing: true,
        navigationMode: GridNavigationMode.cell,
        selectionMode: SelectionMode.multiple,
        gridLinesVisibility: GridLinesVisibility.both,
        headerGridLinesVisibility: GridLinesVisibility.both,
        allowSorting: true,
        allowFiltering: true,
        allowSwiping: true,
        swipeMaxOffset: 100.0,
        endSwipeActionsBuilder: (BuildContext context, DataGridRow row, int rowIndex) {
          return GestureDetector(
            onTap: () {
              final itemId = _items[rowIndex]['id'] as String;
              _deleteRow(itemId);
            },
            child: Container(
              color: Colors.red,
              padding: const EdgeInsets.all(16.0),
              alignment: Alignment.center,
              child: const Text(
                'DELETE',
                style: TextStyle(color: Colors.white),
              ),
            ),
          );
        },
      ),
    );
  }
}

class ExcelDataSource extends DataGridSource {
  List<Map<String, dynamic>> items;
  List<String> columns;
  List<DataGridRow> _dataGridRows = [];
  final Function(String, String, dynamic) onCellSubmitted;
  final Function() onRowAdded;
  final Function(String) onRowDeleted;

  ExcelDataSource({
    required this.items,
    required this.columns,
    required this.onCellSubmitted,
    required this.onRowAdded,
    required this.onRowDeleted,
  }) {
    buildDataGridRows();
  }

  void buildDataGridRows() {
    _dataGridRows = items.map<DataGridRow>((item) {
      return DataGridRow(
        cells: columns.map<DataGridCell>((column) {
          return DataGridCell<String>(
            columnName: column,
            value: item[column]?.toString() ?? '',
          );
        }).toList(),
      );
    }).toList();
  }

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((cell) {
        return Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(8.0),
          child: Text(cell.value.toString()),
        );
      }).toList(),
    );
  }

  @override
  Future<void> onCellSubmit(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) async {
    final rowIndex = rowColumnIndex.rowIndex;
    final itemId = items[rowIndex]['id'] as String;
    final columnName = column.columnName;
    final newValue = dataGridRow
        .getCells()
        .firstWhere((cell) => cell.columnName == columnName)
        .value;

    await onCellSubmitted(itemId, columnName, newValue);

    notifyListeners();
  }

  @override
  Future<bool> canSubmitCell(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) async {
    return column.columnName != 'id';
  }

  @override
  Widget? buildEditWidget(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column, CellSubmit submitCell) {
    return TextField(
      autofocus: true,
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.all(8),
      ),
      controller: TextEditingController(
        text: dataGridRow
            .getCells()
            .firstWhere((cell) => cell.columnName == column.columnName)
            .value
            .toString(),
      ),
      onSubmitted: (value) {
        submitCell();
      },
    );
  }
}
