import 'package:flutter/material.dart';

class AppConstants {
  // App information
  static const String appName = 'FireTool Estimator';
  static const String appVersion = '1.0.0';

  // System types
  static const List<String> systemTypes = [
    'Fire Alarm',
    'Water Sprinkler',
    'Foam System',
    'Novec 1230',
    'FM-200',
    'CO2 System',
    'Emergency Lighting',
    'Fire Extinguishers',
    'Fire Hydrant',
    'Fire Hose Reel',
    'Deluge System',
    'Pre-Action System',
    'Dry Chemical System',
  ];

  // Material categories
  static const Map<String, List<String>> materialCategories = {
    'Fire Alarm': [
      'Initiating Devices',
      'Notification Devices',
      'Control Equipment',
      'Wiring',
      'Accessories',
      'Panels',
      'Detectors',
      'Manual Call Points',
    ],
    'Water Sprinkler': [
      'Sprinkler Heads',
      'Piping',
      'Valves',
      'Fittings',
      'Hangers',
      'Flow Switches',
      'Pressure Switches',
      'Pumps',
      'Water Tanks',
    ],
    'Foam System': [
      'Foam Concentrate',
      'Proportioners',
      'Foam Chambers',
      'Bladder Tanks',
      'Piping',
      'Valves',
      'Nozzles',
      'Pumps',
    ],
    'Novec 1230': [
      'Novec 1230 Agent',
      'Cylinders',
      'Nozzles',
      'Control Panels',
      'Detection Devices',
      'Piping',
      'Valves',
      'Accessories',
    ],
    'FM-200': [
      'FM-200 Agent',
      'Cylinders',
      'Nozzles',
      'Control Panels',
      'Detection Devices',
      'Piping',
      'Valves',
      'Accessories',
    ],
    'CO2 System': [
      'CO2 Cylinders',
      'Nozzles',
      'Control Panels',
      'Detection Devices',
      'Piping',
      'Valves',
      'Warning Signs',
      'Accessories',
    ],
    'Emergency Lighting': [
      'Exit Signs',
      'Emergency Lights',
      'Batteries',
      'Wiring',
      'Central Battery Systems',
    ],
    'Fire Extinguishers': [
      'Portable Extinguishers',
      'Cabinets',
      'Mounting Hardware',
      'Signs',
      'Wheeled Units',
    ],
    'Fire Hydrant': [
      'Hydrant Valves',
      'Piping',
      'Hose Connections',
      'Cabinets',
      'Accessories',
    ],
    'Fire Hose Reel': [
      'Hose Reels',
      'Hoses',
      'Nozzles',
      'Cabinets',
      'Piping',
      'Valves',
    ],
    'Deluge System': [
      'Deluge Valves',
      'Nozzles',
      'Piping',
      'Detection Devices',
      'Control Panels',
      'Accessories',
    ],
    'Pre-Action System': [
      'Pre-Action Valves',
      'Sprinkler Heads',
      'Piping',
      'Detection Devices',
      'Control Panels',
      'Accessories',
    ],
    'Dry Chemical System': [
      'Dry Chemical Agent',
      'Cylinders',
      'Nozzles',
      'Control Panels',
      'Detection Devices',
      'Piping',
      'Accessories',
    ],
  };

  // Labor categories
  static const List<String> laborCategories = [
    'Installation',
    'Programming',
    'Testing',
    'Project Management',
    'Engineering',
    'Permits and Inspections',
  ];

  // Units of measurement
  static const List<String> units = [
    'Each',
    'Feet',
    'Meters',
    'Square Feet',
    'Square Meters',
    'Pounds',
    'Kilograms',
    'Gallons',
    'Liters',
    'Box',
    'Roll',
  ];

  // Default labor rates
  static const Map<String, double> defaultLaborRates = {
    'Installation': 75.0,
    'Programming': 95.0,
    'Testing': 85.0,
    'Project Management': 110.0,
    'Engineering': 125.0,
    'Permits and Inspections': 90.0,
  };

  // Sample materials for all system types
  static final Map<String, List<Map<String, dynamic>>> sampleMaterialsBySystem = {
    'Fire Alarm': [
      {
        'name': 'Addressable Smoke Detector',
        'category': 'Initiating Devices',
        'description': 'Photoelectric smoke detector with addressable module, compatible with all major fire alarm panels',
        'unit': 'Each',
        'exWorksUnitCost': 45.0,
        'localUnitCost': 0.0,
        'installationUnitCost': 25.0,
        'isImported': true,
        'vendor': 'Honeywell',
        'approval': 'UL Listed, FM Approved, NFPA 72 Compliant',
      },
      {
        'name': 'Heat Detector - Fixed Temperature',
        'category': 'Initiating Devices',
        'description': 'Fixed temperature heat detector (135°F/57°C) with rate-of-rise detection capability',
        'unit': 'Each',
        'exWorksUnitCost': 35.0,
        'localUnitCost': 0.0,
        'installationUnitCost': 20.0,
        'isImported': true,
        'vendor': 'System Sensor',
        'approval': 'UL Listed, FM Approved',
      },
      {
        'name': 'Manual Pull Station - Double Action',
        'category': 'Initiating Devices',
        'description': 'Double action manual pull station with break-glass design and key reset',
        'unit': 'Each',
        'exWorksUnitCost': 55.0,
        'localUnitCost': 0.0,
        'installationUnitCost': 30.0,
        'isImported': true,
        'vendor': 'Edwards',
        'approval': 'UL Listed, ADA Compliant',
      },
      {
        'name': 'Horn/Strobe Combination - Wall Mount',
        'category': 'Notification Devices',
        'description': 'Wall-mounted horn/strobe combination with selectable candela settings (15-110cd)',
        'unit': 'Each',
        'exWorksUnitCost': 65.0,
        'localUnitCost': 0.0,
        'installationUnitCost': 35.0,
        'isImported': true,
        'vendor': 'Gentex',
        'approval': 'UL Listed, ADA Compliant',
      },
      {
        'name': 'Fire Alarm Control Panel',
        'category': 'Control Equipment',
        'description': 'Addressable fire alarm control panel with 4 loops capacity, supporting up to 250 devices per loop. Includes LCD display, programming software, and network capability.',
        'unit': 'Each',
        'exWorksUnitCost': 1200.0,
        'localUnitCost': 4500.0,
        'installationUnitCost': 500.0,
        'isImported': true,
        'vendor': 'Siemens',
        'approval': 'UL Listed, NFPA 72 Compliant, FM Approved',
      },
      {
        'name': 'Fire Alarm Wire 14/2',
        'category': 'Wiring',
        'description': 'Fire-rated 14 AWG 2-conductor solid copper wire with red jacket, FPLR rated for riser applications',
        'unit': 'Feet',
        'exWorksUnitCost': 0.75,
        'localUnitCost': 2.81,
        'installationUnitCost': 0.5,
        'isImported': true,
        'vendor': 'Belden',
        'approval': 'UL Listed, NEC Compliant',
      },
      {
        'name': 'Fire Alarm Wire 14/4',
        'category': 'Wiring',
        'description': 'Fire-rated 14 AWG 4-conductor solid copper wire with red jacket, FPLR rated for riser applications',
        'unit': 'Feet',
        'exWorksUnitCost': 1.25,
        'localUnitCost': 4.69,
        'installationUnitCost': 0.75,
        'isImported': true,
        'vendor': 'Belden',
        'approval': 'UL Listed, NEC Compliant',
      },
      {
        'name': 'Battery 12V 7AH',
        'category': 'Accessories',
        'description': 'Sealed lead-acid backup battery, 12V 7AH capacity, for fire alarm control panels and emergency systems',
        'unit': 'Each',
        'exWorksUnitCost': 25.0,
        'localUnitCost': 93.75,
        'installationUnitCost': 10.0,
        'isImported': true,
        'vendor': 'Yuasa',
        'approval': 'UL Listed, CE Certified',
      },
    ],
    'Water Sprinkler': [
      {
        'name': 'Standard Sprinkler Head',
        'category': 'Sprinkler Heads',
        'unit': 'Each',
        'exWorksUnitCost': 12.0,
        'localUnitCost': 45.0,
        'installationUnitCost': 8.0,
        'isImported': true,
        'vendor': 'Viking',
        'approval': 'UL Listed, FM Approved, NFPA 13 Compliant',
      },
      {
        'name': 'Concealed Sprinkler Head',
        'category': 'Sprinkler Heads',
        'unit': 'Each',
        'exWorksUnitCost': 18.0,
        'localUnitCost': 67.5,
        'installationUnitCost': 10.0,
        'isImported': true,
      },
      {
        'name': 'Steel Pipe 1"',
        'category': 'Piping',
        'unit': 'Feet',
        'exWorksUnitCost': 3.5,
        'localUnitCost': 13.13,
        'installationUnitCost': 2.0,
        'isImported': true,
      },
      {
        'name': 'Steel Pipe 1.5"',
        'category': 'Piping',
        'unit': 'Feet',
        'exWorksUnitCost': 5.25,
        'localUnitCost': 19.69,
        'installationUnitCost': 3.0,
        'isImported': true,
      },
      {
        'name': 'Steel Pipe 2"',
        'category': 'Piping',
        'unit': 'Feet',
        'exWorksUnitCost': 7.0,
        'localUnitCost': 26.25,
        'installationUnitCost': 4.0,
        'isImported': true,
      },
      {
        'name': 'Control Valve 2"',
        'category': 'Valves',
        'unit': 'Each',
        'exWorksUnitCost': 175.0,
        'localUnitCost': 656.25,
        'installationUnitCost': 75.0,
        'isImported': true,
      },
      {
        'name': 'Elbow 1"',
        'category': 'Fittings',
        'unit': 'Each',
        'exWorksUnitCost': 4.5,
        'localUnitCost': 16.88,
        'installationUnitCost': 3.0,
        'isImported': true,
      },
      {
        'name': 'Tee 1"',
        'category': 'Fittings',
        'unit': 'Each',
        'exWorksUnitCost': 6.0,
        'localUnitCost': 22.5,
        'installationUnitCost': 4.0,
        'isImported': true,
      },
      {
        'name': 'Pipe Hanger',
        'category': 'Hangers',
        'unit': 'Each',
        'exWorksUnitCost': 3.25,
        'localUnitCost': 12.19,
        'installationUnitCost': 2.0,
        'isImported': true,
      },
    ],
    'Foam System': [
      {
        'name': 'AFFF Foam Concentrate',
        'category': 'Foam Concentrate',
        'unit': 'Gallons',
        'exWorksUnitCost': 35.0,
        'localUnitCost': 131.25,
        'installationUnitCost': 5.0,
        'isImported': true,
        'vendor': 'Ansul',
        'approval': 'UL Listed, FM Approved, NFPA 11 Compliant',
      },
      {
        'name': 'Foam Bladder Tank 100 Gal',
        'category': 'Bladder Tanks',
        'unit': 'Each',
        'exWorksUnitCost': 2500.0,
        'localUnitCost': 9375.0,
        'installationUnitCost': 750.0,
        'isImported': true,
      },
      {
        'name': 'Foam Proportioner 2"',
        'category': 'Proportioners',
        'unit': 'Each',
        'exWorksUnitCost': 450.0,
        'localUnitCost': 1687.5,
        'installationUnitCost': 150.0,
        'isImported': true,
      },
      {
        'name': 'Foam Chamber',
        'category': 'Foam Chambers',
        'unit': 'Each',
        'exWorksUnitCost': 750.0,
        'localUnitCost': 2812.5,
        'installationUnitCost': 200.0,
        'isImported': true,
      },
    ],
    'Novec 1230': [
      {
        'name': 'Novec 1230 Agent',
        'category': 'Novec 1230 Agent',
        'unit': 'Pounds',
        'exWorksUnitCost': 45.0,
        'localUnitCost': 168.75,
        'installationUnitCost': 5.0,
        'isImported': true,
        'vendor': '3M',
        'approval': 'UL Listed, FM Approved, EPA Approved',
      },
      {
        'name': 'Novec 1230 Cylinder 40lb',
        'category': 'Cylinders',
        'unit': 'Each',
        'exWorksUnitCost': 1200.0,
        'localUnitCost': 4500.0,
        'installationUnitCost': 300.0,
        'isImported': true,
      },
      {
        'name': 'Novec 1230 Discharge Nozzle',
        'category': 'Nozzles',
        'unit': 'Each',
        'exWorksUnitCost': 85.0,
        'localUnitCost': 318.75,
        'installationUnitCost': 40.0,
        'isImported': true,
      },
      {
        'name': 'Novec 1230 Control Panel',
        'category': 'Control Panels',
        'unit': 'Each',
        'exWorksUnitCost': 950.0,
        'localUnitCost': 3562.5,
        'installationUnitCost': 250.0,
        'isImported': true,
      },
    ],
    'FM-200': [
      {
        'name': 'FM-200 Agent',
        'category': 'FM-200 Agent',
        'unit': 'Pounds',
        'exWorksUnitCost': 35.0,
        'localUnitCost': 131.25,
        'installationUnitCost': 5.0,
        'isImported': true,
        'vendor': 'Chemours',
        'approval': 'UL Listed, FM Approved, EPA Approved',
      },
      {
        'name': 'FM-200 Cylinder 40lb',
        'category': 'Cylinders',
        'unit': 'Each',
        'exWorksUnitCost': 1100.0,
        'localUnitCost': 4125.0,
        'installationUnitCost': 300.0,
        'isImported': true,
      },
      {
        'name': 'FM-200 Discharge Nozzle',
        'category': 'Nozzles',
        'unit': 'Each',
        'exWorksUnitCost': 80.0,
        'localUnitCost': 300.0,
        'installationUnitCost': 40.0,
        'isImported': true,
      },
      {
        'name': 'FM-200 Control Panel',
        'category': 'Control Panels',
        'unit': 'Each',
        'exWorksUnitCost': 900.0,
        'localUnitCost': 3375.0,
        'installationUnitCost': 250.0,
        'isImported': true,
      },
    ],
    'CO2 System': [
      {
        'name': 'CO2 Cylinder 50lb',
        'category': 'CO2 Cylinders',
        'unit': 'Each',
        'exWorksUnitCost': 650.0,
        'localUnitCost': 2437.5,
        'installationUnitCost': 200.0,
        'isImported': true,
        'vendor': 'Kidde',
        'approval': 'UL Listed, FM Approved, NFPA 12 Compliant',
      },
      {
        'name': 'CO2 Discharge Nozzle',
        'category': 'Nozzles',
        'unit': 'Each',
        'exWorksUnitCost': 75.0,
        'localUnitCost': 281.25,
        'installationUnitCost': 35.0,
        'isImported': true,
      },
      {
        'name': 'CO2 Warning Sign',
        'category': 'Warning Signs',
        'unit': 'Each',
        'exWorksUnitCost': 25.0,
        'localUnitCost': 93.75,
        'installationUnitCost': 10.0,
        'isImported': true,
      },
      {
        'name': 'CO2 Control Panel',
        'category': 'Control Panels',
        'unit': 'Each',
        'exWorksUnitCost': 850.0,
        'localUnitCost': 3187.5,
        'installationUnitCost': 250.0,
        'isImported': true,
      },
    ],
  };

  // Theme colors
  static const Color primaryColor = Color(0xFF1E88E5);
  static const Color accentColor = Color(0xFFFF8F00);
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color errorColor = Color(0xFFD32F2F);
  static const Color textColor = Color(0xFF212121);
  static const Color secondaryTextColor = Color(0xFF757575);

  // Theme data
  static ThemeData getTheme() {
    return ThemeData(
      primaryColor: primaryColor,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        secondary: accentColor,
        error: errorColor,
      ),
      scaffoldBackgroundColor: backgroundColor,
      textTheme: const TextTheme(
        bodyLarge: TextStyle(color: textColor),
        bodyMedium: TextStyle(color: textColor),
        titleLarge: TextStyle(color: textColor, fontWeight: FontWeight.bold),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      tabBarTheme: const TabBarTheme(
        labelColor: Colors.white,
        labelStyle: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
        unselectedLabelColor: Colors.white70,
        indicatorColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
      ),
    );
  }
}
