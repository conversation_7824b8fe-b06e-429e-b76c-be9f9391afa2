import 'package:isar/isar.dart';

part 'isar_models.g.dart';

// Base model for all system items (alarm, water, foam, etc.)
@collection
class SystemItem {
  Id id = Isar.autoIncrement; // Auto-incrementing ID

  @Index(unique: true)
  String? itemId; // The original ID from SQLite

  String? model;
  String? description;
  String? manufacturer;
  String? approval;
  String? exWorksPrice;
  String? localPrice;
  String? installationPrice;
  DateTime? createdAt;
  
  // The type of system this item belongs to
  @enumerated
  SystemType systemType = SystemType.alarm;
}

// Project model
@collection
class Project {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? projectId; // The original ID from SQLite

  String? name;
  String? description;
  String? client;
  String? location;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? status;
  String? currency;
  String? exchangeRate;

  // Backlink to all systems in this project
  @Backlink(to: 'project')
  final systems = IsarLinks<ProjectSystem>();
}

// Project system model
@collection
class ProjectSystem {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? systemId; // The original ID from SQLite

  // Link to the parent project
  final project = IsarLink<Project>();

  @enumerated
  SystemType systemType = SystemType.alarm;
  
  String? name;
  String? description;
  DateTime? createdAt;

  // Backlink to all items in this system
  @Backlink(to: 'system')
  final items = IsarLinks<SystemItemLink>();
}

// Link between project systems and system items
@collection
class SystemItemLink {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? linkId; // The original ID from SQLite

  // Link to the parent system
  final system = IsarLink<ProjectSystem>();
  
  // Link to the item
  final item = IsarLink<SystemItem>();
  
  int quantity = 1;
  String? exWorksPrice;
  String? localPrice;
  String? installationPrice;
  DateTime? createdAt;
}

// Enum for system types
enum SystemType {
  alarm,
  water,
  foam,
  fm200,
  novec,
  co2,
  materials
}

// Extension to convert string to SystemType
extension SystemTypeExtension on String {
  SystemType toSystemType() {
    switch (toLowerCase()) {
      case 'alarm':
        return SystemType.alarm;
      case 'water':
        return SystemType.water;
      case 'foam':
        return SystemType.foam;
      case 'fm200':
        return SystemType.fm200;
      case 'novec':
        return SystemType.novec;
      case 'co2':
        return SystemType.co2;
      case 'materials':
        return SystemType.materials;
      default:
        return SystemType.alarm;
    }
  }
}

// Extension to convert SystemType to string
extension SystemTypeStringExtension on SystemType {
  String toShortString() {
    return toString().split('.').last;
  }
}
