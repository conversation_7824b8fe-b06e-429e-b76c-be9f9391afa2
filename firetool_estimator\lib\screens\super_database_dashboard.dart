import 'package:flutter/material.dart';
import '../models/super_database_models.dart';
import '../services/super_database_service.dart';
import '../services/excel_import_export_service.dart';
import '../services/supabase_sync_service.dart';
import '../widgets/section_card.dart';
import '../widgets/create_section_dialog.dart';
import '../widgets/sync_status_widget.dart';
import 'section_detail_tabbed_screen.dart';
import 'supabase_settings_screen.dart';

class SuperDatabaseDashboard extends StatefulWidget {
  const SuperDatabaseDashboard({super.key});

  @override
  State<SuperDatabaseDashboard> createState() => _SuperDatabaseDashboardState();
}

class _SuperDatabaseDashboardState extends State<SuperDatabaseDashboard> {
  final _superDbService = SuperDatabaseService();
  final _excelService = ExcelImportExportService();
  final _supabaseService = SupabaseSyncService();

  List<Section> _sections = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      await _superDbService.initialize();
      await _loadSections();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize SuperDatabase: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadSections() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final sections = await _superDbService.getSections();

      setState(() {
        _sections = sections;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load sections: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _createNewSection() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => const CreateSectionDialog(),
    );

    if (result != null) {
      try {
        if (result['importFromExcel'] == true) {
          await _importSectionFromExcel(result);
        } else {
          await _createEmptySection(result);
        }
        await _loadSections();
      } catch (e) {
        _showErrorSnackBar('Failed to create section: $e');
      }
    }
  }

  Future<void> _importSectionFromExcel(Map<String, dynamic> sectionData) async {
    try {
      _showLoadingDialog('Importing Excel file...');

      final operation = await _excelService.importExcelAsSection(
        sectionDisplayName: sectionData['displayName'],
        description: sectionData['description'],
        iconName: sectionData['iconName'],
        color: sectionData['color'],
      );

      Navigator.of(context).pop(); // Close loading dialog

      if (operation.status == OperationStatus.completed) {
        _showSuccessSnackBar(
          'Successfully imported ${operation.processedRows} rows from Excel file'
        );
      } else {
        _showErrorSnackBar(
          'Import failed: ${operation.errorMessage ?? "Unknown error"}'
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      _showErrorSnackBar('Import failed: $e');
    }
  }

  Future<void> _createEmptySection(Map<String, dynamic> sectionData) async {
    await _superDbService.createSection(
      name: sectionData['name'],
      displayName: sectionData['displayName'],
      description: sectionData['description'],
      iconName: sectionData['iconName'],
      color: sectionData['color'],
    );

    _showSuccessSnackBar('Section "${sectionData['displayName']}" created successfully');
  }

  Future<void> _exportSection(Section section) async {
    try {
      _showLoadingDialog('Exporting section to Excel...');

      final operation = await _excelService.exportSectionToExcel(
        sectionId: section.id,
      );

      Navigator.of(context).pop(); // Close loading dialog

      if (operation.status == OperationStatus.completed) {
        _showSuccessSnackBar(
          'Successfully exported ${operation.processedRows} rows to Excel file'
        );
      } else {
        _showErrorSnackBar(
          'Export failed: ${operation.errorMessage ?? "Unknown error"}'
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      _showErrorSnackBar('Export failed: $e');
    }
  }

  Future<void> _syncWithSupabase() async {
    if (!_supabaseService.isInitialized) {
      _showErrorSnackBar('Supabase not configured. Please check settings.');
      return;
    }

    try {
      _showLoadingDialog('Syncing with Supabase...');

      final result = await _supabaseService.uploadAllSections();

      Navigator.of(context).pop(); // Close loading dialog

      if (result.status == SyncStatus.completed) {
        _showSuccessSnackBar(
          'Successfully synced ${result.sectionsProcessed} sections with Supabase'
        );
      } else {
        _showErrorSnackBar(
          'Sync failed: ${result.errorMessage ?? "Unknown error"}'
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      _showErrorSnackBar('Sync failed: $e');
    }
  }

  void _openSettings() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SupabaseSettingsScreen(),
      ),
    );
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SuperDatabase'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.cloud_sync),
            onPressed: _syncWithSupabase,
            tooltip: 'Sync with Supabase',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _openSettings,
            tooltip: 'Settings',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSections,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewSection,
        icon: const Icon(Icons.add),
        label: const Text('New Section'),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading SuperDatabase...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red.shade700),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSections,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_sections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.storage,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No sections found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first section to get started',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createNewSection,
              icon: const Icon(Icons.add),
              label: const Text('Create Section'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Sync status widget
        const SyncStatusWidget(),

        // Sections grid
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.2,
              ),
              itemCount: _sections.length,
              itemBuilder: (context, index) {
                final section = _sections[index];
                return SectionCard(
                  section: section,
                  onTap: () => _navigateToSection(section),
                  onExport: () => _exportSection(section),
                  onDelete: () => _deleteSection(section),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToSection(Section section) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SectionDetailTabbedScreen(section: section),
      ),
    ).then((_) => _loadSections()); // Refresh when returning
  }

  Future<void> _deleteSection(Section section) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Section'),
        content: Text(
          'Are you sure you want to delete "${section.displayName}"? '
          'This will permanently delete all data in this section.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _superDbService.deleteSection(section.id);
        _showSuccessSnackBar('Section "${section.displayName}" deleted');
        await _loadSections();
      } catch (e) {
        _showErrorSnackBar('Failed to delete section: $e');
      }
    }
  }
}
