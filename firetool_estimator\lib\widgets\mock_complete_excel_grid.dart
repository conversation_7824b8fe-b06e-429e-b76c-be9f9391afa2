import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../services/local_sql_service.dart';
import '../services/sqlite_schema_manager.dart';
import 'package:intl/intl.dart';

class MockCompleteExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const MockCompleteExcelGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  });

  @override
  State<MockCompleteExcelGrid> createState() => _MockCompleteExcelGridState();
}

enum ColumnType {
  text,
  number,
  currency,
  date,
}

class ColumnDefinition {
  final String name;
  final ColumnType type;
  final String label;
  final String? currencySymbol;

  ColumnDefinition({
    required this.name,
    required this.type,
    String? label,
    this.currencySymbol,
  }) : label = label ?? name;

  // Format value based on column type
  String formatValue(dynamic value) {
    if (value == null) return '';

    switch (type) {
      case ColumnType.text:
        return value.toString();
      case ColumnType.number:
        if (value is num) {
          return value.toString();
        }
        return value.toString();
      case ColumnType.currency:
        final symbol = currencySymbol ?? '\$';
        if (value is num) {
          return NumberFormat.currency(symbol: symbol, decimalDigits: 2).format(value);
        } else if (value is String) {
          try {
            final numValue = double.parse(value);
            return NumberFormat.currency(symbol: symbol, decimalDigits: 2).format(numValue);
          } catch (e) {
            return value;
          }
        }
        return value.toString();
      case ColumnType.date:
        if (value is DateTime) {
          return DateFormat('yyyy-MM-dd').format(value);
        } else if (value is String) {
          try {
            final date = DateTime.parse(value);
            return DateFormat('yyyy-MM-dd').format(date);
          } catch (e) {
            return value;
          }
        }
        return value.toString();
    }
  }

  // Parse value based on column type
  dynamic parseValue(String value) {
    switch (type) {
      case ColumnType.text:
        return value;
      case ColumnType.number:
        try {
          return double.parse(value);
        } catch (e) {
          return value;
        }
      case ColumnType.currency:
        try {
          // Remove currency symbols and parse
          final cleanValue = value.replaceAll(RegExp(r'[^\d.-]'), '');
          return double.parse(cleanValue);
        } catch (e) {
          return value;
        }
      case ColumnType.date:
        try {
          return DateTime.parse(value);
        } catch (e) {
          return value;
        }
    }
  }
}

class _MockCompleteExcelGridState extends State<MockCompleteExcelGrid> {
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _filteredItems = [];
  List<ColumnDefinition> _columns = [];
  final Map<String, ColumnType> _columnTypes = {};
  bool _isLoading = true;
  String? _error;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _editingController = TextEditingController();
  final TextEditingController _columnNameController = TextEditingController();
  final TextEditingController _newColumnController = TextEditingController();

  // View mode
  bool _isCardView = false;

  // Currency symbols
  final List<String> _currencySymbols = ['\$', '€', '£', '¥', 'SAR', 'EGP', 'AED'];

  // Column filtering
  String _columnFilter = '';
  List<String> _filteredColumns = [];

  // Row selection
  final Set<String> _selectedRowIds = {};
  final bool _selectAllRows = false;

  // Scroll controllers
  final ScrollController _horizontalScrollController = ScrollController();
  final ScrollController _verticalScrollController = ScrollController();

  // Column currently being edited
  ColumnDefinition? _editingColumn;

  // Selected column for operations
  String? _selectedColumnName;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _editingController.dispose();
    _columnNameController.dispose();
    _newColumnController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final sqlService = Provider.of<LocalSqlService>(context, listen: false);
      final items = await sqlService.getItems(widget.collectionPath);

      // Extract columns from data
      final Set<String> columnSet = {'id'};

      // Add predefined columns if provided
      if (widget.predefinedColumns != null) {
        columnSet.addAll(widget.predefinedColumns!);
      }

      // Add any additional columns from the data
      for (var item in items) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('created_at');
      columnSet.remove('updated_at');

      // Create column definitions with appropriate types
      final List<ColumnDefinition> columnDefinitions = [];

      for (var columnName in columnSet) {
        ColumnType columnType = ColumnType.text; // Default type

        // Determine column type based on name or content
        if (columnName == 'id') {
          columnType = ColumnType.text;
        } else if (columnName.contains('price') ||
                  columnName.contains('cost') ||
                  columnName.contains('amount')) {
          columnType = ColumnType.currency;
        } else if (columnName.contains('date') ||
                  columnName.contains('time')) {
          columnType = ColumnType.date;
        } else if (columnName.contains('quantity') ||
                  columnName.contains('number') ||
                  columnName.contains('count')) {
          columnType = ColumnType.number;
        } else {
          // Try to determine type from data
          for (var item in items) {
            final value = item[columnName];
            if (value != null) {
              if (value is num) {
                columnType = ColumnType.number;
                break;
              } else if (value is String) {
                // Check if it's a number
                if (double.tryParse(value) != null) {
                  columnType = ColumnType.number;
                  break;
                }
                // Check if it's a date
                if (DateTime.tryParse(value) != null) {
                  columnType = ColumnType.date;
                  break;
                }
              }
            }
          }
        }

        columnDefinitions.add(ColumnDefinition(
          name: columnName,
          type: columnType,
        ));

        // Store column type for reference
        _columnTypes[columnName] = columnType;
      }

      setState(() {
        _items = items;
        _filteredItems = List.from(items);
        _columns = columnDefinitions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(_items);
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();
    setState(() {
      _filteredItems = _items.where((item) {
        for (var column in _columns) {
          final value = item[column.name]?.toString().toLowerCase() ?? '';
          if (value.contains(lowercaseQuery)) {
            return true;
          }
        }
        return false;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        foregroundColor: Colors.white,
        actions: [
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search...',
                      prefixIcon: const Icon(Icons.search),
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      isDense: true,
                    ),
                    onChanged: _filterData,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 120,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.filter_list),
                    label: const Text('Filter'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.themeColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    onPressed: _showFilterDialog,
                  ),
                ),
              ],
            ),
          ),

          // Excel-like toolbar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              border: Border(
                top: BorderSide(color: Colors.grey.shade300),
                bottom: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Determine if we're on a small screen
                final isSmallScreen = constraints.maxWidth < 600;

                // Create toolbar buttons based on screen size
                return SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      // Column operations
                      _buildToolbarButton(
                        icon: Icons.add_box,
                        label: isSmallScreen ? '' : 'Add Column',
                        onPressed: _addNewColumn,
                      ),
                      _buildToolbarButton(
                        icon: Icons.delete_outline,
                        label: isSmallScreen ? '' : 'Delete Column',
                        onPressed: _selectedColumnName != null && _selectedColumnName != 'id'
                            ? () => _deleteColumn(_selectedColumnName!)
                            : null,
                      ),
                      _buildToolbarButton(
                        icon: Icons.edit,
                        label: isSmallScreen ? '' : 'Edit Column',
                        onPressed: _selectedColumnName != null && _selectedColumnName != 'id'
                            ? () => _editSelectedColumn()
                            : null,
                      ),

                      const VerticalDivider(indent: 8, endIndent: 8),

                      // Row operations
                      _buildToolbarButton(
                        icon: Icons.add_circle_outline,
                        label: isSmallScreen ? '' : 'Add Row',
                        onPressed: _addNewRow,
                      ),
                      _buildToolbarButton(
                        icon: Icons.delete_sweep,
                        label: isSmallScreen ? '' : 'Delete Rows',
                        onPressed: _selectedRowIds.isNotEmpty
                            ? () => _deleteSelectedRows()
                            : null,
                      ),

                      const VerticalDivider(indent: 8, endIndent: 8),

                      // Import/Export buttons
                      _buildToolbarButton(
                        icon: Icons.file_upload,
                        label: isSmallScreen ? '' : 'Import',
                        onPressed: _importFromCSV,
                      ),
                      _buildToolbarButton(
                        icon: Icons.file_download,
                        label: isSmallScreen ? '' : 'Export',
                        onPressed: _exportToCSV,
                      ),

                      const VerticalDivider(indent: 8, endIndent: 8),

                      // View toggle
                      _buildToolbarButton(
                        icon: _isCardView ? Icons.grid_on : Icons.view_module,
                        label: isSmallScreen ? '' : (_isCardView ? 'Table View' : 'Card View'),
                        onPressed: () {
                          setState(() {
                            _isCardView = !_isCardView;
                          });
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          // Status bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: Row(
              children: [
                Text(
                  _selectedColumnName != null
                      ? 'Selected: $_selectedColumnName'
                      : 'Click on any cell to edit',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey.shade700,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_filteredItems.length} items',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: widget.themeColor,
                  ),
                ),
              ],
            ),
          ),

          // Data display
          Expanded(
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: widget.themeColor,
                    ),
                  )
                : _error != null
                    ? Center(
                        child: Text(
                          'Error: $_error',
                          style: const TextStyle(color: Colors.red),
                        ),
                      )
                    : _isCardView ? _buildCardView() : _buildDataTable(),
          ),
        ],
      ),
    );
  }

  // Add a new row
  Future<void> _addNewRow() async {
    try {
      final sqlService = Provider.of<LocalSqlService>(context, listen: false);

      // Create empty item
      final Map<String, dynamic> newItem = {
        'id': 'item-${DateTime.now().millisecondsSinceEpoch}',
      };

      // Add default values for each column
      for (var column in _columns) {
        if (column.name != 'id') {
          newItem[column.name] = '';
        }
      }

      // Add to database
      await sqlService.addItem(widget.collectionPath, newItem);

      // Reload data
      await _loadData();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('New row added successfully'),
            backgroundColor: widget.themeColor,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding row: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Update a cell value
  Future<void> _updateCellValue(String itemId, String columnName, dynamic newValue) async {
    try {
      final sqlService = Provider.of<LocalSqlService>(context, listen: false);

      // Update in database
      await sqlService.updateField(widget.collectionPath, itemId, columnName, newValue);

      // Update in local data
      setState(() {
        final itemIndex = _items.indexWhere((item) => item['id'] == itemId);
        if (itemIndex != -1) {
          _items[itemIndex][columnName] = newValue;

          // Update filtered items if needed
          final filteredIndex = _filteredItems.indexWhere((item) => item['id'] == itemId);
          if (filteredIndex != -1) {
            _filteredItems[filteredIndex][columnName] = newValue;
          }
        }
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Cell updated successfully'),
            backgroundColor: widget.themeColor,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating cell: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Show edit dialog for a cell
  void _showEditDialog(String itemId, ColumnDefinition column, dynamic currentValue) {
    _editingController.text = currentValue?.toString() ?? '';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${column.label}'),
        content: TextField(
          controller: _editingController,
          autofocus: true,
          decoration: InputDecoration(
            labelText: column.label,
            border: const OutlineInputBorder(),
          ),
          keyboardType: column.type == ColumnType.number || column.type == ColumnType.currency
              ? TextInputType.number
              : column.type == ColumnType.date
                  ? TextInputType.datetime
                  : TextInputType.text,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Parse value based on column type
              final parsedValue = column.parseValue(_editingController.text);

              // Update the value
              _updateCellValue(itemId, column.name, parsedValue);

              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // Edit the selected column
  void _editSelectedColumn() {
    if (_selectedColumnName == null || _selectedColumnName == 'id') return;

    final column = _columns.firstWhere(
      (col) => col.name == _selectedColumnName,
      orElse: () => _columns.first,
    );

    _showColumnEditDialog(column);
  }

  // Show filter dialog
  Future<void> _showFilterDialog() async {
    final TextEditingController filterController = TextEditingController(text: _columnFilter);

    // Create a map to track which columns are visible
    Map<String, bool> columnVisibility = {};
    for (var column in _columns) {
      columnVisibility[column.name] = !_filteredColumns.contains(column.name);
    }

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Filter Data'),
          content: SizedBox(
            width: 350,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Text filter
                TextField(
                  controller: filterController,
                  decoration: const InputDecoration(
                    labelText: 'Search Text',
                    hintText: 'Filter by any text',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    // Apply filter immediately
                    _filterData(value);
                  },
                ),
                const SizedBox(height: 16),

                // Column visibility header with select/deselect all
                Row(
                  children: [
                    const Text('Column Visibility:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const Spacer(),
                    TextButton(
                      onPressed: () {
                        setDialogState(() {
                          // Select all columns
                          for (var column in _columns) {
                            columnVisibility[column.name] = true;
                          }
                        });
                      },
                      child: const Text('Show All'),
                    ),
                    TextButton(
                      onPressed: () {
                        setDialogState(() {
                          // Deselect all columns except ID
                          for (var column in _columns) {
                            columnVisibility[column.name] = column.name == 'id';
                          }
                        });
                      },
                      child: const Text('Hide All'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Column visibility list
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  height: 250,
                  child: Scrollbar(
                    thickness: 8,
                    child: ListView(
                      children: _columns.map((column) => CheckboxListTile(
                        title: Row(
                          children: [
                            Text(column.label),
                            const SizedBox(width: 8),
                            Icon(
                              column.type == ColumnType.text ? Icons.text_fields :
                              column.type == ColumnType.number ? Icons.numbers :
                              column.type == ColumnType.currency ? Icons.attach_money :
                              Icons.calendar_today,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                          ],
                        ),
                        value: columnVisibility[column.name],
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              columnVisibility[column.name] = value;
                            });
                          }
                        },
                        dense: true,
                      )).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                // Reset all filters
                setState(() {
                  _filteredColumns = [];
                  _columnFilter = '';
                  _filterData('');
                });
                Navigator.pop(context);
              },
              child: const Text('Reset Filters'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                // Apply column visibility filter
                setState(() {
                  _filteredColumns = columnVisibility.entries
                      .where((entry) => !entry.value)
                      .map((entry) => entry.key)
                      .toList();

                  // Apply text filter
                  _columnFilter = filterController.text;
                  _filterData(filterController.text);
                });

                Navigator.pop(context);
              },
              child: const Text('Apply'),
            ),
          ],
        ),
      ),
    );

    // Dispose the controller
    filterController.dispose();
  }

  // Delete selected rows
  Future<void> _deleteSelectedRows() async {
    if (_selectedRowIds.isEmpty) return;

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: Text('Are you sure you want to delete ${_selectedRowIds.length} selected row(s)? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        if (!mounted) return;
        final sqlService = Provider.of<LocalSqlService>(context, listen: false);

        // Create a copy of the selected IDs before clearing
        final List<String> idsToDelete = List.from(_selectedRowIds);
        final int count = idsToDelete.length;

        // Update state first to provide immediate feedback
        setState(() {
          _items.removeWhere((item) => idsToDelete.contains(item['id'].toString()));
          _filteredItems.removeWhere((item) => idsToDelete.contains(item['id'].toString()));
          _selectedRowIds.clear();
        });

        // Then delete from database
        for (var id in idsToDelete) {
          await sqlService.deleteItem(widget.collectionPath, id);
        }

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$count row(s) deleted successfully'),
              backgroundColor: widget.themeColor,
            ),
          );
        }
      } catch (e) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting rows: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Show dialog to edit column properties
  void _showColumnEditDialog(ColumnDefinition column) {
    _columnNameController.text = column.label;

    // Selected type for the dialog
    ColumnType selectedType = column.type;

    // Selected currency symbol
    String selectedCurrencySymbol = column.currencySymbol ?? '\$';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Edit Column: ${column.name}'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Column label (display name)
                TextField(
                  controller: _columnNameController,
                  decoration: const InputDecoration(
                    labelText: 'Column Label',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Column type selection
                const Text('Column Type:'),
                const SizedBox(height: 8),
                DropdownButtonFormField<ColumnType>(
                  value: selectedType,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    DropdownMenuItem(
                      value: ColumnType.text,
                      child: Row(
                        children: [
                          const Icon(Icons.text_fields, size: 20),
                          const SizedBox(width: 8),
                          const Text('Text'),
                        ],
                      ),
                    ),
                    DropdownMenuItem(
                      value: ColumnType.number,
                      child: Row(
                        children: [
                          const Icon(Icons.numbers, size: 20),
                          const SizedBox(width: 8),
                          const Text('Number'),
                        ],
                      ),
                    ),
                    DropdownMenuItem(
                      value: ColumnType.currency,
                      child: Row(
                        children: [
                          const Icon(Icons.attach_money, size: 20),
                          const SizedBox(width: 8),
                          const Text('Currency'),
                        ],
                      ),
                    ),
                    DropdownMenuItem(
                      value: ColumnType.date,
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 20),
                          const SizedBox(width: 8),
                          const Text('Date'),
                        ],
                      ),
                    ),
                  ],
                  onChanged: (newType) {
                    if (newType != null) {
                      setDialogState(() {
                        selectedType = newType;
                      });
                    }
                  },
                ),

                // Currency symbol selection (only shown for currency type)
                if (selectedType == ColumnType.currency) ...[
                  const SizedBox(height: 16),
                  const Text('Currency Symbol:'),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: selectedCurrencySymbol,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                    ),
                    items: _currencySymbols.map((symbol) => DropdownMenuItem(
                      value: symbol,
                      child: Text(symbol),
                    )).toList(),
                    onChanged: (newSymbol) {
                      if (newSymbol != null) {
                        setDialogState(() {
                          selectedCurrencySymbol = newSymbol;
                        });
                      }
                    },
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                // Update column label and type
                _updateColumnProperties(
                  column.name,
                  _columnNameController.text,
                  selectedType,
                  selectedType == ColumnType.currency ? selectedCurrencySymbol : null,
                );

                Navigator.pop(context);
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  // Update column properties
  void _updateColumnProperties(String columnName, String newLabel, ColumnType newType, [String? currencySymbol]) {
    setState(() {
      // Find the column index
      final columnIndex = _columns.indexWhere((col) => col.name == columnName);
      if (columnIndex != -1) {
        // Create a new column definition with updated properties
        final updatedColumn = ColumnDefinition(
          name: columnName,
          type: newType,
          label: newLabel,
          currencySymbol: newType == ColumnType.currency ? currencySymbol : null,
        );

        // Replace the old column with the updated one
        _columns[columnIndex] = updatedColumn;

        // Update column type in the map
        _columnTypes[columnName] = newType;
      }
    });

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Column updated successfully'),
          backgroundColor: widget.themeColor,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  // Add a new column
  Future<void> _addNewColumn() async {
    _newColumnController.text = '';
    ColumnType selectedType = ColumnType.text;

    // Show dialog to get column name
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Add New Column'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _newColumnController,
                decoration: const InputDecoration(
                  labelText: 'Column Name',
                  hintText: 'Enter column name',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<ColumnType>(
                value: selectedType,
                decoration: const InputDecoration(
                  labelText: 'Column Type',
                  border: OutlineInputBorder(),
                ),
                items: [
                  DropdownMenuItem(
                    value: ColumnType.text,
                    child: Row(
                      children: [
                        const Icon(Icons.text_fields, size: 20),
                        const SizedBox(width: 8),
                        const Text('Text'),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: ColumnType.number,
                    child: Row(
                      children: [
                        const Icon(Icons.numbers, size: 20),
                        const SizedBox(width: 8),
                        const Text('Number'),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: ColumnType.currency,
                    child: Row(
                      children: [
                        const Icon(Icons.attach_money, size: 20),
                        const SizedBox(width: 8),
                        const Text('Currency'),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: ColumnType.date,
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today, size: 20),
                        const SizedBox(width: 8),
                        const Text('Date'),
                      ],
                    ),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setDialogState(() {
                      selectedType = value;
                    });
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (_newColumnController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Column name cannot be empty'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                Navigator.pop(
                  context,
                  {
                    'name': _newColumnController.text.trim(),
                    'type': selectedType,
                  }
                );
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );

    if (result != null) {
      try {
        final columnName = result['name'] as String;
        final columnType = result['type'] as ColumnType;

        // Check if column already exists
        if (_columns.any((col) => col.name == columnName)) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Column with this name already exists'),
                backgroundColor: Colors.red,
              ),
            );
          }
          return;
        }

        // Create a new column definition
        final newColumn = ColumnDefinition(
          name: columnName,
          type: columnType,
          label: columnName,
        );

        // Update the database schema
        if (!mounted) return;
        final sqlService = Provider.of<LocalSqlService>(context, listen: false);

        // First, create a list of new items with the new column
        final List<Map<String, dynamic>> updatedItems = [];
        for (var item in _items) {
          final Map<String, dynamic> updatedItem = Map<String, dynamic>.from(item);
          updatedItem[columnName] = '';
          updatedItems.add(updatedItem);
        }

        // Update the state first for immediate feedback
        setState(() {
          // Update the items list
          _items = updatedItems;
          _filteredItems = List.from(updatedItems);

          // Add the new column to the columns list
          _columns.add(newColumn);

          // Update column type map
          _columnTypes[columnName] = columnType;
        });

        // Update each item in the database
        try {
          for (var item in updatedItems) {
            await sqlService.updateItem(widget.collectionPath, item['id'], item);
          }

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Column "$columnName" added successfully'),
                backgroundColor: widget.themeColor,
              ),
            );
          }
        } catch (dbError) {
          // If database update fails, revert the state changes
          if (mounted) {
            setState(() {
              // Reload original data
              _loadData();
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error updating database: $dbError'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error adding column: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Delete a column
  Future<void> _deleteColumn(String columnName) async {
    // Don't allow deleting the ID column
    if (columnName == 'id') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot delete the ID column'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: Text('Are you sure you want to delete the column "$columnName"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        if (!mounted) return;
        final sqlService = Provider.of<LocalSqlService>(context, listen: false);

        // Create updated items without the column
        final List<Map<String, dynamic>> updatedItems = [];
        for (var item in _items) {
          final Map<String, dynamic> updatedItem = Map<String, dynamic>.from(item);
          updatedItem.remove(columnName);
          updatedItems.add(updatedItem);
        }

        // Update state first for immediate feedback
        setState(() {
          _items = updatedItems;
          _filteredItems = List.from(updatedItems);
          _columns.removeWhere((col) => col.name == columnName);
          _columnTypes.remove(columnName);
          if (_selectedColumnName == columnName) {
            _selectedColumnName = null;
          }
        });

        // Update database
        for (var item in updatedItems) {
          await sqlService.updateItem(widget.collectionPath, item['id'], item);
        }
      } catch (e) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting column: $e'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Column "$columnName" deleted successfully'),
            backgroundColor: widget.themeColor,
          ),
        );
      }
    }
  }

  // Delete a row
  Future<void> _deleteRow(String itemId) async {
    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: const Text('Are you sure you want to delete this row? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        if (!mounted) return;
        final sqlService = Provider.of<LocalSqlService>(context, listen: false);

        // Delete from database
        await sqlService.deleteItem(widget.collectionPath, itemId);

        // Delete from local data
        setState(() {
          _items.removeWhere((item) => item['id'] == itemId);
          _filteredItems.removeWhere((item) => item['id'] == itemId);
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Row deleted successfully'),
              backgroundColor: widget.themeColor,
            ),
          );
        }
      } catch (e) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting row: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }



  // Build toolbar button
  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2.0),
      child: Tooltip(
        message: label,
        child: ElevatedButton.icon(
          icon: Icon(icon, size: 18),
          label: Text(label),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: onPressed != null ? widget.themeColor : Colors.grey,
            disabledForegroundColor: Colors.grey.shade400,
            elevation: 0,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          ),
          onPressed: onPressed,
        ),
      ),
    );
  }

  // Build card view
  Widget _buildCardView() {
    // Filter columns based on visibility settings
    final visibleColumns = _columns.where((col) => !_filteredColumns.contains(col.name) && col.name != 'id').toList();

    return Theme(
      data: Theme.of(context).copyWith(
        scrollbarTheme: ScrollbarThemeData(
          thickness: WidgetStateProperty.all(16.0),
          thumbColor: WidgetStateProperty.all(widget.themeColor.withAlpha(180)),
          radius: const Radius.circular(8.0),
          mainAxisMargin: 2.0,
          crossAxisMargin: 2.0,
          thumbVisibility: WidgetStateProperty.all(true),
          trackVisibility: WidgetStateProperty.all(true),
          trackColor: WidgetStateProperty.all(Colors.grey.shade200),
        ),
      ),
      child: Scrollbar(
        thumbVisibility: true,
        trackVisibility: true,
        thickness: 16.0,
        child: GridView.builder(
          gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: 400, // Maximum width of each card
            mainAxisExtent: 220,     // Fixed height for each card
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: _filteredItems.length,
          padding: const EdgeInsets.all(12),
          itemBuilder: (context, index) {
            final item = _filteredItems[index];
            return Card(
              margin: EdgeInsets.zero,
              elevation: 3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: _selectedRowIds.contains(item['id'])
                    ? BorderSide(color: widget.themeColor, width: 2)
                    : BorderSide.none,
              ),
              clipBehavior: Clip.antiAlias,
              child: InkWell(
                onTap: () {
                  // Toggle row selection
                  setState(() {
                    if (_selectedRowIds.contains(item['id'])) {
                      _selectedRowIds.remove(item['id']);
                    } else {
                      _selectedRowIds.add(item['id']);
                    }
                  });
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Card header with ID and actions
                    Container(
                      decoration: BoxDecoration(
                        color: widget.themeColor.withAlpha(30),
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      child: Row(
                        children: [
                          Checkbox(
                            value: _selectedRowIds.contains(item['id']),
                            onChanged: (value) {
                              setState(() {
                                if (value == true) {
                                  _selectedRowIds.add(item['id']);
                                } else {
                                  _selectedRowIds.remove(item['id']);
                                }
                              });
                            },
                            activeColor: widget.themeColor,
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ),
                          Expanded(
                            child: Text(
                              'ID: ${item['id']}',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: widget.themeColor,
                                fontSize: 13,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.edit, size: 20),
                            onPressed: () {
                              // Show edit dialog for the first non-ID field
                              final firstEditableColumn = visibleColumns.isNotEmpty
                                  ? visibleColumns.first
                                  : _columns.firstWhere(
                                      (col) => col.name != 'id',
                                      orElse: () => _columns.first,
                                    );
                              _showEditDialog(item['id'], firstEditableColumn, item[firstEditableColumn.name]);
                            },
                            tooltip: 'Edit',
                            color: widget.themeColor,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, size: 20),
                            onPressed: () => _deleteRow(item['id']),
                            tooltip: 'Delete',
                            color: Colors.red,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                    ),

                    // Card content with fields
                    Expanded(
                      child: Scrollbar(
                        thumbVisibility: true,
                        trackVisibility: true,
                        thickness: 12.0,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: visibleColumns.map((column) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 10.0),
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      // Column label and type
                                      SizedBox(
                                        width: 100,
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              column.label,
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: widget.themeColor,
                                                fontSize: 13,
                                              ),
                                            ),
                                            Text(
                                              column.type.toString().split('.').last,
                                              style: TextStyle(
                                                fontSize: 10,
                                                color: Colors.grey.shade600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      // Value field
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () {
                                            _showEditDialog(item['id'], column, item[column.name]);
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade50,
                                              borderRadius: BorderRadius.circular(6),
                                              border: Border.all(color: Colors.grey.shade300),
                                            ),
                                            child: Text(
                                              column.formatValue(item[column.name]),
                                              style: const TextStyle(fontSize: 13),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // Import data from CSV or Excel
  Future<void> _importFromCSV() async {
    try {
      // Show file picker
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'xlsx', 'xls'],
      );

      if (result == null || result.files.isEmpty || result.files.first.path == null) return;

      final filePath = result.files.first.path!;

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      final sqlService = Provider.of<LocalSqlService>(context, listen: false);
      final db = await sqlService.database;
      int importedCount = 0;

      // Check if widget is still mounted before showing dialog
      if (!mounted) return;

      // Show dialog to ask if user wants to replace or append
      final bool? replaceExisting = await showDialog<bool>(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Import Options'),
          content: const Text('Do you want to replace existing data or append to it?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Append'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Replace'),
            ),
          ],
        ),
      );

      if (replaceExisting == null) return; // Dialog was dismissed

      // Import based on file extension
      if (filePath.toLowerCase().endsWith('.csv')) {
        importedCount = await SQLiteSchemaManager.importFromCSV(
          db,
          widget.collectionPath,
          filePath,
          replaceExisting: replaceExisting,
        );
      } else if (filePath.toLowerCase().endsWith('.xlsx') ||
                filePath.toLowerCase().endsWith('.xls')) {
        importedCount = await SQLiteSchemaManager.importFromExcel(
          db,
          widget.collectionPath,
          filePath,
          replaceExisting: replaceExisting,
        );
      }

      // Reload data
      await _loadData();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Imported $importedCount items successfully'),
            backgroundColor: widget.themeColor,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error importing file: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error importing file: ${e.toString().split('\n').first}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Export data to CSV or Excel
  Future<void> _exportToCSV() async {
    try {
      // Show dialog to ask for export format
      if (!mounted) return;

      final String? exportFormat = await showDialog<String>(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Export Format'),
          content: const Text('Choose the format to export your data:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop('csv'),
              child: const Text('CSV'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(dialogContext).pop('excel'),
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Excel'),
            ),
          ],
        ),
      );

      if (exportFormat == null) return; // Dialog was dismissed

      // Get file path from user
      final String fileExtension = exportFormat == 'csv' ? 'csv' : 'xlsx';
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save ${exportFormat.toUpperCase()} File',
        fileName: '${widget.title.replaceAll(' ', '_')}_export.$fileExtension',
        type: FileType.custom,
        allowedExtensions: [fileExtension],
      );

      if (result == null) return;

      // Check if widget is still mounted
      if (!mounted) return;

      // Get database instance
      final sqlService = Provider.of<LocalSqlService>(context, listen: false);
      final db = await sqlService.database;

      // Export based on selected format
      int rowsExported = 0;
      if (exportFormat == 'csv') {
        rowsExported = await SQLiteSchemaManager.exportToCSV(
          db,
          widget.collectionPath,
          result,
        );
      } else {
        rowsExported = await SQLiteSchemaManager.exportToExcel(
          db,
          widget.collectionPath,
          result,
        );
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Exported $rowsExported rows to ${exportFormat.toUpperCase()} successfully'),
            backgroundColor: widget.themeColor,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error exporting data: $e');
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error exporting data: ${e.toString().split('\n').first}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }



  Widget _buildDataTable() {
    // Filter columns based on visibility settings
    final visibleColumns = _columns.where((col) => !_filteredColumns.contains(col.name)).toList();

    return Theme(
      data: Theme.of(context).copyWith(
        scrollbarTheme: ScrollbarThemeData(
          thickness: WidgetStateProperty.all(16.0),
          thumbColor: WidgetStateProperty.all(widget.themeColor.withAlpha(180)),
          radius: const Radius.circular(8.0),
          mainAxisMargin: 2.0,
          crossAxisMargin: 2.0,
          thumbVisibility: WidgetStateProperty.all(true),
          trackVisibility: WidgetStateProperty.all(true),
          trackColor: WidgetStateProperty.all(Colors.grey.shade200),
        ),
      ),
      child: Scrollbar(
        thumbVisibility: true,
        trackVisibility: true,
        thickness: 16.0,
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Scrollbar(
            thumbVisibility: true,
            trackVisibility: true,
            thickness: 16.0,
            scrollbarOrientation: ScrollbarOrientation.bottom,
            controller: _horizontalScrollController,
            child: SingleChildScrollView(
              controller: _horizontalScrollController,
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingRowColor: WidgetStateProperty.all(
                  widget.themeColor.withAlpha(25),
                ),
                showCheckboxColumn: true,
                columns: [
                  // Data columns
                  ...visibleColumns.map((column) => DataColumn(
                    label: GestureDetector(
                      onTap: () {
                        // Select column for toolbar operations
                        setState(() {
                          _selectedColumnName = column.name;
                        });
                      },
                      onDoubleTap: () {
                        // Edit column on double tap
                        if (column.name != 'id') {
                          _editSelectedColumn();
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                        decoration: BoxDecoration(
                          border: _selectedColumnName == column.name
                              ? Border(
                                  bottom: BorderSide(
                                    color: widget.themeColor,
                                    width: 2,
                                  ),
                                )
                              : null,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Column label
                            Text(
                              column.label,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: widget.themeColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            // Column type indicator
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  column.type == ColumnType.text ? Icons.text_fields :
                                  column.type == ColumnType.number ? Icons.numbers :
                                  column.type == ColumnType.currency ? Icons.attach_money :
                                  Icons.calendar_today,
                                  size: 12,
                                  color: widget.themeColor.withAlpha(180),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  column.type.toString().split('.').last,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: widget.themeColor.withAlpha(180),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  )),
                ],
                rows: _filteredItems.map((item) => DataRow(
                  selected: _selectedRowIds.contains(item['id']),
                  onSelectChanged: (selected) {
                    setState(() {
                      if (selected == true) {
                        _selectedRowIds.add(item['id'].toString());
                      } else {
                        _selectedRowIds.remove(item['id'].toString());
                      }
                    });
                  },
                  cells: visibleColumns.map((column) => DataCell(
                    Text(
                      column.formatValue(item[column.name]),
                      style: TextStyle(
                        color: column.name == 'id' ? Colors.grey : Colors.black,
                        fontWeight: column.name == 'id' ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    onTap: () {
                      // Don't allow editing the ID column
                      if (column.name == 'id') return;

                      // Show edit dialog
                      _showEditDialog(item['id'], column, item[column.name]);
                    },
                    onLongPress: () {
                      // Show context menu for row operations
                      _showRowContextMenu(item);
                    },
                  )).toList(),
                )).toList(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Show context menu for row operations
  void _showRowContextMenu(Map<String, dynamic> item) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset.zero, ancestor: overlay),
        button.localToGlobal(button.size.bottomRight(Offset.zero), ancestor: overlay),
      ),
      Offset.zero & overlay.size,
    );

    showMenu<String>(
      context: context,
      position: position,
      items: [
        PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, color: widget.themeColor, size: 20),
              const SizedBox(width: 8),
              const Text('Edit Row'),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              const Icon(Icons.delete, color: Colors.red, size: 20),
              const SizedBox(width: 8),
              const Text('Delete Row'),
            ],
          ),
        ),
      ],
      elevation: 8.0,
    ).then((value) {
      if (value == 'edit') {
        // Show edit dialog for the first non-ID field
        final firstEditableColumn = _columns.firstWhere(
          (col) => col.name != 'id',
          orElse: () => _columns.first,
        );
        _showEditDialog(item['id'], firstEditableColumn, item[firstEditableColumn.name]);
      } else if (value == 'delete') {
        _deleteRow(item['id']);
      }
    });
  }
}
