import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Minimal DB Example',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const MinimalDbExample(),
    );
  }
}

class MinimalDbExample extends StatefulWidget {
  const MinimalDbExample({Key? key}) : super(key: key);

  @override
  State<MinimalDbExample> createState() => _MinimalDbExampleState();
}

class _MinimalDbExampleState extends State<MinimalDbExample> {
  Database? _db;
  List<Map<String, dynamic>> _data = [];
  String _status = 'Initializing...';

  @override
  void initState() {
    super.initState();
    _initDb();
  }

  @override
  void dispose() {
    _db?.close();
    super.dispose();
  }

  Future<void> _initDb() async {
    try {
      // Get database path
      final dbPath = await getDatabasesPath();
      final path = join(dbPath, 'minimal.db');

      // Delete existing database for testing
      await deleteDatabase(path);

      // Open database
      _db = await openDatabase(
        path,
        version: 1,
        onCreate: (db, version) async {
          // Create a simple table
          await db.execute('''
            CREATE TABLE test_table (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT,
              value INTEGER
            )
          ''');

          // Insert some test data
          await db.insert('test_table', {'name': 'Item 1', 'value': 100});
          await db.insert('test_table', {'name': 'Item 2', 'value': 200});
        },
      );

      // Load initial data
      await _loadData();

      setState(() {
        _status = 'Database initialized';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _loadData() async {
    try {
      final data = await _db!.query('test_table');
      setState(() {
        _data = data;
      });
    } catch (e) {
      setState(() {
        _status = 'Error loading data: $e';
      });
    }
  }

  Future<void> _addRow() async {
    try {
      await _db!.insert('test_table', {
        'name': 'New Item ${_data.length + 1}',
        'value': (_data.length + 1) * 100,
      });

      await _loadData();
      setState(() {
        _status = 'Row added successfully';
      });
    } catch (e) {
      setState(() {
        _status = 'Error adding row: $e';
      });
    }
  }

  Future<void> _updateRow(int id) async {
    try {
      await _db!.update(
        'test_table',
        {'value': 999},
        where: 'id = ?',
        whereArgs: [id],
      );

      await _loadData();
      setState(() {
        _status = 'Row $id updated successfully';
      });
    } catch (e) {
      setState(() {
        _status = 'Error updating row: $e';
      });
    }
  }

  Future<void> _deleteRow(int id) async {
    try {
      await _db!.delete(
        'test_table',
        where: 'id = ?',
        whereArgs: [id],
      );

      await _loadData();
      setState(() {
        _status = 'Row $id deleted successfully';
      });
    } catch (e) {
      setState(() {
        _status = 'Error deleting row: $e';
      });
    }
  }

  Future<void> _addColumn() async {
    try {
      await _db!.execute('ALTER TABLE test_table ADD COLUMN notes TEXT');

      // Update existing rows with a value for the new column
      for (var row in _data) {
        await _db!.update(
          'test_table',
          {'notes': 'Note for ${row['name']}'},
          where: 'id = ?',
          whereArgs: [row['id']],
        );
      }

      await _loadData();
      setState(() {
        _status = 'Column added successfully';
      });
    } catch (e) {
      setState(() {
        _status = 'Error adding column: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Minimal DB Example'),
      ),
      body: Column(
        children: [
          // Status bar
          Container(
            padding: const EdgeInsets.all(8.0),
            color: Colors.grey[200],
            width: double.infinity,
            child: Text(_status),
          ),

          // Action buttons
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _addRow,
                  child: const Text('Add Row'),
                ),
                ElevatedButton(
                  onPressed: _addColumn,
                  child: const Text('Add Column'),
                ),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('Refresh'),
                ),
              ],
            ),
          ),

          // Data display
          Expanded(
            child: ListView.builder(
              primary: true,
              itemCount: _data.length,
              itemBuilder: (context, index) {
                final item = _data[index];
                return Card(
                  margin: const EdgeInsets.all(8.0),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('ID: ${item['id']}', style: const TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        ...item.entries.where((e) => e.key != 'id').map(
                          (e) => Text('${e.key}: ${e.value}')
                        ).toList(),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: () => _updateRow(item['id']),
                              child: const Text('Update'),
                            ),
                            TextButton(
                              onPressed: () => _deleteRow(item['id']),
                              child: const Text('Delete'),
                              style: TextButton.styleFrom(foregroundColor: Colors.red),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
