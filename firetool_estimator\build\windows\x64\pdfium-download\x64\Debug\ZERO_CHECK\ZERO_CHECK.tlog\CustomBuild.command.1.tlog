^D:\FIRETOOL\FIRETOOL_ESTIMATOR\BUILD\WINDOWS\X64\PDFIUM-DOWNLOAD\CMAKEFILES\A72A7616A0F57B9D9118FF3E8B70A8FC\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Firetool/firetool_estimator/build/windows/x64/pdfium-download -BD:/Firetool/firetool_estimator/build/windows/x64/pdfium-download --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/Firetool/firetool_estimator/build/windows/x64/pdfium-download/pdfium-download.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
