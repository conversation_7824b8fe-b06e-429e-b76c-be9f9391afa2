import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_selector/file_selector.dart';
import 'package:path_provider/path_provider.dart';
import '../models/project.dart';
import '../models/system_catalog.dart';
import 'database_service.dart';

class ExcelService {
  final DatabaseService _databaseService = DatabaseService();

  // Export materials catalog to Excel
  Future<File> exportMaterialsCatalog() async {
    final excel = Excel.createExcel();
    final Sheet sheet = excel['Materials Catalog'];

    // Add headers
    final headers = [
      'Name',
      'Category',
      'Unit',
      'Ex-Works Unit Cost (USD)',
      'Local Unit Cost',
      'Installation Unit Cost',
      'Is Imported'
    ];

    for (var i = 0; i < headers.length; i++) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value = TextCellValue(headers[i]);
    }

    // Get materials
    final materials = await _databaseService.getAllMaterials();

    // Add data
    for (var i = 0; i < materials.length; i++) {
      final material = materials[i];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: i + 1)).value = TextCellValue(material.name);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: i + 1)).value = TextCellValue(material.category);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: i + 1)).value = TextCellValue(material.unit);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: i + 1)).value = DoubleCellValue(material.exWorksUnitCost);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: i + 1)).value = DoubleCellValue(material.localUnitCost);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: i + 1)).value = DoubleCellValue(material.installationUnitCost);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: i + 1)).value = TextCellValue(material.isImported ? 'Yes' : 'No');
    }

    // Save file
    final directory = await getApplicationDocumentsDirectory();
    final path = '${directory.path}/materials_catalog.xlsx';
    final file = File(path);
    await file.writeAsBytes(excel.encode()!);
    return file;
  }

  // Export equipment catalog to Excel
  Future<File> exportEquipmentCatalog() async {
    final excel = Excel.createExcel();
    final Sheet sheet = excel['Equipment Catalog'];

    // Add headers
    final headers = [
      'Name',
      'Category',
      'Ex-Works Unit Cost (USD)',
      'Local Unit Cost',
      'Installation Unit Cost',
      'Is Imported'
    ];

    for (var i = 0; i < headers.length; i++) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value = TextCellValue(headers[i]);
    }

    // Get equipment
    final equipment = await _databaseService.getAllEquipment();

    // Add data
    for (var i = 0; i < equipment.length; i++) {
      final item = equipment[i];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: i + 1)).value = TextCellValue(item.name);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: i + 1)).value = TextCellValue(item.category);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: i + 1)).value = DoubleCellValue(item.exWorksUnitCost);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: i + 1)).value = DoubleCellValue(item.localUnitCost);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: i + 1)).value = DoubleCellValue(item.installationUnitCost);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: i + 1)).value = TextCellValue(item.isImported ? 'Yes' : 'No');
    }

    // Save file
    final directory = await getApplicationDocumentsDirectory();
    final path = '${directory.path}/equipment_catalog.xlsx';
    final file = File(path);
    await file.writeAsBytes(excel.encode()!);
    return file;
  }

  // Export systems catalog to Excel
  Future<File> exportSystemsCatalog() async {
    final excel = Excel.createExcel();
    final Sheet sheet = excel['Systems Catalog'];

    // Add headers
    final headers = [
      'Name',
      'Category',
      'Description',
      'Is Active'
    ];

    for (var i = 0; i < headers.length; i++) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value = TextCellValue(headers[i]);
    }

    // Get systems
    final systems = await _databaseService.getAllSystemCatalog();

    // Add data
    for (var i = 0; i < systems.length; i++) {
      final system = systems[i];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: i + 1)).value = TextCellValue(system.name);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: i + 1)).value = TextCellValue(system.category);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: i + 1)).value = TextCellValue(system.description);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: i + 1)).value = TextCellValue(system.isActive ? 'Yes' : 'No');
    }

    // Save file
    final directory = await getApplicationDocumentsDirectory();
    final path = '${directory.path}/systems_catalog.xlsx';
    final file = File(path);
    await file.writeAsBytes(excel.encode()!);
    return file;
  }

  // Import materials catalog from Excel
  Future<int> importMaterialsCatalog() async {
    try {
      // Pick file
      final typeGroup = XTypeGroup(
        label: 'Excel',
        extensions: ['xlsx'],
      );
      final file = await openFile(acceptedTypeGroups: [typeGroup]);
      
      if (file == null) {
        return 0;
      }

      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);
      final sheet = excel.tables.keys.first;
      final table = excel.tables[sheet];

      if (table == null) {
        return 0;
      }

      int importCount = 0;
      // Skip header row
      for (var i = 1; i < table.maxRows; i++) {
        final row = table.rows[i];
        if (row.isEmpty || row[0]?.value == null) continue;

        final name = row[0]?.value.toString() ?? '';
        final category = row[1]?.value.toString() ?? '';
        final unit = row[2]?.value.toString() ?? '';
        final exWorksUnitCost = _parseDouble(row[3]?.value);
        final localUnitCost = _parseDouble(row[4]?.value);
        final installationUnitCost = _parseDouble(row[5]?.value);
        final isImported = row[6]?.value.toString().toLowerCase() == 'yes'; 

        if (name.isNotEmpty && category.isNotEmpty) {
          final material = MaterialItem(
            name: name,
            category: category,
            quantity: 0,
            unit: unit,
            exWorksUnitCost: exWorksUnitCost,
            localUnitCost: localUnitCost,
            installationUnitCost: installationUnitCost,
            isImported: isImported,
          );

          await _databaseService.insertMaterial(material);
          importCount++;
        }
      }

      return importCount;
    } catch (e) {
      // Use logger instead of print in production
      return 0;
    }
  }

  // Import equipment catalog from Excel
  Future<int> importEquipmentCatalog() async {
    try {
      // Pick file
      final typeGroup = XTypeGroup(
        label: 'Excel',
        extensions: ['xlsx'],
      );
      final file = await openFile(acceptedTypeGroups: [typeGroup]);
      
      if (file == null) {
        return 0;
      }

      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);
      final sheet = excel.tables.keys.first;
      final table = excel.tables[sheet];

      if (table == null) {
        return 0;
      }

      int importCount = 0;
      // Skip header row
      for (var i = 1; i < table.maxRows; i++) {
        final row = table.rows[i];
        if (row.isEmpty || row[0]?.value == null) continue;

        final name = row[0]?.value.toString() ?? '';
        final category = row[1]?.value.toString() ?? '';
        final exWorksUnitCost = _parseDouble(row[2]?.value);
        final localUnitCost = _parseDouble(row[3]?.value);
        final installationUnitCost = _parseDouble(row[4]?.value);
        final isImported = row[5]?.value.toString().toLowerCase() == 'yes'; 

        if (name.isNotEmpty && category.isNotEmpty) {
          final equipment = EquipmentItem(
            name: name,
            category: category,
            quantity: 0,
            exWorksUnitCost: exWorksUnitCost,
            localUnitCost: localUnitCost,
            installationUnitCost: installationUnitCost,
            isImported: isImported,
          );

          await _databaseService.insertEquipment(equipment);
          importCount++;
        }
      }

      return importCount;
    } catch (e) {
      // Use logger instead of print in production
      return 0;
    }
  }

  // Import systems catalog from Excel
  Future<int> importSystemsCatalog() async {
    try {
      // Pick file
      final typeGroup = XTypeGroup(
        label: 'Excel',
        extensions: ['xlsx'],
      );
      final file = await openFile(acceptedTypeGroups: [typeGroup]);
      
      if (file == null) {
        return 0;
      }

      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);
      final sheet = excel.tables.keys.first;
      final table = excel.tables[sheet];

      if (table == null) {
        return 0;
      }

      int importCount = 0;
      // Skip header row
      for (var i = 1; i < table.maxRows; i++) {
        final row = table.rows[i];
        if (row.isEmpty || row[0]?.value == null) continue;

        final name = row[0]?.value.toString() ?? '';
        final category = row[1]?.value.toString() ?? '';
        final description = row[2]?.value.toString() ?? '';
        final isActive = row[3]?.value.toString().toLowerCase() == 'yes';   

        if (name.isNotEmpty) {
          final system = SystemCatalogItem(
            name: name,
            category: category,
            description: description,
            isActive: isActive,
          );

          await _databaseService.insertSystemCatalog(system);
          importCount++;
        }
      }

      return importCount;
    } catch (e) {
      // Use logger instead of print in production
      return 0;
    }
  }

  // Helper method to parse double values
  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }
}
