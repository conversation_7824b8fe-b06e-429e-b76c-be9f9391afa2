import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_selector/file_selector.dart';
import 'package:path_provider/path_provider.dart';
import 'database_service.dart';

class ExcelService {
  final DatabaseService _databaseService = DatabaseService();

  // Export table data to Excel
  Future<File> exportTableToExcel(String tableName) async {
    final excel = Excel.createExcel();
    final Sheet sheet = excel[tableName];

    // Get table data and schema
    final data = await _databaseService.getTableData(tableName);
    final schema = await _databaseService.getTableSchema(tableName);

    if (data.isEmpty || schema == null) {
      // Create empty file
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/${tableName}_export.xlsx';
      final file = File(path);
      await file.writeAsBytes(excel.encode()!);
      return file;
    }

    // Add headers from schema
    final headers = schema.keys.where((key) => !key.startsWith('_')).toList();
    for (var i = 0; i < headers.length; i++) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value = TextCellValue(headers[i]);
    }

    // Add data
    for (var i = 0; i < data.length; i++) {
      final row = data[i];
      for (var j = 0; j < headers.length; j++) {
        final value = row[headers[j]];
        if (value != null) {
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1)).value = TextCellValue(value.toString());
        }
      }
    }

    // Save file
    final directory = await getApplicationDocumentsDirectory();
    final path = '${directory.path}/${tableName}_export.xlsx';
    final file = File(path);
    await file.writeAsBytes(excel.encode()!);
    return file;
  }

  // Import Excel data to table
  Future<int> importExcelToTable(String tableName) async {
    try {
      // Pick file
      final typeGroup = XTypeGroup(
        label: 'Excel',
        extensions: ['xlsx'],
      );
      final file = await openFile(acceptedTypeGroups: [typeGroup]);

      if (file == null) {
        return 0;
      }

      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);
      final sheet = excel.tables.keys.first;
      final table = excel.tables[sheet];

      if (table == null) {
        return 0;
      }

      int importCount = 0;

      // Get headers from first row
      final headerRow = table.rows.first;
      final headers = headerRow.map((cell) => cell?.value.toString() ?? '').toList();

      // Skip header row and import data
      for (var i = 1; i < table.maxRows; i++) {
        final row = table.rows[i];
        if (row.isEmpty) continue;

        final Map<String, dynamic> rowData = {};
        for (var j = 0; j < headers.length && j < row.length; j++) {
          if (headers[j].isNotEmpty && row[j]?.value != null) {
            rowData[headers[j]] = row[j]!.value.toString();
          }
        }

        if (rowData.isNotEmpty) {
          await _databaseService.insertRow(tableName, rowData);
          importCount++;
        }
      }

      return importCount;
    } catch (e) {
      print('Error importing Excel: $e');
      return 0;
    }
  }
}
