import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/project.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';

class AdminMaterialsScreen extends StatefulWidget {
  const AdminMaterialsScreen({super.key});

  @override
  State<AdminMaterialsScreen> createState() => _AdminMaterialsScreenState();
}

class _AdminMaterialsScreenState extends State<AdminMaterialsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<MaterialItem> _materials = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadMaterials();
  }

  Future<void> _loadMaterials() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final materials = await _databaseService.getAllMaterials();
      setState(() {
        _materials = materials;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load materials: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteMaterial(MaterialItem material) async {
    // This would be implemented in a real app
    // await _databaseService.deleteMaterial(material.id);
    // await _loadMaterials();
    
    // For now, just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Delete functionality will be implemented soon'),
      ),
    );
  }

  void _showAddEditMaterialDialog({MaterialItem? material}) {
    final isEditing = material != null;
    final nameController = TextEditingController(text: material?.name ?? '');
    final categoryController = TextEditingController(text: material?.category ?? '');
    final unitController = TextEditingController(text: material?.unit ?? '');
    final exWorksUnitCostController = TextEditingController(
      text: material?.exWorksUnitCost.toString() ?? '0.0',
    );
    final localUnitCostController = TextEditingController(
      text: material?.localUnitCost.toString() ?? '0.0',
    );
    final installationUnitCostController = TextEditingController(
      text: material?.installationUnitCost.toString() ?? '0.0',
    );
    bool isImported = material?.isImported ?? true;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Material' : 'Add Material'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: categoryController,
                decoration: const InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: unitController,
                decoration: const InputDecoration(
                  labelText: 'Unit',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: exWorksUnitCostController,
                decoration: const InputDecoration(
                  labelText: 'Ex-Works Unit Cost (USD)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: localUnitCostController,
                decoration: const InputDecoration(
                  labelText: 'Local Unit Cost',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: installationUnitCostController,
                decoration: const InputDecoration(
                  labelText: 'Installation Unit Cost',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Checkbox(
                    value: isImported,
                    onChanged: (value) {
                      setState(() {
                        isImported = value ?? true;
                      });
                    },
                  ),
                  const Text('Is Imported'),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // This would be implemented in a real app
              // final newMaterial = MaterialItem(
              //   id: material?.id,
              //   name: nameController.text,
              //   category: categoryController.text,
              //   quantity: 0,
              //   unit: unitController.text,
              //   exWorksUnitCost: double.tryParse(exWorksUnitCostController.text) ?? 0.0,
              //   localUnitCost: double.tryParse(localUnitCostController.text) ?? 0.0,
              //   installationUnitCost: double.tryParse(installationUnitCostController.text) ?? 0.0,
              //   isImported: isImported,
              // );
              
              // if (isEditing) {
              //   await _databaseService.updateMaterial(newMaterial);
              // } else {
              //   await _databaseService.insertMaterial(newMaterial);
              // }
              
              // await _loadMaterials();
              
              // For now, just show a message
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Save functionality will be implemented soon'),
                ),
              );
              
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);
    
    if (!authService.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Materials Management'),
        ),
        body: const Center(
          child: Text('You do not have permission to access this page.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Materials Management'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _materials.isEmpty
                  ? const Center(child: Text('No materials found'))
                  : ListView.builder(
                      itemCount: _materials.length,
                      itemBuilder: (context, index) {
                        final material = _materials[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          child: ListTile(
                            title: Text(material.name),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Category: ${material.category}'),
                                Text('Unit: ${material.unit}'),
                                Text('Ex-Works: \$${material.exWorksUnitCost.toStringAsFixed(2)}'),
                                Text('Local: ${material.localUnitCost.toStringAsFixed(2)}'),
                                Text('Installation: ${material.installationUnitCost.toStringAsFixed(2)}'),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  onPressed: () => _showAddEditMaterialDialog(material: material),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete),
                                  onPressed: () => _deleteMaterial(material),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddEditMaterialDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }
}
