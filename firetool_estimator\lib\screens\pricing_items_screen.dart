import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../widgets/pricing_item_card.dart';

class PricingItem {
  final String id;
  final String model;
  final String description;
  double quantity;
  final String manufacturer;
  final String approval;
  final double unitCostUSD;
  final double localCostSAR;
  final double installationCostSAR;
  final String unit;

  PricingItem({
    required this.id,
    required this.model,
    required this.description,
    required this.quantity,
    required this.manufacturer,
    required this.approval,
    required this.unitCostUSD,
    required this.localCostSAR,
    required this.installationCostSAR,
    this.unit = 'pcs',
  });

  // Calculate total unit rate in SAR
  double getTotalUnitRate(double exchangeRate) {
    return (unitCostUSD * exchangeRate) + localCostSAR + installationCostSAR;
  }

  // Calculate total cost
  double getTotalCost(double exchangeRate) {
    return getTotalUnitRate(exchangeRate) * quantity;
  }
}

class PricingItemsScreen extends StatefulWidget {
  const PricingItemsScreen({super.key});

  @override
  State<PricingItemsScreen> createState() => _PricingItemsScreenState();
}

class _PricingItemsScreenState extends State<PricingItemsScreen> {
  final double exchangeRate = 3.75; // USD to SAR
  final List<PricingItem> items = [
    PricingItem(
      id: '1',
      model: 'Fire Alarm Control Panel',
      description: 'Addressable fire alarm control panel with 4 loops capacity, supporting up to 1000 devices',
      quantity: 1,
      manufacturer: 'Honeywell',
      approval: 'UL, FM, NFPA',
      unitCostUSD: 2500.00,
      localCostSAR: 1200.00,
      installationCostSAR: 800.00,
      unit: 'pcs',
    ),
    PricingItem(
      id: '2',
      model: 'Smoke Detector',
      description: 'Addressable photoelectric smoke detector with LED indicator',
      quantity: 45,
      manufacturer: 'Siemens',
      approval: 'UL, FM',
      unitCostUSD: 85.00,
      localCostSAR: 50.00,
      installationCostSAR: 75.00,
      unit: 'pcs',
    ),
    PricingItem(
      id: '3',
      model: 'Manual Call Point',
      description: 'Addressable manual call point with resettable element',
      quantity: 20,
      manufacturer: 'Bosch',
      approval: 'EN54, LPCB',
      unitCostUSD: 65.00,
      localCostSAR: 40.00,
      installationCostSAR: 60.00,
      unit: 'pcs',
    ),
    PricingItem(
      id: '4',
      model: 'FM-200 Cylinder',
      description: '100kg FM-200 cylinder with valve assembly and pressure gauge',
      quantity: 2,
      manufacturer: 'Kidde',
      approval: 'UL, FM, EPA',
      unitCostUSD: 4200.00,
      localCostSAR: 1500.00,
      installationCostSAR: 2000.00,
      unit: 'sets',
    ),
  ];

  void _updateQuantity(String itemId, double newQuantity) {
    setState(() {
      final item = items.firstWhere((item) => item.id == itemId);
      item.quantity = newQuantity;
    });
  }

  void _showEditDialog(PricingItem item) {
    // In a real app, this would open an edit dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Edit ${item.model}')),
    );
  }

  void _showDeleteConfirmation(PricingItem item) {
    // In a real app, this would show a confirmation dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Delete ${item.model}')),
    );
  }

  double get totalCost {
    return items.fold(0, (sum, item) => sum + item.getTotalCost(exchangeRate));
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormatter = NumberFormat("#,##0.00");
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pricing Items'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                return PricingItemCard(
                  model: item.model,
                  description: item.description,
                  quantity: item.quantity,
                  manufacturer: item.manufacturer,
                  approval: item.approval,
                  unitCostUSD: item.unitCostUSD,
                  localCostSAR: item.localCostSAR,
                  installationCostSAR: item.installationCostSAR,
                  exchangeRate: exchangeRate,
                  unit: item.unit,
                  onQuantityChanged: (newQuantity) => _updateQuantity(item.id, newQuantity),
                  onEdit: () => _showEditDialog(item),
                  onDelete: () => _showDeleteConfirmation(item),
                );
              },
            ),
          ),
          
          // Total section at bottom
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Total System Cost:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'SAR ${currencyFormatter.format(totalCost)}',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // In a real app, this would open a dialog to add a new item
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Add new item')),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
