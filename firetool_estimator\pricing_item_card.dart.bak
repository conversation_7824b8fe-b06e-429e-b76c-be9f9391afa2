import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class PricingItemCard extends StatelessWidget {
  final String model;
  final String description;
  final double quantity;
  final String manufacturer;
  final String approval;
  final double unitCostUSD;
  final double localCostSAR;
  final double installationCostSAR;
  final double exchangeRate;
  final String unit;
  final Function(double) onQuantityChanged;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const PricingItemCard({
    Key? key,
    required this.model,
    required this.description,
    required this.quantity,
    required this.manufacturer,
    required this.approval,
    required this.unitCostUSD,
    required this.localCostSAR,
    required this.installationCostSAR,
    this.exchangeRate = 3.75,
    this.unit = 'pcs',
    required this.onQuantityChanged,
    required this.onEdit,
    required this.onDelete,
  }) : super(key: key);

  double get totalUnitRate {
    final calculatedRate = (unitCostUSD * exchangeRate) + localCostSAR + installationCostSAR;
    return calculatedRate.roundToDouble();
  }

  double get totalCost {
    return totalUnitRate * quantity;
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormatter = NumberFormat("#,##0");
    final isSmallScreen = MediaQuery.of(context).size.width < 768;
    // Define primary accent color
    const Color primaryAccentColor = Colors.teal; // Changed from blue

    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16, vertical: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300, width: 1),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: primaryAccentColor.shade400, // Lighter header color
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      model,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white, // White text on new header color
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(width: 8),
                      _buildActionButton(context, Icons.edit, onEdit, 'Edit', primaryAccentColor),
                      const SizedBox(width: 4),
                      _buildActionButton(context, Icons.delete, onDelete, 'Delete', primaryAccentColor, iconColor: Colors.red.shade300),
                    ],
                  ),
                ],
              ),
            ),

            // Content area
            Padding(
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              child: isSmallScreen
                  ? _buildMobileLayout(context, currencyFormatter, primaryAccentColor)
                  : _buildCompactLayout(context, currencyFormatter, primaryAccentColor),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, IconData icon, VoidCallback onPressed, String tooltip, Color headerColor, {Color? iconColor}) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        customBorder: const CircleBorder(),
        child: Container(
          padding: const EdgeInsets.all(6.0),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.20), // Adjusted opacity for new header
            shape: BoxShape.circle,
          ),
          child: Icon(icon, size: 18, color: iconColor ?? Colors.white.withOpacity(0.95)),
        ),
      ),
    );
  }


  Widget _buildMobileLayout(BuildContext context, NumberFormat formatter, Color primaryAccentColor) {
    final String currency = 'SAR';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildSectionTitleWithIcon('Description', context, Icons.article_outlined, primaryAccentColor),
        Text(
          description,
          style: TextStyle(fontSize: 13, color: Colors.grey.shade700, height: 1.4),
          maxLines: 4,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 16),

        Row(
          children: [
            Expanded(child: _buildSectionTitleWithIcon('Manufacturer', context, Icons.factory_outlined, primaryAccentColor, mb: 4)),
            const SizedBox(width: 8),
            Expanded(child: _buildSectionTitleWithIcon('Approval', context, Icons.verified_outlined, primaryAccentColor, mb: 4)),
          ],
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(child: _buildInfoBox(manufacturer, icon: Icons.business_center)),
            const SizedBox(width: 8),
            Expanded(child: _buildInfoBox(approval, icon: Icons.check_circle_outline)),
          ],
        ),
        const SizedBox(height: 16),

        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionTitleWithIcon('Quantity', context, Icons.production_quantity_limits_outlined, primaryAccentColor, mb: 4),
                _buildQuantityControl(context, primaryAccentColor),
              ],
            ),
            const SizedBox(width: 16),
            Expanded( // This Expanded makes the TotalsBlock take remaining width.
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start, // Align content to the start
                children: [
                  _buildSectionTitleWithIcon('Rate & Total', context, Icons.price_change_outlined, primaryAccentColor, mb: 4),
                  _buildTotalsBlock(context, formatter, currency, primaryAccentColor, isSmallScreen: true),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        _buildSectionTitleWithIcon('Cost Breakdown', context, Icons.pie_chart_outline_outlined, primaryAccentColor, mb: 6),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildCostChip('Ex-works: \$${formatter.format(unitCostUSD)}', context, primaryAccentColor, icon: Icons.attach_money),
            _buildCostChip('Local: $currency ${formatter.format(localCostSAR)}', context, primaryAccentColor, icon: Icons.local_shipping_outlined),
            _buildCostChip('Installation: $currency ${formatter.format(installationCostSAR)}', context, primaryAccentColor, icon: Icons.build_circle_outlined),
          ],
        ),
      ],
    );
  }

  Widget _buildCompactLayout(BuildContext context, NumberFormat formatter, Color primaryAccentColor) {
    final String currency = 'SAR';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start, // Default alignment is start
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionTitleWithIcon('Description', context, Icons.article_outlined, primaryAccentColor),
                  Text(
                    description,
                    style: TextStyle(fontSize: 13, color: Colors.grey.shade700, height: 1.4),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  _buildSectionTitleWithIcon('Cost Breakdown', context, Icons.pie_chart_outline_outlined, primaryAccentColor, mb: 6),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildCostChip('Ex-works: \$${formatter.format(unitCostUSD)}', context, primaryAccentColor, icon: Icons.attach_money_outlined),
                      _buildCostChip('Local: $currency ${formatter.format(localCostSAR)}', context, primaryAccentColor, icon: Icons.local_shipping_outlined),
                      _buildCostChip('Installation: $currency ${formatter.format(installationCostSAR)}', context, primaryAccentColor, icon: Icons.build_circle_outlined),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(child: _buildSectionTitleWithIcon('Manufacturer', context, Icons.factory_outlined, primaryAccentColor, mb: 4)),
                      const SizedBox(width: 8),
                      Expanded(child: _buildSectionTitleWithIcon('Approval', context, Icons.verified_outlined, primaryAccentColor, mb: 4)),
                    ],
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child: _buildInfoBox(manufacturer, icon: Icons.business_center)),
                      const SizedBox(width: 8),
                      Expanded(child: _buildInfoBox(approval, icon: Icons.check_circle_outline)),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildSectionTitleWithIcon('Quantity', context, Icons.production_quantity_limits_outlined, primaryAccentColor, mb: 4),
                  _buildQuantityControl(context, primaryAccentColor),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        _buildSectionTitleWithIcon('Estimate Summary', context, Icons.monetization_on_outlined, primaryAccentColor, mb: 6),
        // The TotalsBlock will adhere to the maxWidth specified within it.
        // The parent Column aligns children to the start by default.
        _buildTotalsBlock(context, formatter, currency, primaryAccentColor, isSmallScreen: false),
      ],
    );
  }

  Widget _buildTotalsBlock(BuildContext context, NumberFormat formatter, String currency, Color primaryAccentColor, {required bool isSmallScreen}) {
    // Max width for the totals block
    final double maxTotalsWidth = isSmallScreen ? MediaQuery.of(context).size.width * 0.85 : 350;

    return Container(
      constraints: BoxConstraints(maxWidth: maxTotalsWidth), // Constrain the width
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.grey.shade100, // Lighter grey for the whole block
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade300)
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Unit Rate:', style: TextStyle(fontSize: 13, color: Colors.grey.shade700, fontWeight: FontWeight.w500)),
                Text(
                  '$currency ${formatter.format(totalUnitRate)}',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: primaryAccentColor.shade700), // Use accent color
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: primaryAccentColor.shade600, // Use accent color
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: primaryAccentColor.shade200.withOpacity(0.5), // Use accent color for shadow
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                )
              ]
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('TOTAL COST:', style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold, color: Colors.white)),
                Text(
                  '$currency ${formatter.format(totalCost)}',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildInfoBox(String text, {IconData? icon}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          if (icon != null) Icon(icon, size: 14, color: Colors.grey.shade600),
          if (icon != null) const SizedBox(width: 6),
          Expanded(
            child: Text(
              text,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade800),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitleWithIcon(String title, BuildContext context, IconData icon, Color primaryAccentColor, {double mb = 8.0}) {
    return Padding(
      padding: EdgeInsets.only(bottom: mb),
      child: Row(
        children: [
          Icon(icon, size: 16, color: primaryAccentColor.shade700), // Use accent color
          const SizedBox(width: 6),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityControl(BuildContext context, Color primaryAccentColor) {
    final TextEditingController quantityController = TextEditingController(text: quantity.toStringAsFixed(0));
    quantityController.selection = TextSelection.fromPosition(TextPosition(offset: quantityController.text.length));

    return Container(
      width: 100,
      height: 32,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade400),
        boxShadow: [
            BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 2,
                offset: const Offset(0,1),
            )
        ]
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              if (quantity > 0) {
                onQuantityChanged(quantity - 1);
              }
            },
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(6), bottomLeft: Radius.circular(6)),
            child: Container(
              width: 30,
              alignment: Alignment.center,
              child: Icon(Icons.remove, size: 18, color: primaryAccentColor.shade700), // Use accent color
            ),
          ),
          Container(
            width: 1,
            height: 20,
            color: Colors.grey.shade300,
          ),
          SizedBox(
            width: 36,
            child: TextField(
              controller: quantityController,
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.symmetric(vertical: 8),
                border: InputBorder.none,
                hintText: '0',
              ),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Color(0xFF333333)
              ),
              onChanged: (value) {
                final newQuantity = int.tryParse(value);
                if (newQuantity != null && newQuantity >= 0) {
                  onQuantityChanged(newQuantity.toDouble());
                }
              },
              onSubmitted: (value) {
                 final newQuantity = int.tryParse(value);
                if (newQuantity != null && newQuantity >= 0) {
                  onQuantityChanged(newQuantity.toDouble());
                } else {
                   quantityController.text = quantity.toStringAsFixed(0);
                }
              },
            ),
          ),
           Container(
            width: 1,
            height: 20,
            color: Colors.grey.shade300,
          ),
          InkWell(
            onTap: () {
              onQuantityChanged(quantity + 1);
            },
            borderRadius: const BorderRadius.only(topRight: Radius.circular(6), bottomRight: Radius.circular(6)),
            child: Container(
              width: 30,
              alignment: Alignment.center,
              child: Icon(Icons.add, size: 18, color: primaryAccentColor.shade700), // Use accent color
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostChip(String text, BuildContext context, Color primaryAccentColor, {IconData? icon}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: primaryAccentColor.shade50, // Use accent color's light shade
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: primaryAccentColor.shade200), // Use accent color's lighter shade
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) Icon(icon, size: 14, color: primaryAccentColor.shade700), // Use accent color
          if (icon != null) const SizedBox(width: 5),
          Text(
            text,
            style: TextStyle(fontSize: 11, color: primaryAccentColor.shade800, fontWeight: FontWeight.w500), // Use accent color
          ),
        ],
      ),
    );
  }

  // --- Unchanged methods from previous version (kept for completeness) ---
  // Note: These would also need to accept and use the primaryAccentColor for full theme consistency.
  Widget _buildTabletLayout(BuildContext context, NumberFormat formatter) {
    const Color primaryAccentColor = Colors.teal; // Defaulting to teal for this example
    return LayoutBuilder(
      builder: (context, constraints) {
        final bool useVerticalLayout = constraints.maxWidth < 600;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOldSectionTitle('Description', context),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.grey.shade200),
              ),
              width: double.infinity,
              child: Text(
                description,
                style: const TextStyle(fontSize: 14),
              ),
            ),
            const SizedBox(height: 12),
            if (useVerticalLayout)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildOldSectionTitle('Manufacturer', context),
                  Container( child: Text(manufacturer)),
                  const SizedBox(height: 12),
                  _buildOldSectionTitle('Approval', context),
                  Container( child: Text(approval)),
                  const SizedBox(height: 12),
                  _buildOldSectionTitle('Quantity', context),
                  _buildQuantityControl(context, primaryAccentColor), // Pass accent
                ],
              )
            else
              Row( /* ... Original structure ... */ ),
            const SizedBox(height: 12),
          ],
        );
      },
    );
  }
  Widget _buildOldSectionTitle(String title, BuildContext context, {double mb = 4.0}) {
    return Padding(
      padding: EdgeInsets.only(bottom: mb),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 12, 
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade600, 
        ),
      ),
    );
  }
  Widget _buildCostTable(BuildContext context, NumberFormat formatter) {
    final String currency = 'SAR';
    return Container(
      height: 90,
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row( /* ... */ ),
    );
  }
  Widget _buildTotalsSection(BuildContext context, NumberFormat formatter) {
    final String currency = 'SAR';
    return Container(
      height: 90,
      width: double.infinity,
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration( /* ... */ ),
      child: Column( /* ... */ ),
    );
  }
    Widget _buildCostRow(String label, String value, {double fontSize = 12}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: TextStyle(fontSize: fontSize, color: Colors.grey.shade600)),
        Text(value, style: TextStyle(fontSize: fontSize, fontWeight: FontWeight.bold)),
      ],
    );
  }
}
