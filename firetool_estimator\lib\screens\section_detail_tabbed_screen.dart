import 'package:flutter/material.dart';
import '../models/super_database_models.dart';
import '../services/super_database_service.dart';
import '../services/excel_import_export_service.dart';
import '../services/csv_import_service.dart';
import '../widgets/create_subsection_dialog.dart';
import '../widgets/enhanced_data_grid.dart';
import '../widgets/column_management_dialog.dart';

class SectionDetailTabbedScreen extends StatefulWidget {
  final Section section;

  const SectionDetailTabbedScreen({
    Key? key,
    required this.section,
  }) : super(key: key);

  @override
  State<SectionDetailTabbedScreen> createState() => _SectionDetailTabbedScreenState();
}

class _SectionDetailTabbedScreenState extends State<SectionDetailTabbedScreen>
    with TickerProviderStateMixin {
  final _superDbService = SuperDatabaseService();
  final _excelService = ExcelImportExportService();

  List<Subsection> _subsections = [];
  bool _isLoading = true;
  String? _errorMessage;

  TabController? _tabController;
  final Map<String, List<Map<String, dynamic>>> _subsectionData = {};

  @override
  void initState() {
    super.initState();
    _loadSubsections();
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  Future<void> _loadSubsections() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final subsections = await _superDbService.getSubsections(widget.section.id);

      setState(() {
        _subsections = subsections;
        _isLoading = false;
      });

      // Initialize tab controller
      if (_subsections.isNotEmpty) {
        _tabController?.dispose();
        _tabController = TabController(
          length: _subsections.length,
          vsync: this,
        );

        // Load data for the first tab
        if (_subsections.isNotEmpty) {
          await _loadSubsectionData(_subsections.first);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load subsections: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadSubsectionData(Subsection subsection) async {
    if (_subsectionData.containsKey(subsection.id)) return;

    try {
      final data = await _superDbService.getRows(
        sectionName: widget.section.name,
        tableName: subsection.name,
      );

      setState(() {
        _subsectionData[subsection.id] = data;
      });
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to load data for ${subsection.displayName}: $e');
      }
    }
  }

  Future<void> _createNewSubsection() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => CreateSubsectionDialog(sectionId: widget.section.id),
    );

    if (result != null) {
      try {
        await _superDbService.createSubsection(
          sectionId: widget.section.id,
          name: result['name'],
          displayName: result['displayName'],
          columns: List<ColumnDefinition>.from(result['columns']),
          description: result['description'],
        );

        _showSuccessSnackBar('Subsection "${result['displayName']}" created successfully');
        await _loadSubsections();
      } catch (e) {
        _showErrorSnackBar('Failed to create subsection: $e');
      }
    }
  }

  Future<void> _addRowToCurrentTab() async {
    if (_tabController == null || _subsections.isEmpty) return;

    final currentIndex = _tabController!.index;
    final subsection = _subsections[currentIndex];

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _AddRowDialog(subsection: subsection),
    );

    if (result != null) {
      try {
        await _superDbService.insertRow(
          sectionName: widget.section.name,
          tableName: subsection.name,
          data: result,
        );

        // Refresh the current tab data
        _subsectionData.remove(subsection.id);
        await _loadSubsectionData(subsection);

        _showSuccessSnackBar('Row added successfully');
      } catch (e) {
        _showErrorSnackBar('Failed to add row: $e');
      }
    }
  }

  Future<void> _updateCell(int rowIndex, String columnName, dynamic newValue) async {
    if (_tabController == null || _subsections.isEmpty) return;

    final currentIndex = _tabController!.index;
    final subsection = _subsections[currentIndex];
    final data = _subsectionData[subsection.id] ?? [];

    if (rowIndex < data.length) {
      final row = data[rowIndex];
      final rowId = row['_id'];

      if (rowId != null) {
        try {
          await _superDbService.updateRow(
            sectionName: widget.section.name,
            tableName: subsection.name,
            id: rowId,
            data: {columnName: newValue},
          );

          // Update local data
          setState(() {
            data[rowIndex][columnName] = newValue;
          });

          _showSuccessSnackBar('Cell updated successfully');
        } catch (e) {
          _showErrorSnackBar('Failed to update cell: $e');
        }
      }
    }
  }

  Future<void> _deleteRow(int rowIndex) async {
    if (_tabController == null || _subsections.isEmpty) return;

    final currentIndex = _tabController!.index;
    final subsection = _subsections[currentIndex];
    final data = _subsectionData[subsection.id] ?? [];

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Row'),
        content: const Text('Are you sure you want to delete this row? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && rowIndex < data.length) {
      final row = data[rowIndex];
      final rowId = row['_id'];

      if (rowId != null) {
        try {
          await _superDbService.deleteRow(
            sectionName: widget.section.name,
            tableName: subsection.name,
            id: rowId,
          );

          // Refresh the current tab data
          _subsectionData.remove(subsection.id);
          await _loadSubsectionData(subsection);

          _showSuccessSnackBar('Row deleted successfully');
        } catch (e) {
          _showErrorSnackBar('Failed to delete row: $e');
        }
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  /// Import Excel file to section (merge sheets into existing subsections)
  Future<void> _importExcelToSection() async {
    try {
      _showLoadingDialog('Importing Excel file...');

      final operation = await _excelService.importExcelAsSection(
        sectionDisplayName: '${widget.section.displayName}_Import',
        description: 'Imported data for ${widget.section.displayName}',
      );

      if (mounted) Navigator.of(context).pop(); // Close loading dialog

      if (operation.status == OperationStatus.completed) {
        _showSuccessSnackBar(
          'Successfully imported ${operation.processedRows} rows from Excel file'
        );
        await _loadSubsections();
      } else {
        _showErrorSnackBar(
          'Import failed: ${operation.errorMessage ?? "Unknown error"}'
        );
      }
    } catch (e) {
      if (mounted) Navigator.of(context).pop(); // Close loading dialog
      _showErrorSnackBar('Import failed: $e');
    }
  }

  /// Export section to Excel file
  Future<void> _exportSectionToExcel() async {
    try {
      _showLoadingDialog('Exporting section to Excel...');

      final operation = await _excelService.exportSectionToExcel(
        sectionId: widget.section.id,
      );

      if (mounted) Navigator.of(context).pop(); // Close loading dialog

      if (operation.status == OperationStatus.completed) {
        _showSuccessSnackBar(
          'Successfully exported ${operation.processedRows} rows to Excel file'
        );
      } else {
        _showErrorSnackBar(
          'Export failed: ${operation.errorMessage ?? "Unknown error"}'
        );
      }
    } catch (e) {
      if (mounted) Navigator.of(context).pop(); // Close loading dialog
      _showErrorSnackBar('Export failed: $e');
    }
  }

  /// Manage columns for current subsection
  Future<void> _manageColumns() async {
    if (_tabController == null || _subsections.isEmpty) {
      _showErrorSnackBar('No subsection selected');
      return;
    }

    final currentIndex = _tabController!.index;
    final subsection = _subsections[currentIndex];

    final result = await showDialog<List<ColumnDefinition>>(
      context: context,
      builder: (context) => ColumnManagementDialog(
        subsection: subsection,
        section: widget.section,
      ),
    );

    if (result != null) {
      // Refresh subsections to show updated columns
      await _loadSubsections();
      _showSuccessSnackBar('Columns updated successfully');
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final color = _parseColor(widget.section.color) ?? Theme.of(context).primaryColor;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.section.displayName),
        backgroundColor: color,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.upload_file),
            onPressed: _importExcelToSection,
            tooltip: 'Import Excel to Section',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportSectionToExcel,
            tooltip: 'Export Section to Excel',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addRowToCurrentTab,
            tooltip: 'Add Row',
          ),
          IconButton(
            icon: const Icon(Icons.add_box),
            onPressed: _createNewSubsection,
            tooltip: 'Add Subsection',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _manageColumns,
            tooltip: 'Manage Columns',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSubsections,
            tooltip: 'Refresh',
          ),
        ],
        bottom: _subsections.isNotEmpty && _tabController != null
            ? TabBar(
                controller: _tabController,
                isScrollable: true,
                onTap: (index) {
                  _loadSubsectionData(_subsections[index]);
                },
                tabs: _subsections.map((subsection) => Tab(
                  text: subsection.displayName,
                  icon: const Icon(Icons.table_chart, size: 16),
                )).toList(),
              )
            : null,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading subsections...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red.shade700),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSubsections,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_subsections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.table_chart,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No subsections found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first subsection to get started',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createNewSubsection,
              icon: const Icon(Icons.add),
              label: const Text('Create Subsection'),
            ),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: _subsections.map((subsection) => _buildTabContent(subsection)).toList(),
    );
  }

  Widget _buildTabContent(Subsection subsection) {
    final data = _subsectionData[subsection.id] ?? [];

    if (data.isEmpty && !_subsectionData.containsKey(subsection.id)) {
      // Still loading
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: EnhancedDataGrid(
        data: data,
        columns: subsection.columns,
        onCellEdit: _updateCell,
        onRowDelete: _deleteRow,
        onAddRow: _addRowToCurrentTab,
      ),
    );
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;

    try {
      switch (colorString.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'teal': return Colors.teal;
        case 'amber': return Colors.amber;
        case 'indigo': return Colors.indigo;
        default: return null;
      }
    } catch (e) {
      return null;
    }
  }
}

// Add Row Dialog
class _AddRowDialog extends StatefulWidget {
  final Subsection subsection;

  const _AddRowDialog({required this.subsection});

  @override
  State<_AddRowDialog> createState() => _AddRowDialogState();
}

class _AddRowDialogState extends State<_AddRowDialog> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    for (final column in widget.subsection.columns) {
      if (column.name != '_id') {
        _controllers[column.name] = TextEditingController();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add New Row'),
      content: SizedBox(
        width: 400,
        height: 400,
        child: Form(
          key: _formKey,
          child: ListView(
            children: widget.subsection.columns
                .where((col) => col.name != '_id')
                .map((column) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: TextFormField(
                    controller: _controllers[column.name],
                    decoration: InputDecoration(
                      labelText: column.displayName + (column.isRequired ? ' *' : ''),
                      hintText: _getHintForColumnType(column.type),
                      border: const OutlineInputBorder(),
                    ),
                    validator: column.isRequired ? (value) {
                      if (value == null || value.trim().isEmpty) {
                        return '${column.displayName} is required';
                      }
                      return null;
                    } : null,
                    keyboardType: _getKeyboardTypeForColumn(column.type),
                  ),
                ))
                .toList(),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final data = <String, dynamic>{};
              for (final entry in _controllers.entries) {
                final value = entry.value.text.trim();
                if (value.isNotEmpty) {
                  data[entry.key] = value;
                }
              }
              Navigator.of(context).pop(data);
            }
          },
          child: const Text('Add'),
        ),
      ],
    );
  }

  String _getHintForColumnType(ColumnType type) {
    switch (type) {
      case ColumnType.email: return '<EMAIL>';
      case ColumnType.url: return 'https://example.com';
      case ColumnType.phone: return '+1234567890';
      case ColumnType.date: return 'YYYY-MM-DD';
      case ColumnType.datetime: return 'YYYY-MM-DD HH:MM:SS';
      case ColumnType.currency: return '0.00';
      case ColumnType.boolean: return 'true/false';
      default: return '';
    }
  }

  TextInputType _getKeyboardTypeForColumn(ColumnType type) {
    switch (type) {
      case ColumnType.integer: return TextInputType.number;
      case ColumnType.real:
      case ColumnType.currency: return const TextInputType.numberWithOptions(decimal: true);
      case ColumnType.email: return TextInputType.emailAddress;
      case ColumnType.url: return TextInputType.url;
      case ColumnType.phone: return TextInputType.phone;
      default: return TextInputType.text;
    }
  }
}
