import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart';
import 'package:path/path.dart' as p;
import 'package:uuid/uuid.dart';
import '../models/super_database_models.dart';
import 'super_database_service.dart';

/// Service for handling Excel import/export operations
class ExcelImportExportService {
  static final ExcelImportExportService _instance = ExcelImportExportService._internal();
  factory ExcelImportExportService() => _instance;
  ExcelImportExportService._internal();

  final _superDbService = SuperDatabaseService();
  final _uuid = const Uuid();

  /// Import Excel file and create section with subsections
  Future<ImportExportOperation> importExcelAsSection({
    required String sectionDisplayName,
    String? filePath,
    String? description,
    String? iconName,
    String? color,
    bool replaceExisting = false,
  }) async {
    final operationId = _uuid.v4();
    final startTime = DateTime.now();

    // Pick file if not provided
    String? selectedFilePath = filePath;
    if (selectedFilePath == null) {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        throw Exception('No file selected');
      }

      selectedFilePath = result.files.first.path!;
    }

    final operation = ImportExportOperation(
      id: operationId,
      type: 'import',
      sectionId: '', // Will be set after section creation
      filePath: selectedFilePath,
      startTime: startTime,
      status: OperationStatus.inProgress,
    );

    try {
      // Read Excel file
      final file = File(selectedFilePath);
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      if (excel.sheets.isEmpty) {
        throw Exception('Excel file contains no sheets');
      }

      // Create section
      final sectionName = _sanitizeFileName(sectionDisplayName);
      final section = await _superDbService.createSection(
        name: sectionName,
        displayName: sectionDisplayName,
        description: description,
        iconName: iconName,
        color: color,
      );

      int totalRows = 0;
      int processedRows = 0;

      // Process each sheet as a subsection
      for (final sheetName in excel.sheets.keys) {
        final sheet = excel.sheets[sheetName];
        if (sheet == null || sheet.rows.isEmpty) continue;

        // Extract column definitions from first row
        final headerRow = sheet.rows.first;
        final columns = <ColumnDefinition>[];

        for (int i = 0; i < headerRow.length; i++) {
          final cell = headerRow[i];
          if (cell?.value != null) {
            final columnName = cell!.value.toString().trim();
            if (columnName.isNotEmpty) {
              columns.add(ColumnDefinition(
                name: _sanitizeColumnName(columnName),
                displayName: columnName,
                type: _inferColumnType(sheet, i),
              ));
            }
          }
        }

        if (columns.isEmpty) continue;

        // Create subsection
        final subsection = await _superDbService.createSubsection(
          sectionId: section.id,
          name: _sanitizeFileName(sheetName),
          displayName: sheetName,
          columns: columns,
        );

        // Import data rows
        final dataRows = sheet.rows.skip(1).toList();
        totalRows += dataRows.length;

        final db = await _superDbService.getSectionDatabase(section.name);

        await db.transaction((txn) async {
          for (final row in dataRows) {
            final rowData = <String, dynamic>{};

            for (int i = 0; i < columns.length && i < row.length; i++) {
              final cell = row[i];
              final column = columns[i];

              if (cell?.value != null) {
                rowData[column.name] = _convertCellValue(cell!.value, column.type);
              }
            }

            if (rowData.isNotEmpty) {
              await txn.insert(subsection.name, rowData);
              processedRows++;
            }
          }
        });

        if (kDebugMode) {
          print('Imported ${dataRows.length} rows into ${subsection.displayName}');
        }
      }

      // Update operation status
      final completedOperation = ImportExportOperation(
        id: operationId,
        type: 'import',
        sectionId: section.id,
        filePath: selectedFilePath,
        startTime: startTime,
        endTime: DateTime.now(),
        status: OperationStatus.completed,
        totalRows: totalRows,
        processedRows: processedRows,
      );

      if (kDebugMode) {
        print('Successfully imported Excel file: $selectedFilePath');
        print('Created section: ${section.displayName} with ${excel.sheets.length} subsections');
        print('Total rows processed: $processedRows/$totalRows');
      }

      return completedOperation;

    } catch (e) {
      if (kDebugMode) {
        print('Error importing Excel file: $e');
      }

      return ImportExportOperation(
        id: operationId,
        type: 'import',
        sectionId: '',
        filePath: selectedFilePath,
        startTime: startTime,
        endTime: DateTime.now(),
        status: OperationStatus.failed,
        errorMessage: e.toString(),
      );
    }
  }

  /// Export section to Excel file
  Future<ImportExportOperation> exportSectionToExcel({
    required String sectionId,
    String? filePath,
  }) async {
    final operationId = _uuid.v4();
    final startTime = DateTime.now();

    try {
      final section = await _superDbService.getSection(sectionId);
      if (section == null) {
        throw Exception('Section not found');
      }

      final subsections = await _superDbService.getSubsections(sectionId);
      if (subsections.isEmpty) {
        throw Exception('No subsections found in section');
      }

      // Create Excel workbook
      final excel = Excel.createExcel();
      excel.delete('Sheet1'); // Remove default sheet

      final db = await _superDbService.getSectionDatabase(section.name);
      int totalRows = 0;
      int processedRows = 0;

      for (final subsection in subsections) {
        // Create sheet for subsection
        final sheet = excel[subsection.displayName];

        // Add header row
        for (int i = 0; i < subsection.columns.length; i++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
          cell.value = TextCellValue(subsection.columns[i].displayName);
        }

        // Get data from database
        final rows = await db.query(subsection.name);
        totalRows += rows.length;

        // Add data rows
        for (int rowIndex = 0; rowIndex < rows.length; rowIndex++) {
          final row = rows[rowIndex];

          for (int colIndex = 0; colIndex < subsection.columns.length; colIndex++) {
            final column = subsection.columns[colIndex];
            final value = row[column.name];
            final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex, rowIndex: rowIndex + 1));
            cell.value = TextCellValue(_formatCellValue(value, column.type).toString());
          }

          processedRows++;
        }

        if (kDebugMode) {
          print('Exported ${rows.length} rows from ${subsection.displayName}');
        }
      }

      // Save file
      String outputPath = filePath ?? await _getDefaultExportPath(section.displayName);
      final fileBytes = excel.save();
      if (fileBytes != null) {
        final file = File(outputPath);
        await file.writeAsBytes(fileBytes);
      }

      final completedOperation = ImportExportOperation(
        id: operationId,
        type: 'export',
        sectionId: sectionId,
        filePath: outputPath,
        startTime: startTime,
        endTime: DateTime.now(),
        status: OperationStatus.completed,
        totalRows: totalRows,
        processedRows: processedRows,
      );

      if (kDebugMode) {
        print('Successfully exported section to: $outputPath');
        print('Total rows exported: $processedRows/$totalRows');
      }

      return completedOperation;

    } catch (e) {
      if (kDebugMode) {
        print('Error exporting section to Excel: $e');
      }

      return ImportExportOperation(
        id: operationId,
        type: 'export',
        sectionId: sectionId,
        filePath: filePath ?? '',
        startTime: startTime,
        endTime: DateTime.now(),
        status: OperationStatus.failed,
        errorMessage: e.toString(),
      );
    }
  }

  /// Infer column type from sheet data
  ColumnType _inferColumnType(Sheet sheet, int columnIndex) {
    final sampleSize = 10;
    final dataRows = sheet.rows.skip(1).take(sampleSize);

    int intCount = 0;
    int doubleCount = 0;
    int dateCount = 0;
    int boolCount = 0;
    int totalCount = 0;

    for (final row in dataRows) {
      if (columnIndex < row.length) {
        final cell = row[columnIndex];
        if (cell?.value != null) {
          final value = cell!.value.toString().trim();
          if (value.isNotEmpty) {
            totalCount++;

            if (int.tryParse(value) != null) {
              intCount++;
            } else if (double.tryParse(value) != null) {
              doubleCount++;
            } else if (DateTime.tryParse(value) != null) {
              dateCount++;
            } else if (value.toLowerCase() == 'true' || value.toLowerCase() == 'false') {
              boolCount++;
            }
          }
        }
      }
    }

    if (totalCount == 0) return ColumnType.text;

    final intRatio = intCount / totalCount;
    final doubleRatio = doubleCount / totalCount;
    final dateRatio = dateCount / totalCount;
    final boolRatio = boolCount / totalCount;

    if (boolRatio > 0.8) return ColumnType.boolean;
    if (dateRatio > 0.8) return ColumnType.datetime;
    if (intRatio > 0.8) return ColumnType.integer;
    if (doubleRatio > 0.8) return ColumnType.real;

    return ColumnType.text;
  }

  dynamic _convertCellValue(dynamic value, ColumnType type) {
    if (value == null) return null;

    final stringValue = value.toString().trim();
    if (stringValue.isEmpty) return null;

    switch (type) {
      case ColumnType.integer:
        return int.tryParse(stringValue) ?? 0;
      case ColumnType.real:
      case ColumnType.currency:
        return double.tryParse(stringValue) ?? 0.0;
      case ColumnType.boolean:
        return stringValue.toLowerCase() == 'true' ? 1 : 0;
      case ColumnType.date:
      case ColumnType.datetime:
        final date = DateTime.tryParse(stringValue);
        return date?.toIso8601String() ?? stringValue;
      default:
        return stringValue;
    }
  }

  dynamic _formatCellValue(dynamic value, ColumnType type) {
    if (value == null) return '';

    switch (type) {
      case ColumnType.boolean:
        return (value == 1 || value == true) ? 'TRUE' : 'FALSE';
      case ColumnType.date:
      case ColumnType.datetime:
        if (value is String) {
          final date = DateTime.tryParse(value);
          return date?.toIso8601String() ?? value;
        }
        return value.toString();
      default:
        return value.toString();
    }
  }

  String _sanitizeFileName(String input) {
    return input
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  String _sanitizeColumnName(String input) {
    return input
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  Future<String> _getDefaultExportPath(String sectionName) async {
    // This would typically open a save dialog
    // For now, return a default path
    return p.join(Directory.current.path, '${_sanitizeFileName(sectionName)}_export.xlsx');
  }
}
