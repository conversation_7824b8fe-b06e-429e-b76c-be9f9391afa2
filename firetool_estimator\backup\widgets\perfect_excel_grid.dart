import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart' hide Border, BorderStyle;
import 'dart:io';
import 'dart:async';
import 'dart:math' as math;

class PerfectExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const PerfectExcelGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  });

  @override
  State<PerfectExcelGrid> createState() => _PerfectExcelGridState();
}

class _PerfectExcelGridState extends State<PerfectExcelGrid> {
  // Controllers
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _editingController = TextEditingController();
  final FocusNode _gridFocusNode = FocusNode();
  final FocusNode _editingFocusNode = FocusNode();

  // Data state
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _filteredItems = [];
  List<String> _columns = [];
  final Map<String, double> _columnWidths = {};
  List<double> _rowHeights = [];
  bool _isLoading = true;
  String? _error;
  bool _isCardView = false;

  // Selection state
  int? _selectedRow;
  int? _selectedCol;
  int? _selectionStartRow;
  int? _selectionStartCol;
  int? _selectionEndRow;
  int? _selectionEndCol;
  bool _isSelecting = false;
  bool _isEditing = false;
  final bool _isResizingColumn = false;
  final bool _isResizingRow = false;

  // Column sorting
  String? _sortColumn;
  bool _sortAscending = true;

  // Cell size constants
  final double _defaultColumnWidth = 150.0;
  final double _defaultRowHeight = 40.0;
  final double _headerHeight = 50.0;
  final double _rowHeaderWidth = 60.0;
  final double _resizeHandleWidth = 5.0;

  @override
  void initState() {
    super.initState();

    // Initialize columns with predefined columns if provided
    if (widget.predefinedColumns != null) {
      _columns = List.from(widget.predefinedColumns!);
    } else {
      _columns = [
        'model',
        'description',
        'manufacturer',
        'approval',
        'ex_works_price',
        'local_price',
        'installation_price',
      ];
    }

    // Initialize column widths
    for (var column in _columns) {
      _columnWidths[column] = _defaultColumnWidth;
    }

    _loadData();

    // Add keyboard listener
    _gridFocusNode.addListener(() {
      if (_gridFocusNode.hasFocus) {
        // This ensures the grid can receive keyboard events
      }
    });

    // Add editing focus listener
    _editingFocusNode.addListener(() {
      if (!_editingFocusNode.hasFocus && _isEditing) {
        _saveEdit();
      }
    });
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _searchController.dispose();
    _editingController.dispose();
    _gridFocusNode.dispose();
    _editingFocusNode.dispose();
    super.dispose();
  }

  // Data loading and filtering
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final loadedItems = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Discover all columns from data
      final Set<String> columnSet = {'id'};

      // Add predefined columns
      columnSet.addAll(_columns);

      // Add any additional columns from the data
      for (var item in loadedItems) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Initialize row heights
      _rowHeights = List.generate(loadedItems.length, (_) => _defaultRowHeight);

      setState(() {
        _items = loadedItems;
        _filteredItems = List.from(loadedItems);
        _columns = columnSet.toList();

        // Initialize any new column widths
        for (var column in _columns) {
          if (!_columnWidths.containsKey(column)) {
            _columnWidths[column] = _defaultColumnWidth;
          }
        }

        _isLoading = false;
      });

      // Apply sorting if active
      if (_sortColumn != null) {
        _sortData();
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(_items);
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    setState(() {
      _filteredItems = _items.where((item) {
        // Search in all columns
        for (var column in _columns) {
          final value = item[column]?.toString().toLowerCase() ?? '';
          if (value.contains(lowercaseQuery)) {
            return true;
          }
        }
        return false;
      }).toList();
    });
  }

  void _sortData() {
    if (_sortColumn == null) return;

    setState(() {
      _filteredItems.sort((a, b) {
        final aValue = a[_sortColumn]?.toString() ?? '';
        final bValue = b[_sortColumn]?.toString() ?? '';

        // Try to parse as numbers if possible
        final aNum = double.tryParse(aValue);
        final bNum = double.tryParse(bValue);

        if (aNum != null && bNum != null) {
          return _sortAscending ? aNum.compareTo(bNum) : bNum.compareTo(aNum);
        }

        return _sortAscending ? aValue.compareTo(bValue) : bValue.compareTo(aValue);
      });
    });
  }

  // Cell selection and editing
  void _selectCell(int row, int col) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _selectedRow = row;
      _selectedCol = col;
      _selectionStartRow = row;
      _selectionStartCol = col;
      _selectionEndRow = row;
      _selectionEndCol = col;
      _isSelecting = false;

      // If we're already editing, save the edit first
      if (_isEditing) {
        _saveEdit();
      }
    });

    // Request focus to the grid
    _gridFocusNode.requestFocus();
  }

  void _startSelecting(int row, int col) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _selectedRow = row;
      _selectedCol = col;
      _selectionStartRow = row;
      _selectionStartCol = col;
      _selectionEndRow = row;
      _selectionEndCol = col;
      _isSelecting = true;

      // If we're already editing, save the edit first
      if (_isEditing) {
        _saveEdit();
      }
    });
  }

  void _updateSelection(int row, int col) {
    if (!_isSelecting || _selectionStartRow == null || _selectionStartCol == null) {
      return;
    }

    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _selectionEndRow = row;
      _selectionEndCol = col;
    });
  }

  void _endSelecting() {
    setState(() {
      _isSelecting = false;
    });
  }

  void _startEditing() {
    if (_selectedRow == null || _selectedCol == null) {
      return;
    }

    final row = _selectedRow!;
    final col = _selectedCol!;

    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    final column = _columns[col];
    final value = _filteredItems[row][column]?.toString() ?? '';

    setState(() {
      _isEditing = true;
      _editingController.text = value;
    });

    // Focus and select all text
    _editingFocusNode.requestFocus();
    _editingController.selection = TextSelection(
      baseOffset: 0,
      extentOffset: _editingController.text.length,
    );
  }

  void _startEditingWithCharacter(String char) {
    if (_selectedRow == null || _selectedCol == null) {
      return;
    }

    final row = _selectedRow!;
    final col = _selectedCol!;

    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _isEditing = true;
      _editingController.text = char;
    });

    // Focus and place cursor at end
    _editingFocusNode.requestFocus();
    _editingController.selection = TextSelection.fromPosition(
      TextPosition(offset: _editingController.text.length),
    );
  }

  void _saveEdit() {
    if (!_isEditing || _selectedRow == null || _selectedCol == null) {
      return;
    }

    final row = _selectedRow!;
    final col = _selectedCol!;

    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    final column = _columns[col];
    final itemId = _filteredItems[row]['id'] as String;
    final newValue = _editingController.text;

    // Update in Firestore
    FirebaseFirestore.instance
        .collection(widget.collectionPath)
        .doc(itemId)
        .update({column: newValue})
        .then((_) {
          // Update local data
          setState(() {
            _filteredItems[row][column] = newValue;

            // Also update in _items
            final index = _items.indexWhere((item) => item['id'] == itemId);
            if (index >= 0) {
              _items[index][column] = newValue;
            }

            _isEditing = false;
          });

          // Re-sort if needed
          if (_sortColumn != null) {
            _sortData();
          }
        })
        .catchError((error) {
          _showSnackBar('Error updating cell: $error', isError: true);
          setState(() {
            _isEditing = false;
          });
        });
  }

  void _cancelEdit() {
    setState(() {
      _isEditing = false;
    });
  }

  // Keyboard handling
  void _handleKeyEvent(RawKeyEvent event) {
    if (event is! RawKeyDownEvent) return;

    // If editing, handle editing-specific keys
    if (_isEditing) {
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        _cancelEdit();
      } else if (event.logicalKey == LogicalKeyboardKey.enter ||
                event.logicalKey == LogicalKeyboardKey.tab) {
        _saveEdit();

        // Move to next cell
        if (event.logicalKey == LogicalKeyboardKey.tab) {
          _moveSelection(0, event.isShiftPressed ? -1 : 1);
        } else if (event.logicalKey == LogicalKeyboardKey.enter) {
          _moveSelection(1, 0);
        }
      }
      return;
    }

    // Handle navigation keys
    if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
      _moveSelection(-1, 0);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
      _moveSelection(1, 0);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
      _moveSelection(0, -1);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
      _moveSelection(0, 1);
    } else if (event.logicalKey == LogicalKeyboardKey.tab) {
      _moveSelection(0, event.isShiftPressed ? -1 : 1);
    } else if (event.logicalKey == LogicalKeyboardKey.enter) {
      _moveSelection(1, 0);
    } else if (event.logicalKey == LogicalKeyboardKey.f2 ||
              event.logicalKey == LogicalKeyboardKey.space) {
      _startEditing();
    } else if (event.logicalKey == LogicalKeyboardKey.keyC &&
              event.isControlPressed) {
      _copySelection();
    } else if (event.logicalKey == LogicalKeyboardKey.keyV &&
              event.isControlPressed) {
      _pasteFromClipboard();
    } else if (event.logicalKey == LogicalKeyboardKey.delete ||
              event.logicalKey == LogicalKeyboardKey.backspace) {
      _clearSelection();
    } else if (_isAlphanumeric(event.character)) {
      // Start editing with the pressed key
      _startEditingWithCharacter(event.character ?? '');
    }
  }

  bool _isAlphanumeric(String? char) {
    if (char == null || char.isEmpty) return false;
    return RegExp(r'^[a-zA-Z0-9]$').hasMatch(char);
  }

  void _moveSelection(int rowDelta, int colDelta) {
    if (_selectedRow == null || _selectedCol == null) {
      _selectCell(0, 0);
      return;
    }

    final newRow = math.max(0, math.min(_filteredItems.length - 1, _selectedRow! + rowDelta));
    final newCol = math.max(0, math.min(_columns.length - 1, _selectedCol! + colDelta));

    _selectCell(newRow, newCol);
  }

  // Clipboard operations
  Future<void> _copySelection() async {
    if (_selectionStartRow == null || _selectionStartCol == null ||
        _selectionEndRow == null || _selectionEndCol == null) {
      return;
    }

    final startRow = math.min(_selectionStartRow!, _selectionEndRow!);
    final endRow = math.max(_selectionStartRow!, _selectionEndRow!);
    final startCol = math.min(_selectionStartCol!, _selectionEndCol!);
    final endCol = math.max(_selectionStartCol!, _selectionEndCol!);

    // Build a 2D array of the selected cells
    final List<List<String>> data = [];

    for (int i = startRow; i <= endRow; i++) {
      if (i >= _filteredItems.length) continue;

      final row = <String>[];

      for (int j = startCol; j <= endCol; j++) {
        if (j >= _columns.length) continue;

        final column = _columns[j];
        row.add(_filteredItems[i][column]?.toString() ?? '');
      }

      data.add(row);
    }

    // Convert to tab-separated values
    final clipboard = data.map((row) => row.join('\t')).join('\n');

    // Copy to clipboard
    await Clipboard.setData(ClipboardData(text: clipboard));

    _showSnackBar('Copied to clipboard');
  }

  Future<void> _pasteFromClipboard() async {
    if (_selectedRow == null || _selectedCol == null) {
      _showSnackBar('Please select a cell first', isError: true);
      return;
    }

    final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data == null || data.text == null) {
      _showSnackBar('No data in clipboard', isError: true);
      return;
    }

    // Parse clipboard data
    final rows = data.text!.split('\n');
    if (rows.isEmpty) {
      _showSnackBar('No rows found in clipboard data', isError: true);
      return;
    }

    // Start from the selected cell
    final startRow = _selectedRow!;
    final startCol = _selectedCol!;

    // Prepare batch update
    final batch = FirebaseFirestore.instance.batch();
    int cellsUpdated = 0;

    // Process each row from clipboard
    for (var i = 0; i < rows.length; i++) {
      final rowIndex = startRow + i;
      if (rowIndex >= _filteredItems.length) continue;

      final itemId = _filteredItems[rowIndex]['id'] as String;
      final values = rows[i].split('\t');
      final Map<String, dynamic> updates = {};

      for (var j = 0; j < values.length; j++) {
        final colIndex = startCol + j;
        if (colIndex >= _columns.length) continue;

        final column = _columns[colIndex];
        if (column == 'id') continue;

        updates[column] = values[j];
        cellsUpdated++;
      }

      if (updates.isNotEmpty) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(itemId);

        batch.update(docRef, updates);

        // Update local data
        for (var key in updates.keys) {
          _filteredItems[rowIndex][key] = updates[key];

          // Also update in _items
          final originalIndex = _items.indexWhere((item) => item['id'] == itemId);
          if (originalIndex >= 0) {
            _items[originalIndex][key] = updates[key];
          }
        }
      }
    }

    try {
      await batch.commit();

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }

      _showSnackBar('Pasted $cellsUpdated cells from clipboard');
    } catch (e) {
      _showSnackBar('Error pasting data: $e', isError: true);
    }
  }

  Future<void> _clearSelection() async {
    if (_selectionStartRow == null || _selectionStartCol == null ||
        _selectionEndRow == null || _selectionEndCol == null) {
      return;
    }

    final startRow = math.min(_selectionStartRow!, _selectionEndRow!);
    final endRow = math.max(_selectionStartRow!, _selectionEndRow!);
    final startCol = math.min(_selectionStartCol!, _selectionEndCol!);
    final endCol = math.max(_selectionStartCol!, _selectionEndCol!);

    // Prepare batch update
    final batch = FirebaseFirestore.instance.batch();
    int cellsCleared = 0;

    // Process each selected cell
    for (int i = startRow; i <= endRow; i++) {
      if (i >= _filteredItems.length) continue;

      final itemId = _filteredItems[i]['id'] as String;
      final Map<String, dynamic> updates = {};

      for (int j = startCol; j <= endCol; j++) {
        if (j >= _columns.length) continue;

        final column = _columns[j];
        if (column == 'id') continue;

        updates[column] = '';
        cellsCleared++;
      }

      if (updates.isNotEmpty) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(itemId);

        batch.update(docRef, updates);

        // Update local data
        for (var key in updates.keys) {
          _filteredItems[i][key] = '';

          // Also update in _items
          final originalIndex = _items.indexWhere((item) => item['id'] == itemId);
          if (originalIndex >= 0) {
            _items[originalIndex][key] = '';
          }
        }
      }
    }

    try {
      await batch.commit();

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }

      _showSnackBar('Cleared $cellsCleared cells');
    } catch (e) {
      _showSnackBar('Error clearing cells: $e', isError: true);
    }
  }

  // Import/Export
  Future<void> _importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result == null || result.files.isEmpty) return;

      final file = File(result.files.first.path!);
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      if (excel.tables.isEmpty) {
        _showSnackBar('No data found in Excel file', isError: true);
        return;
      }

      // Use the first sheet
      final sheet = excel.tables.entries.first.value;

      // Get headers from first row
      final headers = <String>[];
      final headerRow = sheet.rows.first;

      for (var cell in headerRow) {
        if (cell?.value != null) {
          headers.add(cell!.value.toString());
        }
      }

      if (headers.isEmpty) {
        _showSnackBar('No headers found in Excel file', isError: true);
        return;
      }

      // Clear existing data if needed
      final clearExisting = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Import Options'),
          content: const Text('Do you want to clear existing data before importing?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('No, Add to Existing'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Yes, Clear Existing'),
            ),
          ],
        ),
      ) ?? false;

      if (clearExisting) {
        // Delete all existing documents
        final batch = FirebaseFirestore.instance.batch();
        for (var item in _items) {
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc(item['id'] as String);
          batch.delete(docRef);
        }
        await batch.commit();

        // Clear local data
        setState(() {
          _items = [];
          _filteredItems = [];
          _rowHeights = [];
        });
      }

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      final newItems = <Map<String, dynamic>>[];
      int count = 0;

      for (var i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final Map<String, dynamic> item = {};

        for (var j = 0; j < row.length && j < headers.length; j++) {
          final cell = row[j];
          if (cell?.value != null) {
            item[headers[j]] = cell!.value.toString();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc();
          batch.set(docRef, item);

          newItems.add({
            'id': docRef.id,
            ...item,
          });

          count++;
        }
      }

      await batch.commit();

      // Update columns to match imported data
      final Set<String> columnSet = {'id'};

      // Add all headers from the Excel file
      columnSet.addAll(headers);

      // Add any additional columns from existing data
      for (var item in _items) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      // Update local data
      setState(() {
        _columns = columnSet.toList();

        // Initialize any new column widths
        for (var column in _columns) {
          if (!_columnWidths.containsKey(column)) {
            _columnWidths[column] = _defaultColumnWidth;
          }
        }

        _items.addAll(newItems);
        _filteredItems.addAll(newItems);

        // Update row heights
        _rowHeights = List.generate(_filteredItems.length, (_) => _defaultRowHeight);
      });

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }

      _showSnackBar('Imported $count items from Excel');
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
    }
  }

  Future<void> _exportToExcel() async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      // Add headers
      for (var i = 0; i < _columns.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value =
            TextCellValue(_columns[i]);
      }

      // Add data
      for (var i = 0; i < _filteredItems.length; i++) {
        final item = _filteredItems[i];

        for (var j = 0; j < _columns.length; j++) {
          final column = _columns[j];
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1)).value =
              TextCellValue(item[column]?.toString() ?? '');
        }
      }

      // Save file
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Excel File',
        fileName: '${widget.title.replaceAll(' ', '_')}_export.xlsx',
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
      );

      if (result != null) {
        final file = File(result);
        await file.writeAsBytes(excel.encode()!);
        _showSnackBar('Data exported to Excel');
      }
    } catch (e) {
      _showSnackBar('Error exporting to Excel: $e', isError: true);
    }
  }

  // Row operations
  Future<void> _addNewRow() async {
    final Map<String, dynamic> newItem = {};

    // Add default empty values for all columns
    for (var column in _columns) {
      if (column != 'id') {
        newItem[column] = '';
      }
    }

    newItem['createdAt'] = FieldValue.serverTimestamp();

    try {
      final docRef = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Add to local data with the new ID
      final newItemWithId = {
        'id': docRef.id,
        ...newItem,
      };

      setState(() {
        _items.add(newItemWithId);
        _filteredItems.add(newItemWithId);
        _rowHeights.add(_defaultRowHeight);
      });

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }

      _showSnackBar('New row added');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  Future<void> _deleteRow(int rowIndex) async {
    if (rowIndex < 0 || rowIndex >= _filteredItems.length) {
      return;
    }

    final itemId = _filteredItems[rowIndex]['id'] as String;

    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(itemId)
          .delete();

      setState(() {
        _filteredItems.removeAt(rowIndex);
        _items.removeWhere((item) => item['id'] == itemId);
        _rowHeights.removeAt(rowIndex);

        // Reset selection if the selected row was deleted
        if (_selectedRow == rowIndex) {
          _selectedRow = null;
          _selectedCol = null;
          _selectionStartRow = null;
          _selectionStartCol = null;
          _selectionEndRow = null;
          _selectionEndCol = null;
        } else if (_selectedRow != null && _selectedRow! > rowIndex) {
          // Adjust selection if a row above was deleted
          _selectedRow = _selectedRow! - 1;
          if (_selectionStartRow != null) _selectionStartRow = _selectionStartRow! - 1;
          if (_selectionEndRow != null) _selectionEndRow = _selectionEndRow! - 1;
        }
      });

      _showSnackBar('Row deleted');
    } catch (e) {
      _showSnackBar('Error deleting row: $e', isError: true);
    }
  }

  // Column operations
  Future<void> _addColumn(String columnName) async {
    if (columnName.isEmpty) {
      _showSnackBar('Column name cannot be empty', isError: true);
      return;
    }

    if (_columns.contains(columnName)) {
      _showSnackBar('Column already exists', isError: true);
      return;
    }

    setState(() {
      _columns.add(columnName);
      _columnWidths[columnName] = _defaultColumnWidth;
    });

    _showSnackBar('Column "$columnName" added');
  }

  Future<void> _deleteColumn(String column) async {
    // Don't allow deleting the ID column
    if (column == 'id') {
      _showSnackBar('Cannot delete ID column', isError: true);
      return;
    }

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Column'),
        content: Text('Are you sure you want to delete the "$column" column? This will remove this data from all items.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // We need to update all documents to remove this field
      final batch = FirebaseFirestore.instance.batch();

      for (var item in _items) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(item['id'] as String);

        // Firebase doesn't have a direct "remove field" operation,
        // so we set it to FieldValue.delete()
        batch.update(docRef, {column: FieldValue.delete()});
      }

      await batch.commit();

      // Update local data
      setState(() {
        _columns.remove(column);
        _columnWidths.remove(column);

        for (var item in _items) {
          item.remove(column);
        }

        for (var item in _filteredItems) {
          item.remove(column);
        }

        // Reset selection if the selected column was deleted
        if (_selectedCol != null && _columns[_selectedCol!] == column) {
          _selectedRow = null;
          _selectedCol = null;
          _selectionStartRow = null;
          _selectionStartCol = null;
          _selectionEndRow = null;
          _selectionEndCol = null;
        }
      });

      _showSnackBar('Column "$column" deleted');
    } catch (e) {
      _showSnackBar('Error deleting column: $e', isError: true);
    }
  }

  // Resize columns and rows
  void _resizeColumn(String column, double newWidth) {
    setState(() {
      _columnWidths[column] = math.max(50.0, newWidth);
    });
  }

  void _resizeRow(int rowIndex, double newHeight) {
    if (rowIndex < 0 || rowIndex >= _rowHeights.length) return;

    setState(() {
      _rowHeights[rowIndex] = math.max(30.0, newHeight);
    });
  }

  // Utility methods
  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  String _getDisplayName(String column) {
    // Convert snake_case to Title Case
    return column.split('_').map((word) =>
      word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  void _showAddColumnDialog() {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Column'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Column Name',
            hintText: 'Enter column name',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final columnName = controller.text.trim();
              if (columnName.isNotEmpty) {
                _addColumn(columnName);
                Navigator.of(context).pop();
              }
            },
            style: TextButton.styleFrom(foregroundColor: widget.themeColor),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildExcelGrid() {
    return Focus(
      focusNode: _gridFocusNode,
      onKeyEvent: (_, event) {
        if (event is KeyDownEvent) {
          // Handle keyboard events
          if (_isEditing) {
            if (event.logicalKey == LogicalKeyboardKey.escape) {
              _cancelEdit();
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.enter ||
                      event.logicalKey == LogicalKeyboardKey.tab) {
              _saveEdit();

              // Move to next cell
              if (event.logicalKey == LogicalKeyboardKey.tab) {
                _moveSelection(0, HardwareKeyboard.instance.isShiftPressed ? -1 : 1);
              } else if (event.logicalKey == LogicalKeyboardKey.enter) {
                _moveSelection(1, 0);
              }
              return KeyEventResult.handled;
            }
          } else {
            // Handle navigation keys
            if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
              _moveSelection(-1, 0);
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
              _moveSelection(1, 0);
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
              _moveSelection(0, -1);
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
              _moveSelection(0, 1);
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.tab) {
              _moveSelection(0, HardwareKeyboard.instance.isShiftPressed ? -1 : 1);
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.enter) {
              _moveSelection(1, 0);
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.f2 ||
                      event.logicalKey == LogicalKeyboardKey.space) {
              _startEditing();
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.keyC &&
                      HardwareKeyboard.instance.isControlPressed) {
              _copySelection();
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.keyV &&
                      HardwareKeyboard.instance.isControlPressed) {
              _pasteFromClipboard();
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.delete ||
                      event.logicalKey == LogicalKeyboardKey.backspace) {
              _clearSelection();
              return KeyEventResult.handled;
            } else if (_isAlphanumeric(event.character)) {
              // Start editing with the pressed key
              _startEditingWithCharacter(event.character ?? '');
              return KeyEventResult.handled;
            }
          }
        }
        return KeyEventResult.ignored;
      },
      child: Stack(
        children: [
          // Excel-like grid
          Scrollbar(
            controller: _verticalController,
            thumbVisibility: true,
            thickness: 16.0, // Thicker scrollbar
            radius: const Radius.circular(8.0),
            child: Scrollbar(
              controller: _horizontalController,
              thumbVisibility: true,
              thickness: 16.0, // Thicker scrollbar
              radius: const Radius.circular(8.0),
              notificationPredicate: (notification) => notification.depth == 1,
              child: SingleChildScrollView(
                controller: _verticalController,
                child: SingleChildScrollView(
                  controller: _horizontalController,
                  scrollDirection: Axis.horizontal,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Column headers
                      Row(
                        children: [
                          // Row header (empty corner)
                          Container(
                            width: _rowHeaderWidth,
                            height: _headerHeight,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              border: Border.all(color: Colors.grey.shade400),
                            ),
                          ),

                          // Column headers
                          ..._columns.asMap().entries.map((entry) {
                            final colIndex = entry.key;
                            final column = entry.value;

                            return _buildColumnHeader(column, colIndex);
                          }),
                        ],
                      ),

                      // Data rows
                      ..._filteredItems.asMap().entries.map((entry) {
                        final rowIndex = entry.key;
                        final item = entry.value;

                        return _buildDataRow(item, rowIndex);
                      }),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Editing overlay
          if (_isEditing && _selectedRow != null && _selectedCol != null)
            Positioned(
              left: _getColumnPosition(_selectedCol!),
              top: _headerHeight + _getRowPosition(_selectedRow!),
              width: _columnWidths[_columns[_selectedCol!]] ?? _defaultColumnWidth,
              height: _rowHeights[_selectedRow!],
              child: TextField(
                controller: _editingController,
                focusNode: _editingFocusNode,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  filled: true,
                  fillColor: Colors.white,
                ),
                onSubmitted: (_) => _saveEdit(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildColumnHeader(String column, int colIndex) {
    final isSorted = _sortColumn == column;

    return GestureDetector(
      onTap: () {
        setState(() {
          if (_sortColumn == column) {
            _sortAscending = !_sortAscending;
          } else {
            _sortColumn = column;
            _sortAscending = true;
          }
          _sortData();
        });
      },
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Stack(
          children: [
            Container(
              width: _columnWidths[column] ?? _defaultColumnWidth,
              height: _headerHeight,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    widget.themeColor.withOpacity(0.7),
                    widget.themeColor.withOpacity(0.5),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                border: Border.all(color: Colors.grey.shade400),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8),
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      _getDisplayName(column),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (isSorted)
                    Icon(
                      _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                      color: Colors.white,
                      size: 16,
                    ),
                  if (column != 'id')
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white, size: 16),
                      onPressed: () => _deleteColumn(column),
                      tooltip: 'Delete Column',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                ],
              ),
            ),
            // Resize handle
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              width: _resizeHandleWidth,
              child: MouseRegion(
                cursor: SystemMouseCursors.resizeLeftRight,
                child: GestureDetector(
                  onHorizontalDragUpdate: (details) {
                    _resizeColumn(column, (_columnWidths[column] ?? _defaultColumnWidth) + details.delta.dx);
                  },
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataRow(Map<String, dynamic> item, int rowIndex) {
    return Stack(
      children: [
        Row(
          children: [
            // Row header (row number and delete button)
            Container(
              width: _rowHeaderWidth,
              height: _rowHeights[rowIndex],
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                border: Border.all(color: Colors.grey.shade400),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 4),
                    child: Text(
                      '${rowIndex + 1}',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                    onPressed: () => _deleteRow(rowIndex),
                    tooltip: 'Delete Row',
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // Data cells
            ..._columns.asMap().entries.map((entry) {
              final colIndex = entry.key;
              final column = entry.value;

              return _buildDataCell(item, column, rowIndex, colIndex);
            }),
          ],
        ),
        // Resize handle
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          height: _resizeHandleWidth,
          child: MouseRegion(
            cursor: SystemMouseCursors.resizeUpDown,
            child: GestureDetector(
              onVerticalDragUpdate: (details) {
                _resizeRow(rowIndex, _rowHeights[rowIndex] + details.delta.dy);
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDataCell(Map<String, dynamic> item, String column, int rowIndex, int colIndex) {
    final isSelected = _selectedRow == rowIndex && _selectedCol == colIndex;
    final isInSelection = _isInSelection(rowIndex, colIndex);

    return GestureDetector(
      onTap: () => _selectCell(rowIndex, colIndex),
      onDoubleTap: _startEditing,
      onPanStart: (_) => _startSelecting(rowIndex, colIndex),
      onPanUpdate: (details) {
        final RenderBox box = context.findRenderObject() as RenderBox;
        final position = box.globalToLocal(details.globalPosition);

        // Calculate which cell we're over
        final cellRowIndex = _getCellRowFromPosition(position.dy);
        final cellColIndex = _getCellColFromPosition(position.dx);

        if (cellRowIndex != null && cellColIndex != null) {
          _updateSelection(cellRowIndex, cellColIndex);
        }
      },
      onPanEnd: (_) => _endSelecting(),
      child: Container(
        width: _columnWidths[column] ?? _defaultColumnWidth,
        height: _rowHeights[rowIndex],
        decoration: BoxDecoration(
          color: isSelected
              ? widget.themeColor.withOpacity(0.2)
              : isInSelection
                  ? widget.themeColor.withOpacity(0.1)
                  : Colors.white,
          border: Border.all(
            color: isSelected || isInSelection
                ? widget.themeColor
                : Colors.grey.shade400,
            width: isSelected ? 2 : 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        alignment: Alignment.centerLeft,
        child: Text(
          item[column]?.toString() ?? '',
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _buildCardView() {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: 400,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        return _buildCard(item, index);
      },
    );
  }

  Widget _buildCard(Map<String, dynamic> item, int index) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: widget.themeColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: widget.themeColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    item['model']?.toString() ?? 'No Model',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  onPressed: () => _deleteRow(index),
                  tooltip: 'Delete Item',
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(12),
              children: [
                // ID
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      const Text(
                        'ID: ',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          item['id']?.toString() ?? '',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),

                // Other fields
                ..._columns.where((col) => col != 'id').map((column) {
                  return _buildCardField(item, column, index);
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardField(Map<String, dynamic> item, String column, int rowIndex) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '${_getDisplayName(column)}:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: () => _showEditDialog(item, column, rowIndex),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  item[column]?.toString() ?? '',
                  style: TextStyle(
                    color: item[column]?.toString().isEmpty ?? true ? Colors.grey : Colors.black,
                    fontStyle: item[column]?.toString().isEmpty ?? true ? FontStyle.italic : FontStyle.normal,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showEditDialog(Map<String, dynamic> item, String column, int rowIndex) {
    final TextEditingController controller = TextEditingController(text: item[column]?.toString() ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${_getDisplayName(column)}'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: _getDisplayName(column),
            border: const OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final itemId = item['id'] as String;
              final newValue = controller.text;

              // Update in Firestore
              FirebaseFirestore.instance
                  .collection(widget.collectionPath)
                  .doc(itemId)
                  .update({column: newValue})
                  .then((_) {
                    // Update local data
                    setState(() {
                      _filteredItems[rowIndex][column] = newValue;

                      // Also update in _items
                      final index = _items.indexWhere((i) => i['id'] == itemId);
                      if (index >= 0) {
                        _items[index][column] = newValue;
                      }
                    });

                    Navigator.of(context).pop();
                    _showSnackBar('Updated successfully');
                  })
                  .catchError((error) {
                    Navigator.of(context).pop();
                    _showSnackBar('Error updating: $error', isError: true);
                  });
            },
            style: TextButton.styleFrom(foregroundColor: widget.themeColor),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // Helper methods for grid positioning
  double _getColumnPosition(int colIndex) {
    double position = _rowHeaderWidth;
    for (int i = 0; i < colIndex; i++) {
      position += _columnWidths[_columns[i]] ?? _defaultColumnWidth;
    }
    return position;
  }

  double _getRowPosition(int rowIndex) {
    double position = 0;
    for (int i = 0; i < rowIndex; i++) {
      position += _rowHeights[i];
    }
    return position;
  }

  int? _getCellRowFromPosition(double y) {
    if (y < _headerHeight) return null;

    double position = _headerHeight;
    for (int i = 0; i < _rowHeights.length; i++) {
      position += _rowHeights[i];
      if (y < position) return i;
    }

    return null;
  }

  int? _getCellColFromPosition(double x) {
    if (x < _rowHeaderWidth) return null;

    double position = _rowHeaderWidth;
    for (int i = 0; i < _columns.length; i++) {
      position += _columnWidths[_columns[i]] ?? _defaultColumnWidth;
      if (x < position) return i;
    }

    return null;
  }

  bool _isInSelection(int rowIndex, int colIndex) {
    if (_selectionStartRow == null || _selectionStartCol == null ||
        _selectionEndRow == null || _selectionEndCol == null) {
      return false;
    }

    final startRow = math.min(_selectionStartRow!, _selectionEndRow!);
    final endRow = math.max(_selectionStartRow!, _selectionEndRow!);
    final startCol = math.min(_selectionStartCol!, _selectionEndCol!);
    final endCol = math.max(_selectionStartCol!, _selectionEndCol!);

    return rowIndex >= startRow && rowIndex <= endRow &&
           colIndex >= startCol && colIndex <= endCol;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        actions: [
          // View toggle
          ToggleButtons(
            isSelected: [!_isCardView, _isCardView],
            onPressed: (index) {
              setState(() {
                _isCardView = index == 1;
              });
            },
            borderRadius: BorderRadius.circular(4),
            children: const [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Icon(Icons.grid_on),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Icon(Icons.view_module),
              ),
            ],
          ),
          const SizedBox(width: 16),

          // Import/Export buttons
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: 'Import Excel',
            onPressed: _importFromExcel,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: 'Export Excel',
            onPressed: _exportToExcel,
          ),

          // Add row/column buttons
          IconButton(
            icon: const Icon(Icons.add_box),
            tooltip: 'Add Row',
            onPressed: _addNewRow,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            tooltip: 'More Options',
            onSelected: (value) {
              if (value == 'add_column') {
                _showAddColumnDialog();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'add_column',
                child: Text('Add Column'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterData('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: _filterData,
            ),
          ),

          // Data grid or card view
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator(color: widget.themeColor))
                : _error != null
                    ? _buildErrorWidget()
                    : _isCardView
                        ? _buildCardView()
                        : _buildExcelGrid(),
          ),
        ],
      ),
    );
  }
}
