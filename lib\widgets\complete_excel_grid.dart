import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/local_sql_service.dart';
import 'dart:math';

class CompleteExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const CompleteExcelGrid({
    Key? key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  }) : super(key: key);

  @override
  State<CompleteExcelGrid> createState() => _CompleteExcelGridState();
}

class _CompleteExcelGridState extends State<CompleteExcelGrid> {
  // Data state
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _filteredItems = [];
  List<String> _columns = [];
  bool _isLoading = true;
  String? _error;

  // SQL Service
  final LocalSqlService _sqlService = LocalSqlService();

  // Search
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Initialize columns with predefined columns if provided
    if (widget.predefinedColumns != null) {
      _columns = List.from(widget.predefinedColumns!);
    } else {
      _columns = [
        'model',
        'description',
        'manufacturer',
        'approval',
        'ex_works_price',
        'local_price',
        'installation_price',
      ];
    }

    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Data loading and filtering
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load data from local SQL database
      final loadedItems = await _sqlService.getItems(widget.collectionPath);

      // Discover all columns from data
      final Set<String> columnSet = {'id'};

      // Add predefined columns
      columnSet.addAll(_columns);

      // Add any additional columns from the data
      for (var item in loadedItems) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('created_at');
      columnSet.remove('updated_at');

      setState(() {
        _items = loadedItems;
        _filteredItems = List.from(loadedItems);
        _columns = columnSet.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
      print('Error loading data: $e'); // Log the error
    }
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(_items);
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    setState(() {
      _filteredItems = _items.where((item) {
        // Search in all columns
        for (var column in _columns) {
          final value = item[column]?.toString().toLowerCase() ?? '';
          if (value.contains(lowercaseQuery)) {
            return true;
          }
        }
        return false;
      }).toList();
    });
  }

  // CRUD operations
  Future<void> _updateCell(String itemId, String column, dynamic value) async {
    try {
      // Don't update ID column
      if (column == 'id') return;

      // Update in local SQL database
      await _sqlService.updateField(widget.collectionPath, itemId, column, value);

      // Update local data
      setState(() {
        final itemIndex = _items.indexWhere((item) => item['id'] == itemId);
        if (itemIndex != -1) {
          _items[itemIndex][column] = value;
          _filteredItems = _filterData(_searchController.text);
        }
      });
      
      _showSnackBar('Cell updated successfully');
    } catch (e) {
      _showSnackBar('Error updating cell: $e', isError: true);
    }
  }

  Future<void> _addNewRow() async {
    try {
      // Create an empty row with default values
      final Map<String, dynamic> newItem = {};

      // Add default empty values for each column
      for (var column in _columns) {
        if (column != 'id') {
          newItem[column] = '';
        }
      }

      // Add to local SQL database
      final newId = await _sqlService.addItem(widget.collectionPath, newItem);

      // Add to local data
      setState(() {
        _items.add({
          'id': newId,
          ...newItem,
        });
        _filteredItems = _filterData(_searchController.text);
      });

      _showSnackBar('Row added successfully');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  Future<void> _deleteRow(String itemId) async {
    try {
      // Delete from local SQL database
      await _sqlService.deleteItem(widget.collectionPath, itemId);

      // Delete from local data
      setState(() {
        _items.removeWhere((item) => item['id'] == itemId);
        _filteredItems = _filterData(_searchController.text);
      });

      _showSnackBar('Row deleted successfully');
    } catch (e) {
      _showSnackBar('Error deleting row: $e', isError: true);
    }
  }

  // Import/Export methods
  Future<void> _importFromExcel() async {
    try {
      _showSnackBar('Excel import functionality will be implemented later');
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
    }
  }

  Future<void> _exportToExcel() async {
    try {
      _showSnackBar('Excel export functionality will be implemented later');
    } catch (e) {
      _showSnackBar('Error exporting to Excel: $e', isError: true);
    }
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final text = clipboardData?.text;

      if (text == null || text.isEmpty) {
        _showSnackBar('Clipboard is empty', isError: true);
        return;
      }

      _showSnackBar('Clipboard paste functionality will be implemented later');
    } catch (e) {
      _showSnackBar('Error pasting from clipboard: $e', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addNewRow,
            tooltip: 'Add Row',
          ),
          IconButton(
            icon: const Icon(Icons.file_upload),
            onPressed: _importFromExcel,
            tooltip: 'Import from Excel',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportToExcel,
            tooltip: 'Export to Excel',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildToolbar(),
          Expanded(
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: widget.themeColor,
                    ),
                  )
                : _error != null
                    ? _buildErrorWidget()
                    : _buildDataTable(),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    labelText: 'Search',
                    prefixIcon: const Icon(Icons.search),
                    border: const OutlineInputBorder(),
                    contentPadding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                  ),
                  onChanged: _filterData,
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.content_paste),
                label: const Text('Paste'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _pasteFromClipboard,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'Local SQL Database',
                style: TextStyle(
                  color: widget.themeColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${_filteredItems.length} items',
                style: TextStyle(
                  color: widget.themeColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildDataTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          headingRowColor: MaterialStateProperty.all(
            Color.fromRGBO(
              widget.themeColor.red,
              widget.themeColor.green,
              widget.themeColor.blue,
              0.1,
            ),
          ),
          columns: _columns.map((column) => DataColumn(
            label: Text(
              column,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: widget.themeColor,
              ),
            ),
          )).toList(),
          rows: _filteredItems.map((item) => DataRow(
            cells: _columns.map((column) => DataCell(
              Text(item[column]?.toString() ?? ''),
              onTap: () {
                // Show edit dialog
                _showEditDialog(item['id'], column, item[column]);
              },
            )).toList(),
          )).toList(),
        ),
      ),
    );
  }

  void _showEditDialog(String itemId, String column, dynamic currentValue) {
    final TextEditingController controller = TextEditingController(
      text: currentValue?.toString() ?? '',
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit $column'),
        content: TextField(
          controller: controller,
          autofocus: true,
          decoration: InputDecoration(
            labelText: column,
            border: const OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _updateCell(itemId, column, controller.text);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
