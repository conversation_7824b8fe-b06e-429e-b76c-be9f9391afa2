import 'package:flutter/foundation.dart';
import '../models/super_database_models.dart';

/// Service for validating data during import and editing operations
class DataValidationService {
  static final DataValidationService _instance = DataValidationService._internal();
  factory DataValidationService() => _instance;
  DataValidationService._internal();

  /// Validate a single cell value against column definition
  ValidationResult validateCellValue(dynamic value, ColumnDefinition column) {
    final errors = <String>[];
    final warnings = <String>[];

    // Check required fields
    if (column.isRequired && (value == null || value.toString().trim().isEmpty)) {
      errors.add('${column.displayName} is required');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    // Skip validation for null/empty optional fields
    if (value == null || value.toString().trim().isEmpty) {
      return ValidationResult(isValid: true, errors: errors, warnings: warnings);
    }

    final stringValue = value.toString().trim();

    // Type-specific validation
    switch (column.type) {
      case ColumnType.integer:
        if (int.tryParse(stringValue) == null) {
          errors.add('${column.displayName} must be a valid integer');
        }
        break;

      case ColumnType.real:
      case ColumnType.currency:
        if (double.tryParse(stringValue) == null) {
          errors.add('${column.displayName} must be a valid number');
        }
        break;

      case ColumnType.boolean:
        final validBooleans = ['true', 'false', 'yes', 'no', '1', '0'];
        if (!validBooleans.contains(stringValue.toLowerCase())) {
          errors.add('${column.displayName} must be true/false or yes/no');
        }
        break;

      case ColumnType.email:
        if (!_isValidEmail(stringValue)) {
          errors.add('${column.displayName} must be a valid email address');
        }
        break;

      case ColumnType.url:
        if (!_isValidUrl(stringValue)) {
          errors.add('${column.displayName} must be a valid URL');
        }
        break;

      case ColumnType.phone:
        if (!_isValidPhone(stringValue)) {
          warnings.add('${column.displayName} may not be a valid phone number');
        }
        break;

      case ColumnType.date:
      case ColumnType.datetime:
        if (DateTime.tryParse(stringValue) == null) {
          errors.add('${column.displayName} must be a valid date');
        }
        break;

      case ColumnType.json:
        try {
          // Try to parse as JSON
          if (stringValue.startsWith('{') || stringValue.startsWith('[')) {
            // Basic JSON validation - could be enhanced
            if (!_isValidJson(stringValue)) {
              errors.add('${column.displayName} must be valid JSON');
            }
          }
        } catch (e) {
          errors.add('${column.displayName} must be valid JSON');
        }
        break;

      case ColumnType.text:
        // Text validation (length, patterns, etc.)
        if (stringValue.length > 10000) {
          warnings.add('${column.displayName} is very long (${stringValue.length} characters)');
        }
        break;
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate an entire row against column definitions
  RowValidationResult validateRow(Map<String, dynamic> row, List<ColumnDefinition> columns) {
    final cellResults = <String, ValidationResult>{};
    final allErrors = <String>[];
    final allWarnings = <String>[];

    for (final column in columns) {
      if (column.name == '_id') continue; // Skip auto-increment ID

      final value = row[column.name];
      final result = validateCellValue(value, column);
      
      cellResults[column.name] = result;
      allErrors.addAll(result.errors);
      allWarnings.addAll(result.warnings);
    }

    // Check for unique constraints
    // Note: This would need database access to check uniqueness properly
    // For now, we'll just validate the format

    return RowValidationResult(
      isValid: allErrors.isEmpty,
      errors: allErrors,
      warnings: allWarnings,
      cellResults: cellResults,
    );
  }

  /// Validate multiple rows for batch operations
  BatchValidationResult validateBatch(
    List<Map<String, dynamic>> rows,
    List<ColumnDefinition> columns,
  ) {
    final rowResults = <int, RowValidationResult>{};
    final allErrors = <String>[];
    final allWarnings = <String>[];
    int validRows = 0;

    for (int i = 0; i < rows.length; i++) {
      final result = validateRow(rows[i], columns);
      rowResults[i] = result;
      
      if (result.isValid) {
        validRows++;
      } else {
        allErrors.addAll(result.errors.map((e) => 'Row ${i + 1}: $e'));
      }
      
      allWarnings.addAll(result.warnings.map((w) => 'Row ${i + 1}: $w'));
    }

    return BatchValidationResult(
      isValid: allErrors.isEmpty,
      errors: allErrors,
      warnings: allWarnings,
      rowResults: rowResults,
      totalRows: rows.length,
      validRows: validRows,
      invalidRows: rows.length - validRows,
    );
  }

  /// Suggest data type for a column based on sample values
  ColumnType suggestColumnType(List<dynamic> sampleValues) {
    if (sampleValues.isEmpty) return ColumnType.text;

    final nonNullValues = sampleValues
        .where((v) => v != null && v.toString().trim().isNotEmpty)
        .map((v) => v.toString().trim())
        .toList();

    if (nonNullValues.isEmpty) return ColumnType.text;

    int intCount = 0;
    int doubleCount = 0;
    int boolCount = 0;
    int emailCount = 0;
    int urlCount = 0;
    int dateCount = 0;

    for (final value in nonNullValues) {
      if (int.tryParse(value) != null) {
        intCount++;
      } else if (double.tryParse(value) != null) {
        doubleCount++;
      } else if (_isBooleanValue(value)) {
        boolCount++;
      } else if (_isValidEmail(value)) {
        emailCount++;
      } else if (_isValidUrl(value)) {
        urlCount++;
      } else if (DateTime.tryParse(value) != null) {
        dateCount++;
      }
    }

    final total = nonNullValues.length;
    const threshold = 0.8; // 80% of values must match type

    if (boolCount / total >= threshold) return ColumnType.boolean;
    if (emailCount / total >= threshold) return ColumnType.email;
    if (urlCount / total >= threshold) return ColumnType.url;
    if (dateCount / total >= threshold) return ColumnType.datetime;
    if (intCount / total >= threshold) return ColumnType.integer;
    if (doubleCount / total >= threshold) return ColumnType.real;

    return ColumnType.text;
  }

  /// Clean and normalize data value
  dynamic cleanValue(dynamic value, ColumnType type) {
    if (value == null) return null;
    
    final stringValue = value.toString().trim();
    if (stringValue.isEmpty) return null;

    switch (type) {
      case ColumnType.integer:
        return int.tryParse(stringValue) ?? 0;
      
      case ColumnType.real:
      case ColumnType.currency:
        return double.tryParse(stringValue) ?? 0.0;
      
      case ColumnType.boolean:
        final lower = stringValue.toLowerCase();
        return (lower == 'true' || lower == 'yes' || lower == '1') ? 1 : 0;
      
      case ColumnType.email:
        return stringValue.toLowerCase();
      
      case ColumnType.date:
      case ColumnType.datetime:
        final date = DateTime.tryParse(stringValue);
        return date?.toIso8601String() ?? stringValue;
      
      default:
        return stringValue;
    }
  }

  // Private validation methods
  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(email);
  }

  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  bool _isValidPhone(String phone) {
    // Basic phone validation - could be enhanced
    final cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');
    return cleaned.length >= 7 && cleaned.length <= 15;
  }

  bool _isValidJson(String json) {
    try {
      // This is a very basic check - could use a proper JSON parser
      final trimmed = json.trim();
      return (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
             (trimmed.startsWith('[') && trimmed.endsWith(']'));
    } catch (e) {
      return false;
    }
  }

  bool _isBooleanValue(String value) {
    final lower = value.toLowerCase();
    return ['true', 'false', 'yes', 'no', '1', '0'].contains(lower);
  }
}

/// Result of validating a single cell
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  @override
  String toString() {
    return 'ValidationResult(valid: $isValid, errors: ${errors.length}, warnings: ${warnings.length})';
  }
}

/// Result of validating a single row
class RowValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final Map<String, ValidationResult> cellResults;

  RowValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.cellResults,
  });

  @override
  String toString() {
    return 'RowValidationResult(valid: $isValid, errors: ${errors.length}, warnings: ${warnings.length})';
  }
}

/// Result of validating multiple rows
class BatchValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final Map<int, RowValidationResult> rowResults;
  final int totalRows;
  final int validRows;
  final int invalidRows;

  BatchValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.rowResults,
    required this.totalRows,
    required this.validRows,
    required this.invalidRows,
  });

  double get validationRate => totalRows > 0 ? validRows / totalRows : 0.0;

  @override
  String toString() {
    return 'BatchValidationResult(valid: $isValid, total: $totalRows, valid: $validRows, invalid: $invalidRows)';
  }
}
