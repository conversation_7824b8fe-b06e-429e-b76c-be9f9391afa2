import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class PricingItemCard extends StatelessWidget {
  final String model;
  final String description;
  final double quantity;
  final String manufacturer;
  final String approval;
  final double unitCostUSD;
  final double localCostSAR;
  final double installationCostSAR;
  final double exchangeRate;
  final String unit;
  final Function(double) onQuantityChanged;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const PricingItemCard({
    super.key,
    required this.model,
    required this.description,
    required this.quantity,
    required this.manufacturer,
    required this.approval,
    required this.unitCostUSD,
    required this.localCostSAR,
    required this.installationCostSAR,
    this.exchangeRate = 3.75,
    this.unit = 'pcs',
    required this.onQuantityChanged,
    required this.onEdit,
    required this.onDelete,
  });

  double get totalUnitRate {
    final calculatedRate = (unitCostUSD * exchangeRate) + localCostSAR + installationCostSAR;
    return calculatedRate.roundToDouble();
  }

  double get totalCost {
    return totalUnitRate * quantity;
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormatter = NumberFormat("#,##0");
    final isSmallScreen = MediaQuery.of(context).size.width < 768;
    // Define a neutral accent color for totals, can be adjusted
    final Color totalsAccentColor = Colors.blueGrey;


    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16, vertical: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300, width: 1),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white, // Main card background
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100, // Light grey header background
                border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      model,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700, // Bluish text for item name
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(width: 8),
                      _buildActionButton(context, Icons.edit, onEdit, 'Edit'),
                      const SizedBox(width: 4),
                      _buildActionButton(context, Icons.delete, onDelete, 'Delete', iconColor: Colors.red.shade400),
                    ],
                  ),
                ],
              ),
            ),

            // Content area
            Padding(
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              child: isSmallScreen
                  ? _buildMobileLayout(context, currencyFormatter, totalsAccentColor)
                  : _buildCompactLayout(context, currencyFormatter, totalsAccentColor),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, IconData icon, VoidCallback onPressed, String tooltip, {Color? iconColor}) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        customBorder: const CircleBorder(),
        child: Container(
          padding: const EdgeInsets.all(6.0),
          decoration: BoxDecoration(
            color: Colors.grey.shade200.withOpacity(0.7), // Subtle background for buttons on grey header
            shape: BoxShape.circle,
          ),
          child: Icon(icon, size: 18, color: iconColor ?? Colors.grey.shade700), // Darker grey icons
        ),
      ),
    );
  }


  Widget _buildMobileLayout(BuildContext context, NumberFormat formatter, Color totalsAccentColor) {
    final String currency = 'SAR';
    final reducedSpacing = const SizedBox(height: 12); // Reduced from 16

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildSectionTitleWithIcon('Description', context, Icons.article_outlined),
        Text(
          description,
          style: TextStyle(fontSize: 13, color: Colors.grey.shade700, height: 1.4),
          maxLines: 4,
          overflow: TextOverflow.ellipsis,
        ),
        reducedSpacing,

        Row(
          children: [
            Expanded(child: _buildSectionTitleWithIcon('Manufacturer', context, Icons.factory_outlined, mb: 4)),
            const SizedBox(width: 8),
            Expanded(child: _buildSectionTitleWithIcon('Approval', context, Icons.verified_outlined, mb: 4)),
          ],
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(child: _buildInfoBox(manufacturer, icon: Icons.business_center)),
            const SizedBox(width: 8),
            Expanded(child: _buildInfoBox(approval, icon: Icons.check_circle_outline)),
          ],
        ),
        reducedSpacing,

        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionTitleWithIcon('Quantity', context, Icons.production_quantity_limits_outlined, mb: 4),
                _buildQuantityControl(context),
              ],
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionTitleWithIcon('Rate & Total', context, Icons.price_change_outlined, mb: 4),
                  _buildTotalsBlock(context, formatter, currency, totalsAccentColor, isSmallScreen: true),
                ],
              ),
            ),
          ],
        ),
        reducedSpacing,

        _buildSectionTitleWithIcon('Cost Breakdown', context, Icons.pie_chart_outline_outlined, mb: 6),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildCostChip('Ex-works: \$${formatter.format(unitCostUSD)}', context, icon: Icons.attach_money),
            _buildCostChip('Local: $currency ${formatter.format(localCostSAR)}', context, icon: Icons.local_shipping_outlined),
            _buildCostChip('Installation: $currency ${formatter.format(installationCostSAR)}', context, icon: Icons.build_circle_outlined),
          ],
        ),
      ],
    );
  }

  Widget _buildCompactLayout(BuildContext context, NumberFormat formatter, Color totalsAccentColor) {
    final String currency = 'SAR';
    final reducedSpacing = const SizedBox(height: 12); // Reduced from 16/20
    final reducedSpacingBetweenMainSections = const SizedBox(height: 16); // Reduced from 20

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionTitleWithIcon('Description', context, Icons.article_outlined),
                  Text(
                    description,
                    style: TextStyle(fontSize: 13, color: Colors.grey.shade700, height: 1.4),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  reducedSpacing,
                  _buildSectionTitleWithIcon('Cost Breakdown', context, Icons.pie_chart_outline_outlined, mb: 6),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildCostChip('Ex-works: \$${formatter.format(unitCostUSD)}', context, icon: Icons.attach_money_outlined),
                      _buildCostChip('Local: $currency ${formatter.format(localCostSAR)}', context, icon: Icons.local_shipping_outlined),
                      _buildCostChip('Installation: $currency ${formatter.format(installationCostSAR)}', context, icon: Icons.build_circle_outlined),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 20), // Keep horizontal spacing for visual separation
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(child: _buildSectionTitleWithIcon('Manufacturer', context, Icons.factory_outlined, mb: 4)),
                      const SizedBox(width: 8),
                      Expanded(child: _buildSectionTitleWithIcon('Approval', context, Icons.verified_outlined, mb: 4)),
                    ],
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child: _buildInfoBox(manufacturer, icon: Icons.business_center)),
                      const SizedBox(width: 8),
                      Expanded(child: _buildInfoBox(approval, icon: Icons.check_circle_outline)),
                    ],
                  ),
                  reducedSpacing,
                  _buildSectionTitleWithIcon('Quantity', context, Icons.production_quantity_limits_outlined, mb: 4),
                  _buildQuantityControl(context),
                ],
              ),
            ),
          ],
        ),
        reducedSpacingBetweenMainSections,
        _buildSectionTitleWithIcon('Estimate Summary', context, Icons.monetization_on_outlined, mb: 6),
        _buildTotalsBlock(context, formatter, currency, totalsAccentColor, isSmallScreen: false),
      ],
    );
  }

  Widget _buildTotalsBlock(BuildContext context, NumberFormat formatter, String currency, Color totalsAccentColor, {required bool isSmallScreen}) {
    final double maxTotalsWidth = isSmallScreen ? MediaQuery.of(context).size.width * 0.85 : 350;

    return Container(
      constraints: BoxConstraints(maxWidth: maxTotalsWidth),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade300)
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Unit Rate:', style: TextStyle(fontSize: 13, color: Colors.grey.shade700, fontWeight: FontWeight.w500)),
                Text(
                  '$currency ${formatter.format(totalUnitRate)}',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.blue.shade700),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.blue.shade600,
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.shade200.withAlpha(128),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                )
              ]
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('TOTAL COST:', style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold, color: Colors.white)),
                Text(
                  '$currency ${formatter.format(totalCost)}',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildInfoBox(String text, {IconData? icon}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          if (icon != null) Icon(icon, size: 14, color: Colors.grey.shade600),
          if (icon != null) const SizedBox(width: 6),
          Expanded(
            child: Text(
              text,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade800),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitleWithIcon(String title, BuildContext context, IconData icon, {double mb = 8.0}) {
    // Using a more neutral color for section icons, e.g., a darker grey
    final Color iconColor = Colors.blueGrey.shade600;
    return Padding(
      padding: EdgeInsets.only(bottom: mb),
      child: Row(
        children: [
          Icon(icon, size: 16, color: iconColor),
          const SizedBox(width: 6),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityControl(BuildContext context) {
    final TextEditingController quantityController = TextEditingController(text: quantity.toStringAsFixed(0));
    quantityController.selection = TextSelection.fromPosition(TextPosition(offset: quantityController.text.length));
    final Color iconColor = Colors.blueGrey.shade700; // Neutral color for quantity icons

    return Container(
      width: 100,
      height: 32,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade400),
        boxShadow: [
            BoxShadow(
                color: Colors.grey.withAlpha(25),
                spreadRadius: 1,
                blurRadius: 2,
                offset: const Offset(0,1),
            )
        ]
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              if (quantity > 0) {
                onQuantityChanged(quantity - 1);
              }
            },
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(6), bottomLeft: Radius.circular(6)),
            child: Container(
              width: 30,
              alignment: Alignment.center,
              child: Icon(Icons.remove, size: 18, color: iconColor),
            ),
          ),
          Container(
            width: 1,
            height: 20,
            color: Colors.grey.shade300,
          ),
          SizedBox(
            width: 36,
            child: TextField(
              controller: quantityController,
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.symmetric(vertical: 8),
                border: InputBorder.none,
                hintText: '0',
              ),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Color(0xFF333333)
              ),
              onChanged: (value) {
                final newQuantity = int.tryParse(value);
                if (newQuantity != null && newQuantity >= 0) {
                  onQuantityChanged(newQuantity.toDouble());
                }
              },
              onSubmitted: (value) {
                 final newQuantity = int.tryParse(value);
                if (newQuantity != null && newQuantity >= 0) {
                  onQuantityChanged(newQuantity.toDouble());
                } else {
                   quantityController.text = quantity.toStringAsFixed(0);
                }
              },
            ),
          ),
           Container(
            width: 1,
            height: 20,
            color: Colors.grey.shade300,
          ),
          InkWell(
            onTap: () {
              onQuantityChanged(quantity + 1);
            },
            borderRadius: const BorderRadius.only(topRight: Radius.circular(6), bottomRight: Radius.circular(6)),
            child: Container(
              width: 30,
              alignment: Alignment.center,
              child: Icon(Icons.add, size: 18, color: iconColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostChip(String text, BuildContext context, {IconData? icon}) {
    // Grey background for cost chips
    final Color chipBackgroundColor = Colors.grey.shade200;
    final Color chipIconColor = Colors.grey.shade700;
    final Color chipTextColor = Colors.black87;


    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: chipBackgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) Icon(icon, size: 14, color: chipIconColor),
          if (icon != null) const SizedBox(width: 5),
          Text(
            text,
            style: TextStyle(fontSize: 11, color: chipTextColor, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  // --- Unchanged methods from previous version (kept for completeness) ---
  Widget _buildTabletLayout(BuildContext context, NumberFormat formatter) {
    final Color totalsAccentColor = Colors.blueGrey;
    return LayoutBuilder(
      builder: (context, constraints) {
        final bool useVerticalLayout = constraints.maxWidth < 600;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOldSectionTitle('Description', context),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.grey.shade200),
              ),
              width: double.infinity,
              child: Text(
                description,
                style: const TextStyle(fontSize: 14),
              ),
            ),
            const SizedBox(height: 12),
            if (useVerticalLayout)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildOldSectionTitle('Manufacturer', context),
                  Container( child: Text(manufacturer)),
                  const SizedBox(height: 12),
                  _buildOldSectionTitle('Approval', context),
                  Container( child: Text(approval)),
                  const SizedBox(height: 12),
                  _buildOldSectionTitle('Quantity', context),
                  _buildQuantityControl(context),
                ],
              )
            else
              Row( /* ... Original structure ... */ ),
            const SizedBox(height: 12),
          ],
        );
      },
    );
  }
  Widget _buildOldSectionTitle(String title, BuildContext context, {double mb = 4.0}) {
    return Padding(
      padding: EdgeInsets.only(bottom: mb),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade600,
        ),
      ),
    );
  }
  Widget _buildCostTable(BuildContext context, NumberFormat formatter) {
    final String currency = 'SAR';
    return Container(
      height: 90,
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row( /* ... */ ),
    );
  }
  Widget _buildTotalsSection(BuildContext context, NumberFormat formatter) {
    final String currency = 'SAR';
    return Container(
      height: 90,
      width: double.infinity,
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration( /* ... */ ),
      child: Column( /* ... */ ),
    );
  }
    Widget _buildCostRow(String label, String value, {double fontSize = 12}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: TextStyle(fontSize: fontSize, color: Colors.grey.shade600)),
        Text(value, style: TextStyle(fontSize: fontSize, fontWeight: FontWeight.bold)),
      ],
    );
  }
}
