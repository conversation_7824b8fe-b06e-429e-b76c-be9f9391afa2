import 'package:flutter/material.dart';
import '../services/local_sql_service.dart';

class DatabaseTestScreen extends StatefulWidget {
  const DatabaseTestScreen({Key? key}) : super(key: key);

  @override
  State<DatabaseTestScreen> createState() => _DatabaseTestScreenState();
}

class _DatabaseTestScreenState extends State<DatabaseTestScreen> {
  final LocalSqlService _sqlService = LocalSqlService();
  final TextEditingController _tableNameController = TextEditingController(text: 'alarm');
  final TextEditingController _columnNameController = TextEditingController();
  final TextEditingController _columnTypeController = TextEditingController(text: 'TEXT');
  final TextEditingController _sqlQueryController = TextEditingController();
  
  List<String> _tables = [];
  List<Map<String, dynamic>> _columns = [];
  List<Map<String, dynamic>> _data = [];
  String _statusMessage = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadTables();
  }

  @override
  void dispose() {
    _tableNameController.dispose();
    _columnNameController.dispose();
    _columnTypeController.dispose();
    _sqlQueryController.dispose();
    super.dispose();
  }

  // Load all tables in the database
  Future<void> _loadTables() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Loading tables...';
    });

    try {
      final tables = await _sqlService.getTableNames();
      setState(() {
        _tables = tables;
        _statusMessage = 'Loaded ${tables.length} tables';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading tables: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Load columns for the selected table
  Future<void> _loadColumns() async {
    final tableName = _tableNameController.text.trim();
    if (tableName.isEmpty) {
      setState(() {
        _statusMessage = 'Please enter a table name';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Loading columns for $tableName...';
    });

    try {
      final columns = await _sqlService.getTableColumns(tableName);
      setState(() {
        _columns = columns;
        _statusMessage = 'Loaded ${columns.length} columns for $tableName';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading columns: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Load data from the selected table
  Future<void> _loadData() async {
    final tableName = _tableNameController.text.trim();
    if (tableName.isEmpty) {
      setState(() {
        _statusMessage = 'Please enter a table name';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Loading data from $tableName...';
    });

    try {
      final data = await _sqlService.getItems(tableName);
      setState(() {
        _data = data;
        _statusMessage = 'Loaded ${data.length} rows from $tableName';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading data: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Add a new column to the selected table
  Future<void> _addColumn() async {
    final tableName = _tableNameController.text.trim();
    final columnName = _columnNameController.text.trim();
    final columnType = _columnTypeController.text.trim();

    if (tableName.isEmpty || columnName.isEmpty || columnType.isEmpty) {
      setState(() {
        _statusMessage = 'Please enter table name, column name, and column type';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Adding column $columnName to $tableName...';
    });

    try {
      await _sqlService.addColumn(tableName, columnName, columnType);
      setState(() {
        _statusMessage = 'Added column $columnName to $tableName';
        _columnNameController.clear();
      });
      await _loadColumns();
    } catch (e) {
      setState(() {
        _statusMessage = 'Error adding column: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Execute a raw SQL query
  Future<void> _executeRawSql() async {
    final sql = _sqlQueryController.text.trim();
    if (sql.isEmpty) {
      setState(() {
        _statusMessage = 'Please enter an SQL query';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Executing SQL query...';
    });

    try {
      await _sqlService.executeRawSql(sql);
      setState(() {
        _statusMessage = 'SQL query executed successfully';
        _sqlQueryController.clear();
      });
      await _loadTables();
      await _loadColumns();
      await _loadData();
    } catch (e) {
      setState(() {
        _statusMessage = 'Error executing SQL query: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadTables();
              _loadColumns();
              _loadData();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status message
                  Container(
                    padding: const EdgeInsets.all(8.0),
                    color: Colors.grey.shade200,
                    width: double.infinity,
                    child: Text(
                      _statusMessage,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(height: 16.0),

                  // Table selection
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _tableNameController,
                          decoration: const InputDecoration(
                            labelText: 'Table Name',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: _loadColumns,
                        child: const Text('Load Columns'),
                      ),
                      const SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: _loadData,
                        child: const Text('Load Data'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16.0),

                  // Available tables
                  const Text(
                    'Available Tables:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Wrap(
                    spacing: 8.0,
                    children: _tables.map((table) => Chip(
                      label: Text(table),
                      onDeleted: () {
                        _tableNameController.text = table;
                      },
                    )).toList(),
                  ),
                  const SizedBox(height: 16.0),

                  // Add column section
                  const Text(
                    'Add Column:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _columnNameController,
                          decoration: const InputDecoration(
                            labelText: 'Column Name',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      Expanded(
                        child: TextField(
                          controller: _columnTypeController,
                          decoration: const InputDecoration(
                            labelText: 'Column Type',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: _addColumn,
                        child: const Text('Add Column'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16.0),

                  // Raw SQL section
                  const Text(
                    'Execute Raw SQL:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _sqlQueryController,
                          decoration: const InputDecoration(
                            labelText: 'SQL Query',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: _executeRawSql,
                        child: const Text('Execute'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16.0),

                  // Table columns
                  if (_columns.isNotEmpty) ...[
                    const Text(
                      'Table Columns:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        columns: const [
                          DataColumn(label: Text('ID')),
                          DataColumn(label: Text('Name')),
                          DataColumn(label: Text('Type')),
                          DataColumn(label: Text('Not Null')),
                          DataColumn(label: Text('Default')),
                          DataColumn(label: Text('Primary Key')),
                        ],
                        rows: _columns.map((column) => DataRow(
                          cells: [
                            DataCell(Text(column['cid'].toString())),
                            DataCell(Text(column['name'].toString())),
                            DataCell(Text(column['type'].toString())),
                            DataCell(Text(column['notnull'] == 1 ? 'Yes' : 'No')),
                            DataCell(Text(column['dflt_value']?.toString() ?? '')),
                            DataCell(Text(column['pk'] == 1 ? 'Yes' : 'No')),
                          ],
                        )).toList(),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                  ],

                  // Table data
                  if (_data.isNotEmpty) ...[
                    const Text(
                      'Table Data:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        columns: _data.first.keys.map((key) => 
                          DataColumn(label: Text(key))
                        ).toList(),
                        rows: _data.map((row) => DataRow(
                          cells: row.keys.map((key) => 
                            DataCell(Text(row[key]?.toString() ?? ''))
                          ).toList(),
                        )).toList(),
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}
