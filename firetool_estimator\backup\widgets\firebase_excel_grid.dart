import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class FirebaseExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;
  final Map<String, List<String>>? dropdownOptions;

  const FirebaseExcelGrid({
    super.key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
    this.dropdownOptions,
  });

  @override
  State<FirebaseExcelGrid> createState() => _FirebaseExcelGridState();
}

class _FirebaseExcelGridState extends State<FirebaseExcelGrid> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _cellEditController = TextEditingController();
  final Map<String, TextEditingController> _formControllers = {};

  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _filteredItems = [];
  List<String> _columns = [];
  String _searchQuery = '';
  bool _isLoading = true;
  String? _error;
  bool _isAddingItem = false;
  String? _editingItemId;
  String? _editingColumn;
  bool _isSelectMode = false;
  final Set<String> _selectedItems = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _horizontalController.dispose();
    _verticalController.dispose();
    _searchController.dispose();
    _cellEditController.dispose();
    for (var controller in _formControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  // Data loading and manipulation
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final items = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Determine columns
      final Set<String> columnSet = {'id'};

      // Add predefined columns if provided
      if (widget.predefinedColumns != null) {
        columnSet.addAll(widget.predefinedColumns!);
      }

      // Add any additional columns from the data
      for (var item in items) {
        columnSet.addAll(item.keys);
      }

      // Remove internal fields
      columnSet.remove('createdAt');
      columnSet.remove('updatedAt');

      setState(() {
        _items = items;
        _columns = columnSet.toList();
        _filteredItems = List.from(_items);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    if (_searchQuery.isEmpty) {
      _filteredItems = List.from(_items);
    } else {
      _filteredItems = _items.where((item) {
        return _columns.any((column) {
          final value = item[column]?.toString().toLowerCase() ?? '';
          return value.contains(_searchQuery.toLowerCase());
        });
      }).toList();
    }
  }

  // CRUD operations
  Future<void> _addItem(Map<String, dynamic> item) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(item);
      await _loadData();
      _showSnackBar('Item added successfully');
    } catch (e) {
      _showSnackBar('Error adding item: $e', isError: true);
    }
  }

  Future<void> _updateItem(String id, Map<String, dynamic> data) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .update(data);
      await _loadData();
      _showSnackBar('Item updated successfully');
    } catch (e) {
      _showSnackBar('Error updating item: $e', isError: true);
    }
  }

  Future<void> _deleteItem(String id) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .delete();
      await _loadData();
      _showSnackBar('Item deleted successfully');
    } catch (e) {
      _showSnackBar('Error deleting item: $e', isError: true);
    }
  }

  Future<void> _deleteSelectedItems() async {
    try {
      final batch = FirebaseFirestore.instance.batch();

      for (var id in _selectedItems) {
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc(id);
        batch.delete(docRef);
      }

      await batch.commit();
      await _loadData();

      setState(() {
        _selectedItems.clear();
        _isSelectMode = false;
      });

      _showSnackBar('${_selectedItems.length} items deleted successfully');
    } catch (e) {
      _showSnackBar('Error deleting items: $e', isError: true);
    }
  }

  // Import/Export functions
  Future<void> _importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result == null || result.files.isEmpty) return;

      final file = File(result.files.first.path!);
      final bytes = await file.readAsBytes();

      // Use a safer approach to parse Excel files
      try {
        // First try the normal approach
        final excel = Excel.decodeBytes(bytes);
        await _processExcelData(excel);
      } catch (excelError) {
        // If that fails, try a manual approach with CSV conversion
        _showSnackBar('Using alternative import method...', isError: false);
        await _importAsCSV(file);
      }
    } catch (e) {
      _showSnackBar('Error importing from Excel: $e', isError: true);
    }
  }

  Future<void> _processExcelData(Excel excel) async {
    final sheet = excel.tables.keys.first;
    final table = excel.tables[sheet]!;

    if (table.rows.isEmpty) {
      _showSnackBar('Excel file is empty', isError: true);
      return;
    }

    // Get headers from first row
    final headers = <String>[];
    for (var cell in table.rows[0]) {
      if (cell?.value != null) {
        headers.add(cell!.value.toString());
      }
    }

    if (headers.isEmpty) {
      _showSnackBar('No headers found in Excel file', isError: true);
      return;
    }

    // Process data rows
    final batch = FirebaseFirestore.instance.batch();
    int count = 0;

    for (var i = 1; i < table.rows.length; i++) {
      final row = table.rows[i];
      final Map<String, dynamic> item = {};

      for (var j = 0; j < row.length && j < headers.length; j++) {
        if (row[j]?.value != null) {
          final value = row[j]!.value;
          item[_getColumnKey(headers[j])] = value.toString(); // Convert all values to string for consistency
        }
      }

      if (item.isNotEmpty) {
        item['createdAt'] = FieldValue.serverTimestamp();
        final docRef = FirebaseFirestore.instance
            .collection(widget.collectionPath)
            .doc();
        batch.set(docRef, item);
        count++;
      }
    }

    await batch.commit();
    await _loadData();
    _showSnackBar('Imported $count items from Excel');
  }

  // Alternative import method using CSV as an intermediate format
  Future<void> _importAsCSV(File excelFile) async {
    try {
      // Create a temporary directory to save the CSV file
      final tempDir = await getTemporaryDirectory();
      final csvPath = '${tempDir.path}/temp_import.csv';

      // Use a system command to convert Excel to CSV
      // This is a workaround and might not work on all platforms
      // For a production app, consider using a more robust solution

      // For now, we'll try to parse the Excel file manually
      final batch = FirebaseFirestore.instance.batch();
      int count = 0;

      // Read the file as text and try to parse it
      final content = await excelFile.readAsString();
      final lines = content.split('\n');

      if (lines.isEmpty) {
        _showSnackBar('No data found in file', isError: true);
        return;
      }

      // Assume the first line contains headers
      final headers = lines[0].split(',').map((h) => h.trim()).toList();

      for (var i = 1; i < lines.length; i++) {
        if (lines[i].trim().isEmpty) continue;

        final values = lines[i].split(',');
        final Map<String, dynamic> item = {};

        for (var j = 0; j < values.length && j < headers.length; j++) {
          final value = values[j].trim();
          if (value.isNotEmpty) {
            item[_getColumnKey(headers[j])] = value;
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc();
          batch.set(docRef, item);
          count++;
        }
      }

      await batch.commit();
      await _loadData();
      _showSnackBar('Imported $count items using alternative method');
    } catch (e) {
      _showSnackBar('Error in alternative import: $e', isError: true);

      // As a last resort, suggest using clipboard import
      _showSnackBar('Try copying data from Excel and using "Import from Clipboard"', isError: false);
    }
  }

  Future<void> _importFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final text = clipboardData?.text;

      if (text == null || text.isEmpty) {
        _showSnackBar('Clipboard is empty', isError: true);
        return;
      }

      final rows = text.split('\n');
      if (rows.isEmpty) {
        _showSnackBar('No data found in clipboard', isError: true);
        return;
      }

      // Get headers from first row
      final headers = rows[0].split('\t');

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      int count = 0;

      for (var i = 1; i < rows.length; i++) {
        if (rows[i].trim().isEmpty) continue;

        final values = rows[i].split('\t');
        if (values.isEmpty) continue;

        final Map<String, dynamic> item = {};

        for (var j = 0; j < values.length && j < headers.length; j++) {
          if (values[j].trim().isNotEmpty) {
            item[_getColumnKey(headers[j].trim())] = values[j].trim();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance
              .collection(widget.collectionPath)
              .doc();
          batch.set(docRef, item);
          count++;
        }
      }

      await batch.commit();
      await _loadData();
      _showSnackBar('Imported $count items from clipboard');
    } catch (e) {
      _showSnackBar('Error importing from clipboard: $e', isError: true);
    }
  }

  Future<void> _exportToExcel() async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel[widget.title];

      // Add headers
      for (var i = 0; i < _columns.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value =
            TextCellValue(_getDisplayName(_columns[i]));
      }

      // Add data
      for (var i = 0; i < _items.length; i++) {
        final item = _items[i];
        for (var j = 0; j < _columns.length; j++) {
          final column = _columns[j];
          final value = item[column];
          if (value != null) {
            sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1)).value =
                TextCellValue(value.toString());
          }
        }
      }

      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/${widget.title.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final file = File(path);
      await file.writeAsBytes(excel.encode()!);
      _showSnackBar('Exported to $path');
    } catch (e) {
      _showSnackBar('Error exporting to Excel: $e', isError: true);
    }
  }

  // Helper methods
  String _getDisplayName(String column) {
    // Convert snake_case to Title Case
    return column.split('_').map((word) =>
      word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
    ).join(' ');
  }

  String _getColumnKey(String displayName) {
    // Convert Title Case to snake_case
    return displayName.toLowerCase().replaceAll(' ', '_');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // UI Building methods
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: widget.themeColor,
        ),
      );
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    return Column(
      children: [
        _buildToolbar(),
        if (_isAddingItem) _buildAddItemForm(),
        TabBar(
          controller: _tabController,
          labelColor: widget.themeColor,
          indicatorColor: widget.themeColor,
          tabs: const [
            Tab(icon: Icon(Icons.table_chart), text: 'Table View'),
            Tab(icon: Icon(Icons.grid_view), text: 'Card View'),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildTableView(),
              _buildCardView(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildToolbar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                      _applyFilters();
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.add),
                tooltip: 'Add Item',
                onPressed: () {
                  setState(() {
                    _isAddingItem = !_isAddingItem;

                    if (_isAddingItem) {
                      // Initialize form controllers
                      for (var column in _columns) {
                        if (column != 'id') {
                          _formControllers[column] = TextEditingController();
                        }
                      }
                    } else {
                      // Dispose form controllers
                      for (var controller in _formControllers.values) {
                        controller.dispose();
                      }
                      _formControllers.clear();
                    }
                  });
                },
              ),
              if (_isSelectMode)
                IconButton(
                  icon: const Icon(Icons.delete),
                  tooltip: 'Delete Selected',
                  onPressed: _selectedItems.isNotEmpty ? _deleteSelectedItems : null,
                ),
              IconButton(
                icon: Icon(_isSelectMode ? Icons.cancel : Icons.select_all),
                tooltip: _isSelectMode ? 'Cancel Selection' : 'Select Items',
                onPressed: () {
                  setState(() {
                    _isSelectMode = !_isSelectMode;
                    if (!_isSelectMode) {
                      _selectedItems.clear();
                    }
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.upload_file),
                label: const Text('Import Excel'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _importFromExcel,
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.paste),
                label: const Text('Import from Clipboard'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _importFromClipboard,
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.download),
                label: const Text('Export Excel'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  foregroundColor: Colors.white,
                ),
                onPressed: _exportToExcel,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddItemForm() {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Add New Item',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _isAddingItem = false;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16.0,
              runSpacing: 16.0,
              children: _columns.map((column) {
                if (column == 'id') return const SizedBox.shrink();

                final controller = _formControllers[column] ?? TextEditingController();

                // Check if this field has dropdown options
                final dropdownOptions = widget.dropdownOptions?[column];

                if (dropdownOptions != null) {
                  return SizedBox(
                    width: 200,
                    child: DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: _getDisplayName(column),
                        border: const OutlineInputBorder(),
                      ),
                      items: dropdownOptions.map((option) {
                        return DropdownMenuItem<String>(
                          value: option,
                          child: Text(option),
                        );
                      }).toList(),
                      onChanged: (value) {
                        controller.text = value ?? '';
                      },
                    ),
                  );
                }

                return SizedBox(
                  width: 200,
                  child: TextField(
                    controller: controller,
                    decoration: InputDecoration(
                      labelText: _getDisplayName(column),
                      border: const OutlineInputBorder(),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                final item = <String, dynamic>{};
                for (var column in _columns) {
                  if (column != 'id' && _formControllers.containsKey(column)) {
                    final value = _formControllers[column]!.text;
                    if (value.isNotEmpty) {
                      item[column] = value;
                    }
                  }
                }

                if (item.isNotEmpty) {
                  _addItem(item);
                  setState(() {
                    _isAddingItem = false;
                  });
                } else {
                  _showSnackBar('Please fill at least one field', isError: true);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableView() {
    return Scrollbar(
      controller: _verticalController,
      thumbVisibility: true,
      child: Scrollbar(
        controller: _horizontalController,
        thumbVisibility: true,
        notificationPredicate: (notification) => notification.depth == 1,
        child: SingleChildScrollView(
          controller: _verticalController,
          child: SingleChildScrollView(
            controller: _horizontalController,
            scrollDirection: Axis.horizontal,
            child: DataTable(
              headingRowColor: WidgetStateProperty.resolveWith<Color?>(
                (Set<WidgetState> states) => widget.themeColor.withAlpha(25),
              ),
              dataRowColor: WidgetStateProperty.resolveWith<Color?>(
                (Set<WidgetState> states) {
                  if (states.contains(WidgetState.selected)) {
                    return widget.themeColor.withAlpha(13);
                  }
                  return null;
                },
              ),
              columns: [
                if (_isSelectMode)
                  const DataColumn(
                    label: Text(''),
                  ),
                ..._columns.map((column) => DataColumn(
                  label: Text(
                    _getDisplayName(column),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                )),
                const DataColumn(
                  label: Text('Actions'),
                ),
              ],
              rows: _filteredItems.map((item) {
                final itemId = item['id'] as String;
                final isSelected = _selectedItems.contains(itemId);

                return DataRow(
                  selected: isSelected,
                  cells: [
                    if (_isSelectMode)
                      DataCell(
                        Checkbox(
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              if (value == true) {
                                _selectedItems.add(itemId);
                              } else {
                                _selectedItems.remove(itemId);
                              }
                            });
                          },
                        ),
                      ),
                    ..._columns.map((column) {
                      final value = item[column];
                      final isEditing = _editingItemId == itemId && _editingColumn == column;

                      return DataCell(
                        Text(value?.toString() ?? ''),
                        onTap: _isSelectMode ? null : () {
                          setState(() {
                            _editingItemId = itemId;
                            _editingColumn = column;
                            _cellEditController.text = value?.toString() ?? '';
                          });
                        },
                      );
                    }),
                    DataCell(
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                            onPressed: () => _deleteItem(itemId),
                            tooltip: 'Delete',
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCardView() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        final itemId = item['id'] as String;
        final isSelected = _selectedItems.contains(itemId);

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: isSelected
                ? BorderSide(color: widget.themeColor, width: 2)
                : BorderSide.none,
          ),
          child: InkWell(
            onTap: _isSelectMode
                ? () {
                    setState(() {
                      if (isSelected) {
                        _selectedItems.remove(itemId);
                      } else {
                        _selectedItems.add(itemId);
                      }
                    });
                  }
                : null,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (_isSelectMode)
                        Checkbox(
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              if (value == true) {
                                _selectedItems.add(itemId);
                              } else {
                                _selectedItems.remove(itemId);
                              }
                            });
                          },
                        ),
                      Expanded(
                        child: Text(
                          item['model']?.toString() ?? item['name']?.toString() ?? 'Item $index',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                        onPressed: () => _deleteItem(itemId),
                        tooltip: 'Delete',
                      ),
                    ],
                  ),
                  const Divider(),
                  Expanded(
                    child: ListView(
                      children: _columns.where((column) => column != 'id').map((column) {
                        final value = item[column];
                        if (value == null) return const SizedBox.shrink();

                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${_getDisplayName(column)}: ',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  value.toString(),
                                  style: const TextStyle(fontSize: 12),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
