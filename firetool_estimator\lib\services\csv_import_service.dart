import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'package:uuid/uuid.dart';
import '../models/super_database_models.dart';
import 'super_database_service.dart';

/// Service for handling CSV import operations
class CsvImportService {
  static final CsvImportService _instance = CsvImportService._internal();
  factory CsvImportService() => _instance;
  CsvImportService._internal();

  final _superDbService = SuperDatabaseService();
  final _uuid = const Uuid();

  /// Import CSV file as a new subsection
  Future<ImportExportOperation> importCsvAsSubsection({
    required String sectionId,
    required String subsectionName,
    String? filePath,
    String? description,
    bool hasHeader = true,
    String delimiter = ',',
    ImportStrategy strategy = ImportStrategy.addNew,
  }) async {
    final operationId = _uuid.v4();
    final startTime = DateTime.now();

    // Pick file if not provided
    String? selectedFilePath = filePath;
    if (selectedFilePath == null) {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        throw Exception('No file selected');
      }

      selectedFilePath = result.files.first.path!;
    }

    final operation = ImportExportOperation(
      id: operationId,
      type: 'import',
      sectionId: sectionId,
      filePath: selectedFilePath,
      startTime: startTime,
      status: OperationStatus.inProgress,
    );

    try {
      // Read CSV file
      final file = File(selectedFilePath);
      final content = await file.readAsString();
      
      // Parse CSV
      final csvData = const CsvToListConverter().convert(
        content,
        fieldDelimiter: delimiter,
        shouldParseNumbers: false, // We'll handle type conversion ourselves
      );

      if (csvData.isEmpty) {
        throw Exception('CSV file is empty');
      }

      List<String> headers;
      List<List<dynamic>> dataRows;

      if (hasHeader) {
        headers = csvData.first.map((e) => e.toString().trim()).toList();
        dataRows = csvData.skip(1).toList();
      } else {
        // Generate column names
        final columnCount = csvData.first.length;
        headers = List.generate(columnCount, (index) => 'Column${index + 1}');
        dataRows = csvData;
      }

      // Infer column types
      final columns = <ColumnDefinition>[];
      for (int i = 0; i < headers.length; i++) {
        final columnName = _sanitizeColumnName(headers[i]);
        final displayName = headers[i];
        final type = _inferColumnType(dataRows, i);
        
        columns.add(ColumnDefinition(
          name: columnName,
          displayName: displayName,
          type: type,
        ));
      }

      // Get section
      final section = await _superDbService.getSection(sectionId);
      if (section == null) {
        throw Exception('Section not found');
      }

      // Check if subsection already exists
      final existingSubsections = await _superDbService.getSubsections(sectionId);
      final existingSubsection = existingSubsections
          .where((s) => s.name == _sanitizeFileName(subsectionName))
          .firstOrNull;

      Subsection subsection;
      if (existingSubsection != null && strategy == ImportStrategy.replace) {
        // Delete existing data
        await _clearSubsectionData(section.name, existingSubsection.name);
        subsection = existingSubsection;
      } else if (existingSubsection != null && strategy == ImportStrategy.merge) {
        subsection = existingSubsection;
      } else {
        // Create new subsection
        subsection = await _superDbService.createSubsection(
          sectionId: sectionId,
          name: _sanitizeFileName(subsectionName),
          displayName: subsectionName,
          columns: columns,
          description: description,
        );
      }

      // Import data
      final db = await _superDbService.getSectionDatabase(section.name);
      int processedRows = 0;

      await db.transaction((txn) async {
        for (final row in dataRows) {
          final rowData = <String, dynamic>{};
          
          for (int i = 0; i < columns.length && i < row.length; i++) {
            final cell = row[i];
            final column = columns[i];
            
            if (cell != null && cell.toString().trim().isNotEmpty) {
              rowData[column.name] = _convertCellValue(cell, column.type);
            }
          }

          if (rowData.isNotEmpty) {
            await txn.insert(subsection.name, rowData);
            processedRows++;
          }
        }
      });

      // Update operation status
      final completedOperation = ImportExportOperation(
        id: operationId,
        type: 'import',
        sectionId: sectionId,
        subsectionId: subsection.id,
        filePath: selectedFilePath,
        startTime: startTime,
        endTime: DateTime.now(),
        status: OperationStatus.completed,
        totalRows: dataRows.length,
        processedRows: processedRows,
      );

      if (kDebugMode) {
        print('Successfully imported CSV file: $selectedFilePath');
        print('Created/updated subsection: ${subsection.displayName}');
        print('Total rows processed: $processedRows/${dataRows.length}');
      }

      return completedOperation;

    } catch (e) {
      if (kDebugMode) {
        print('Error importing CSV file: $e');
      }

      return ImportExportOperation(
        id: operationId,
        type: 'import',
        sectionId: sectionId,
        filePath: selectedFilePath,
        startTime: startTime,
        endTime: DateTime.now(),
        status: OperationStatus.failed,
        errorMessage: e.toString(),
      );
    }
  }

  /// Export subsection to CSV file
  Future<ImportExportOperation> exportSubsectionToCsv({
    required String sectionId,
    required String subsectionId,
    String? filePath,
    String delimiter = ',',
    bool includeHeader = true,
  }) async {
    final operationId = _uuid.v4();
    final startTime = DateTime.now();

    try {
      final section = await _superDbService.getSection(sectionId);
      if (section == null) {
        throw Exception('Section not found');
      }

      final subsections = await _superDbService.getSubsections(sectionId);
      final subsection = subsections.where((s) => s.id == subsectionId).firstOrNull;
      if (subsection == null) {
        throw Exception('Subsection not found');
      }

      // Get data from database
      final db = await _superDbService.getSectionDatabase(section.name);
      final rows = await db.query(subsection.name);

      // Prepare CSV data
      final csvData = <List<dynamic>>[];

      // Add header if requested
      if (includeHeader) {
        csvData.add(subsection.columns.map((col) => col.displayName).toList());
      }

      // Add data rows
      for (final row in rows) {
        final csvRow = <dynamic>[];
        for (final column in subsection.columns) {
          final value = row[column.name];
          csvRow.add(_formatCellValueForCsv(value, column.type));
        }
        csvData.add(csvRow);
      }

      // Convert to CSV string
      final csvString = const ListToCsvConverter().convert(
        csvData,
        fieldDelimiter: delimiter,
      );

      // Save file
      String outputPath = filePath ?? await _getDefaultCsvExportPath(subsection.displayName);
      final file = File(outputPath);
      await file.writeAsString(csvString);

      final completedOperation = ImportExportOperation(
        id: operationId,
        type: 'export',
        sectionId: sectionId,
        subsectionId: subsectionId,
        filePath: outputPath,
        startTime: startTime,
        endTime: DateTime.now(),
        status: OperationStatus.completed,
        totalRows: rows.length,
        processedRows: rows.length,
      );

      if (kDebugMode) {
        print('Successfully exported subsection to CSV: $outputPath');
        print('Total rows exported: ${rows.length}');
      }

      return completedOperation;

    } catch (e) {
      if (kDebugMode) {
        print('Error exporting subsection to CSV: $e');
      }

      return ImportExportOperation(
        id: operationId,
        type: 'export',
        sectionId: sectionId,
        subsectionId: subsectionId,
        filePath: filePath ?? '',
        startTime: startTime,
        endTime: DateTime.now(),
        status: OperationStatus.failed,
        errorMessage: e.toString(),
      );
    }
  }

  /// Infer column type from CSV data
  ColumnType _inferColumnType(List<List<dynamic>> dataRows, int columnIndex) {
    final sampleSize = 20;
    final sampleRows = dataRows.take(sampleSize);
    
    int intCount = 0;
    int doubleCount = 0;
    int dateCount = 0;
    int boolCount = 0;
    int emailCount = 0;
    int urlCount = 0;
    int totalCount = 0;

    for (final row in sampleRows) {
      if (columnIndex < row.length) {
        final cell = row[columnIndex];
        if (cell != null) {
          final value = cell.toString().trim();
          if (value.isNotEmpty) {
            totalCount++;
            
            // Check for boolean
            if (value.toLowerCase() == 'true' || value.toLowerCase() == 'false' ||
                value.toLowerCase() == 'yes' || value.toLowerCase() == 'no') {
              boolCount++;
            }
            // Check for email
            else if (RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(value)) {
              emailCount++;
            }
            // Check for URL
            else if (Uri.tryParse(value)?.hasAbsolutePath == true) {
              urlCount++;
            }
            // Check for date
            else if (DateTime.tryParse(value) != null) {
              dateCount++;
            }
            // Check for numbers
            else if (int.tryParse(value) != null) {
              intCount++;
            } else if (double.tryParse(value) != null) {
              doubleCount++;
            }
          }
        }
      }
    }

    if (totalCount == 0) return ColumnType.text;

    final boolRatio = boolCount / totalCount;
    final emailRatio = emailCount / totalCount;
    final urlRatio = urlCount / totalCount;
    final dateRatio = dateCount / totalCount;
    final intRatio = intCount / totalCount;
    final doubleRatio = doubleCount / totalCount;

    if (boolRatio > 0.8) return ColumnType.boolean;
    if (emailRatio > 0.8) return ColumnType.email;
    if (urlRatio > 0.8) return ColumnType.url;
    if (dateRatio > 0.8) return ColumnType.datetime;
    if (intRatio > 0.8) return ColumnType.integer;
    if (doubleRatio > 0.8) return ColumnType.real;

    return ColumnType.text;
  }

  dynamic _convertCellValue(dynamic value, ColumnType type) {
    if (value == null) return null;
    
    final stringValue = value.toString().trim();
    if (stringValue.isEmpty) return null;

    switch (type) {
      case ColumnType.integer:
        return int.tryParse(stringValue) ?? 0;
      case ColumnType.real:
      case ColumnType.currency:
        return double.tryParse(stringValue) ?? 0.0;
      case ColumnType.boolean:
        final lower = stringValue.toLowerCase();
        return (lower == 'true' || lower == 'yes' || lower == '1') ? 1 : 0;
      case ColumnType.date:
      case ColumnType.datetime:
        final date = DateTime.tryParse(stringValue);
        return date?.toIso8601String() ?? stringValue;
      default:
        return stringValue;
    }
  }

  dynamic _formatCellValueForCsv(dynamic value, ColumnType type) {
    if (value == null) return '';
    
    switch (type) {
      case ColumnType.boolean:
        return (value == 1 || value == true) ? 'Yes' : 'No';
      case ColumnType.date:
      case ColumnType.datetime:
        if (value is String) {
          final date = DateTime.tryParse(value);
          return date?.toIso8601String() ?? value;
        }
        return value.toString();
      default:
        return value.toString();
    }
  }

  Future<void> _clearSubsectionData(String sectionName, String tableName) async {
    final db = await _superDbService.getSectionDatabase(sectionName);
    await db.delete(tableName);
  }

  String _sanitizeFileName(String input) {
    return input
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  String _sanitizeColumnName(String input) {
    return input
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  Future<String> _getDefaultCsvExportPath(String subsectionName) async {
    return '${_sanitizeFileName(subsectionName)}_export.csv';
  }
}

enum ImportStrategy {
  addNew,    // Create new subsection (default)
  replace,   // Replace existing data
  merge,     // Merge with existing data
}
