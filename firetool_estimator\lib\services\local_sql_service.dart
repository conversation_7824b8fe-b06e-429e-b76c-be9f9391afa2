import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:async';
import 'dart:math';

class LocalSqlService {
  static final LocalSqlService _instance = LocalSqlService._internal();
  static Database? _database;

  // Factory constructor
  factory LocalSqlService() {
    return _instance;
  }

  // Private constructor
  LocalSqlService._internal();

  // Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialize database
  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'firetool.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
    );
  }

  // Create database tables
  Future<void> _createDatabase(Database db, int version) async {
    // Create tables for each system type
    await db.execute('''
      CREATE TABLE alarm (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE water (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE foam (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE fm200 (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE novec (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE co2 (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE materials (
        id TEXT PRIMARY KEY,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE projects (
        id TEXT PRIMARY KEY,
        name TEXT,
        description TEXT,
        client TEXT,
        location TEXT,
        created_at TEXT,
        updated_at TEXT,
        status TEXT,
        currency TEXT,
        exchange_rate TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE project_systems (
        id TEXT PRIMARY KEY,
        project_id TEXT,
        system_type TEXT,
        name TEXT,
        description TEXT,
        created_at TEXT,
        FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE system_items (
        id TEXT PRIMARY KEY,
        system_id TEXT,
        item_id TEXT,
        item_type TEXT,
        quantity INTEGER,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT,
        FOREIGN KEY (system_id) REFERENCES project_systems (id) ON DELETE CASCADE
      )
    ''');

    // Insert sample data
    await _insertSampleData(db);
  }

  // Insert sample data for each system type
  Future<void> _insertSampleData(Database db) async {
    final systemTypes = ['alarm', 'water', 'foam', 'fm200', 'novec', 'co2', 'materials'];
    final random = Random();
    
    for (var systemType in systemTypes) {
      for (var i = 1; i <= 20; i++) {
        await db.insert(systemType, {
          'id': 'sample-$systemType-$i',
          'model': 'Model $i',
          'description': 'Description for $systemType item $i',
          'manufacturer': 'Manufacturer ${i % 3 + 1}',
          'approval': 'UL Listed',
          'ex_works_price': '${(i * 100) + random.nextInt(50)}',
          'local_price': '${(i * 150) + random.nextInt(75)}',
          'installation_price': '${(i * 50) + random.nextInt(25)}',
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }

    // Create sample projects
    final projectIds = <String>[];
    for (var i = 1; i <= 5; i++) {
      final projectId = 'project-$i';
      projectIds.add(projectId);
      
      await db.insert('projects', {
        'id': projectId,
        'name': 'Project $i',
        'description': 'Sample project $i description',
        'client': 'Client ${i % 3 + 1}',
        'location': 'Location $i',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'status': i % 3 == 0 ? 'Completed' : (i % 3 == 1 ? 'In Progress' : 'Pending'),
        'currency': 'SAR',
        'exchange_rate': '3.75',
      });
    }

    // Create sample systems for each project
    final systemIds = <String>[];
    for (var projectId in projectIds) {
      for (var i = 1; i <= 3; i++) {
        final systemType = systemTypes[random.nextInt(systemTypes.length)];
        final systemId = 'system-$projectId-$i';
        systemIds.add(systemId);
        
        await db.insert('project_systems', {
          'id': systemId,
          'project_id': projectId,
          'system_type': systemType,
          'name': '$systemType System $i',
          'description': 'Sample $systemType system $i for project $projectId',
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }

    // Create sample items for each system
    for (var systemId in systemIds) {
      final systemType = (await db.query(
        'project_systems',
        columns: ['system_type'],
        where: 'id = ?',
        whereArgs: [systemId],
      )).first['system_type'] as String;
      
      // Get random items from the corresponding system type
      final items = await db.query(
        systemType,
        limit: 5 + random.nextInt(5),
      );
      
      for (var item in items) {
        await db.insert('system_items', {
          'id': 'item-$systemId-${item['id']}',
          'system_id': systemId,
          'item_id': item['id'] as String,
          'item_type': systemType,
          'quantity': 1 + random.nextInt(10),
          'ex_works_price': item['ex_works_price'],
          'local_price': item['local_price'],
          'installation_price': item['installation_price'],
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }
  }

  // CRUD Operations

  // Get all items from a collection
  Future<List<Map<String, dynamic>>> getItems(String collectionPath) async {
    final db = await database;
    return await db.query(collectionPath);
  }

  // Get item by ID
  Future<Map<String, dynamic>?> getItemById(String collectionPath, String id) async {
    final db = await database;
    final results = await db.query(
      collectionPath,
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    
    return results.isNotEmpty ? results.first : null;
  }

  // Add a new item to a collection
  Future<String> addItem(String collectionPath, Map<String, dynamic> item) async {
    final db = await database;
    
    // Generate a unique ID if not provided
    if (!item.containsKey('id') || item['id'] == null) {
      item['id'] = 'item-${DateTime.now().millisecondsSinceEpoch}';
    }
    
    // Add created_at timestamp if not provided
    if (!item.containsKey('created_at') || item['created_at'] == null) {
      item['created_at'] = DateTime.now().toIso8601String();
    }
    
    await db.insert(collectionPath, item);
    return item['id'];
  }

  // Update an item in a collection
  Future<void> updateItem(String collectionPath, String itemId, Map<String, dynamic> item) async {
    final db = await database;
    await db.update(
      collectionPath,
      item,
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Update a specific field in an item
  Future<void> updateField(String collectionPath, String itemId, String field, dynamic value) async {
    final db = await database;
    await db.update(
      collectionPath,
      {field: value},
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Delete an item from a collection
  Future<void> deleteItem(String collectionPath, String itemId) async {
    final db = await database;
    await db.delete(
      collectionPath,
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Close the database
  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
