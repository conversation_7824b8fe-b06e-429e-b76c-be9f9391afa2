import 'package:flutter/material.dart';
import '../models/super_database_models.dart';
import '../services/super_database_service.dart';

class ColumnManagementDialog extends StatefulWidget {
  final Subsection subsection;
  final Section section;

  const ColumnManagementDialog({
    Key? key,
    required this.subsection,
    required this.section,
  }) : super(key: key);

  @override
  State<ColumnManagementDialog> createState() => _ColumnManagementDialogState();
}

class _ColumnManagementDialogState extends State<ColumnManagementDialog> {
  final _superDbService = SuperDatabaseService();
  List<ColumnDefinition> _columns = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _columns = List.from(widget.subsection.columns);
  }

  void _addColumn() {
    setState(() {
      _columns.add(ColumnDefinition(
        name: 'new_column_${_columns.length}',
        displayName: 'New Column ${_columns.length}',
        type: ColumnType.text,
      ));
    });
  }

  void _removeColumn(int index) {
    if (_columns.length <= 1) {
      _showErrorSnackBar('Cannot delete the last column');
      return;
    }
    
    setState(() {
      _columns.removeAt(index);
    });
  }

  void _editColumn(int index) {
    showDialog(
      context: context,
      builder: (context) => _ColumnEditDialog(
        column: _columns[index],
        onSave: (updatedColumn) {
          setState(() {
            _columns[index] = updatedColumn;
          });
        },
      ),
    );
  }

  Future<void> _saveChanges() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Update the subsection with new columns
      await _superDbService.updateSubsectionColumns(
        sectionName: widget.section.name,
        subsectionId: widget.subsection.id,
        newColumns: _columns,
      );

      if (mounted) {
        Navigator.of(context).pop(_columns);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to update columns: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Manage Columns - ${widget.subsection.displayName}'),
      content: SizedBox(
        width: 600,
        height: 500,
        child: Column(
          children: [
            // Header with add button
            Row(
              children: [
                Text(
                  'Columns (${_columns.length})',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _addColumn,
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Add Column'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Columns list
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _columns.length,
                  itemBuilder: (context, index) {
                    final column = _columns[index];
                    final isLastColumn = _columns.length == 1;
                    
                    return ListTile(
                      leading: Icon(
                        _getColumnTypeIcon(column.type),
                        color: Theme.of(context).primaryColor,
                      ),
                      title: Text(
                        column.displayName,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      subtitle: Text(
                        '${column.name} • ${column.type.displayName}${column.isRequired ? ' • Required' : ''}${column.isUnique ? ' • Unique' : ''}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit, size: 18),
                            onPressed: () => _editColumn(index),
                            tooltip: 'Edit column',
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.delete, 
                              size: 18,
                              color: isLastColumn ? Colors.grey : Colors.red,
                            ),
                            onPressed: isLastColumn ? null : () => _removeColumn(index),
                            tooltip: isLastColumn ? 'Cannot delete last column' : 'Delete column',
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveChanges,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Save Changes'),
        ),
      ],
    );
  }

  IconData _getColumnTypeIcon(ColumnType type) {
    switch (type) {
      case ColumnType.text:
        return Icons.text_fields;
      case ColumnType.integer:
        return Icons.numbers;
      case ColumnType.real:
        return Icons.numbers;
      case ColumnType.boolean:
        return Icons.check_box;
      case ColumnType.date:
        return Icons.calendar_today;
      case ColumnType.datetime:
        return Icons.access_time;
      case ColumnType.currency:
        return Icons.attach_money;
      case ColumnType.email:
        return Icons.email;
      case ColumnType.url:
        return Icons.link;
      case ColumnType.phone:
        return Icons.phone;
      case ColumnType.json:
        return Icons.code;
    }
  }
}

class _ColumnEditDialog extends StatefulWidget {
  final ColumnDefinition column;
  final Function(ColumnDefinition) onSave;

  const _ColumnEditDialog({
    required this.column,
    required this.onSave,
  });

  @override
  State<_ColumnEditDialog> createState() => _ColumnEditDialogState();
}

class _ColumnEditDialogState extends State<_ColumnEditDialog> {
  late TextEditingController _nameController;
  late TextEditingController _displayNameController;
  late TextEditingController _descriptionController;
  late ColumnType _selectedType;
  late bool _isRequired;
  late bool _isUnique;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.column.name);
    _displayNameController = TextEditingController(text: widget.column.displayName);
    _descriptionController = TextEditingController(text: widget.column.description ?? '');
    _selectedType = widget.column.type;
    _isRequired = widget.column.isRequired;
    _isUnique = widget.column.isUnique;
    
    _displayNameController.addListener(_updateNameFromDisplayName);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _displayNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _updateNameFromDisplayName() {
    final displayName = _displayNameController.text;
    final sanitizedName = displayName
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .replaceAll(RegExp(r'\s+'), '_');
    _nameController.text = sanitizedName;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Column'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'Display Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Internal Name',
                border: OutlineInputBorder(),
              ),
              readOnly: true,
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<ColumnType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Type',
                border: OutlineInputBorder(),
              ),
              items: ColumnType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: const Text('Required'),
              value: _isRequired,
              onChanged: (bool? value) {
                setState(() {
                  _isRequired = value ?? false;
                });
              },
            ),
            CheckboxListTile(
              title: const Text('Unique'),
              value: _isUnique,
              onChanged: (bool? value) {
                setState(() {
                  _isUnique = value ?? false;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final updatedColumn = ColumnDefinition(
              name: _nameController.text.trim(),
              displayName: _displayNameController.text.trim(),
              type: _selectedType,
              description: _descriptionController.text.trim().isEmpty 
                  ? null 
                  : _descriptionController.text.trim(),
              isRequired: _isRequired,
              isUnique: _isUnique,
            );
            widget.onSave(updatedColumn);
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
