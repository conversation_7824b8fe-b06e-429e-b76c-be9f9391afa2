import 'package:flutter/material.dart';
import '../services/local_sql_service.dart';

class MockCompleteExcelGrid extends StatefulWidget {
  final String collectionPath;
  final String title;
  final Color themeColor;
  final List<String>? predefinedColumns;

  const MockCompleteExcelGrid({
    Key? key,
    required this.collectionPath,
    required this.title,
    required this.themeColor,
    this.predefinedColumns,
  }) : super(key: key);

  @override
  State<MockCompleteExcelGrid> createState() => _MockCompleteExcelGridState();
}

class _MockCompleteExcelGridState extends State<MockCompleteExcelGrid> {
  final LocalSqlService _sqlService = LocalSqlService();
  final ScrollController _horizontalScrollController = ScrollController();
  final ScrollController _verticalScrollController = ScrollController();

  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _columns = [];
  bool _isLoading = true;
  String? _error;

  // Controllers for adding new columns
  final TextEditingController _newColumnNameController = TextEditingController();
  final TextEditingController _newColumnTypeController = TextEditingController(text: 'TEXT');

  // Controllers for editing cells
  final Map<String, TextEditingController> _cellControllers = {};

  @override
  void initState() {
    super.initState();
    _loadData();
    _loadTableInfo();
  }

  @override
  void dispose() {
    _newColumnNameController.dispose();
    _newColumnTypeController.dispose();
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();

    // Dispose all cell controllers
    for (var controller in _cellControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  // Load data from the database
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final items = await _sqlService.getItems(widget.collectionPath);

      setState(() {
        _items = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // Load table structure information
  Future<void> _loadTableInfo() async {
    try {
      final columns = await _sqlService.getTableColumns(widget.collectionPath);

      setState(() {
        _columns = columns;
      });
    } catch (e) {
      print('Error loading table info: $e');
    }
  }

  // Add a new column to the table
  Future<void> _addColumn() async {
    final name = _newColumnNameController.text.trim();
    final type = _newColumnTypeController.text.trim().toUpperCase();

    if (name.isEmpty || type.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Column name and type are required')),
      );
      return;
    }

    try {
      await _sqlService.addColumn(widget.collectionPath, name, type);

      // Refresh data
      await _loadTableInfo();
      await _loadData();

      // Clear text fields
      _newColumnNameController.clear();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Column $name added successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding column: $e')),
      );
    }
  }

  // Delete a column from the table
  Future<void> _deleteColumn(String columnName) async {
    try {
      await _sqlService.deleteColumn(widget.collectionPath, columnName);

      // Refresh data
      await _loadTableInfo();
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Column $columnName deleted successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting column: $e')),
      );
    }
  }

  // Add a new row to the table
  Future<void> _addRow() async {
    final Map<String, dynamic> newRow = {};

    // Add default values for each column
    for (var column in _columns) {
      final name = column['name'].toString();
      if (name == 'id') {
        // ID will be auto-generated
        continue;
      }

      // Default empty values based on column type
      final type = column['type'].toString().toUpperCase();
      if (type.contains('INT')) {
        newRow[name] = 0;
      } else if (type.contains('REAL') || type.contains('FLOAT') || type.contains('DOUBLE')) {
        newRow[name] = 0.0;
      } else {
        newRow[name] = '';
      }
    }

    try {
      await _sqlService.addItem(widget.collectionPath, newRow);

      // Refresh data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Row added successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding row: $e')),
      );
    }
  }

  // Delete a row from the table
  Future<void> _deleteRow(String itemId) async {
    try {
      await _sqlService.deleteItem(widget.collectionPath, itemId);

      // Refresh data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Row deleted successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting row: $e')),
      );
    }
  }

  // Update a cell value
  Future<void> _updateCell(String itemId, String columnName, String value) async {
    try {
      await _sqlService.updateField(widget.collectionPath, itemId, columnName, value);

      // Refresh data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Cell updated successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating cell: $e')),
      );
    }
  }

  // Show dialog to add a new column
  void _showAddColumnDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Column'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _newColumnNameController,
              decoration: const InputDecoration(
                labelText: 'Column Name',
                hintText: 'Enter column name',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _newColumnTypeController,
              decoration: const InputDecoration(
                labelText: 'Column Type',
                hintText: 'TEXT, INTEGER, REAL, etc.',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _addColumn();
            },
            child: const Text('Add Column'),
          ),
        ],
      ),
    );
  }

  // Show confirmation dialog for deleting a column
  void _showDeleteColumnDialog(String columnName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Column'),
        content: Text('Are you sure you want to delete the column "$columnName"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            onPressed: () {
              Navigator.pop(context);
              _deleteColumn(columnName);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  // Show confirmation dialog for deleting a row
  void _showDeleteRowDialog(Map<String, dynamic> item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Row'),
        content: const Text('Are you sure you want to delete this row?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            onPressed: () {
              Navigator.pop(context);
              _deleteRow(item['id'].toString());
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  // Show dialog to edit a cell
  void _showEditCellDialog(Map<String, dynamic> item, String columnName) {
    // Create a unique key for this cell
    final cellKey = '${item['id']}_$columnName';

    // Create or reuse a controller for this cell
    if (!_cellControllers.containsKey(cellKey)) {
      _cellControllers[cellKey] = TextEditingController(
        text: item[columnName]?.toString() ?? '',
      );
    } else {
      _cellControllers[cellKey]!.text = item[columnName]?.toString() ?? '';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit $columnName'),
        content: TextField(
          controller: _cellControllers[cellKey],
          decoration: InputDecoration(
            labelText: columnName,
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateCell(
                item['id'].toString(),
                columnName,
                _cellControllers[cellKey]!.text,
              );
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            tooltip: 'Add Column',
            onPressed: _showAddColumnDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Data',
            onPressed: () {
              _loadData();
              _loadTableInfo();
            },
          ),
        ],
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : _error != null
          ? Center(child: Text('Error: $_error'))
          : Column(
              children: [
                // Table structure info
                Container(
                  padding: const EdgeInsets.all(8.0),
                  color: widget.themeColor.withOpacity(0.1),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Table Structure: ${widget.collectionPath}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 150,
                        child: Scrollbar(
                          controller: _horizontalScrollController,
                          thumbVisibility: true,
                          trackVisibility: true,
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            controller: _horizontalScrollController,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('ID')),
                                DataColumn(label: Text('Name')),
                                DataColumn(label: Text('Type')),
                                DataColumn(label: Text('Not Null')),
                                DataColumn(label: Text('Default')),
                                DataColumn(label: Text('Primary Key')),
                                DataColumn(label: Text('Actions')),
                              ],
                              rows: _columns.map((column) => DataRow(
                                cells: [
                                  DataCell(Text(column['cid'].toString())),
                                  DataCell(Text(column['name'].toString())),
                                  DataCell(Text(column['type'].toString())),
                                  DataCell(Text(column['notnull'] == 1 ? 'Yes' : 'No')),
                                  DataCell(Text(column['dflt_value']?.toString() ?? '')),
                                  DataCell(Text(column['pk'] == 1 ? 'Yes' : 'No')),
                                  DataCell(
                                    // Don't allow deleting primary key columns
                                    column['pk'] == 1
                                      ? const Text('Cannot delete PK')
                                      : IconButton(
                                          icon: const Icon(Icons.delete, color: Colors.red),
                                          onPressed: () => _showDeleteColumnDialog(column['name'].toString()),
                                        ),
                                  ),
                                ],
                              )).toList(),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const Divider(),

                // Data table
                Expanded(
                  child: Scrollbar(
                    controller: _verticalScrollController,
                    thumbVisibility: true,
                    trackVisibility: true,
                    child: SingleChildScrollView(
                      controller: _verticalScrollController,
                      child: Scrollbar(
                        controller: _horizontalScrollController,
                        thumbVisibility: true,
                        trackVisibility: true,
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          controller: _horizontalScrollController,
                          child: _items.isEmpty
                            ? const Padding(
                                padding: EdgeInsets.all(16.0),
                                child: Text('No data available'),
                              )
                            : DataTable(
                                columns: [
                                  // Add a column for actions
                                  const DataColumn(label: Text('Actions')),
                                  // Add columns for each field
                                  ..._columns.map((column) =>
                                    DataColumn(label: Text(column['name'].toString()))
                                  ).toList(),
                                ],
                                rows: _items.map((item) => DataRow(
                                  cells: [
                                    // Actions cell
                                    DataCell(
                                      IconButton(
                                        icon: const Icon(Icons.delete, color: Colors.red),
                                        onPressed: () => _showDeleteRowDialog(item),
                                      ),
                                    ),
                                    // Data cells
                                    ..._columns.map((column) =>
                                      DataCell(
                                        Text(item[column['name']]?.toString() ?? ''),
                                        onTap: () {
                                          // Show edit dialog for this cell
                                          _showEditCellDialog(item, column['name'].toString());
                                        },
                                      )
                                    ).toList(),
                                  ],
                                )).toList(),
                              ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addRow,
        backgroundColor: widget.themeColor,
        child: const Icon(Icons.add),
        tooltip: 'Add Row',
      ),
    );
  }
}
