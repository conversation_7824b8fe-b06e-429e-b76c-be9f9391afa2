import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:async';
import 'dart:math' as math;
import '../services/super_database_service.dart';
import '../services/excel_import_export_service.dart';
import '../models/super_database_models.dart';

class SuperDatabaseGrid extends StatefulWidget {
  final String sectionName;
  final String sectionId;
  final Color themeColor;

  const SuperDatabaseGrid({
    super.key,
    required this.sectionName,
    required this.sectionId,
    required this.themeColor,
  });

  @override
  State<SuperDatabaseGrid> createState() => _SuperDatabaseGridState();
}

class _SuperDatabaseGridState extends State<SuperDatabaseGrid>
    with TickerProviderStateMixin {

  // Services
  final SuperDatabaseService _dbService = SuperDatabaseService();

  // Controllers
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _editingController = TextEditingController();
  final FocusNode _gridFocusNode = FocusNode();
  final FocusNode _editingFocusNode = FocusNode();

  // Tab controller for subsections
  TabController? _tabController;

  // Data state
  Section? _section;
  List<Subsection> _subsections = [];
  Subsection? _currentSubsection;
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _filteredItems = [];
  List<ColumnDefinition> _columns = [];
  final Map<String, double> _columnWidths = {};
  List<double> _rowHeights = [];
  bool _isLoading = true;
  String? _error;

  // Selection state
  int? _selectedRow;
  int? _selectedCol;
  int? _selectionStartRow;
  int? _selectionStartCol;
  int? _selectionEndRow;
  int? _selectionEndCol;
  bool _isSelecting = false;
  bool _isEditing = false;

  // Column sorting
  String? _sortColumn;
  bool _sortAscending = true;

  // Cell size constants
  final double _defaultColumnWidth = 150.0;
  final double _defaultRowHeight = 40.0;
  final double _headerHeight = 50.0;
  final double _rowHeaderWidth = 60.0;

  @override
  void initState() {
    super.initState();
    _initializeDatabase();

    // Add keyboard listener
    _gridFocusNode.addListener(() {
      if (_gridFocusNode.hasFocus) {
        // Grid can receive keyboard events
      }
    });

    // Add editing focus listener
    _editingFocusNode.addListener(() {
      if (!_editingFocusNode.hasFocus && _isEditing) {
        _saveEdit();
      }
    });
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _searchController.dispose();
    _editingController.dispose();
    _gridFocusNode.dispose();
    _editingFocusNode.dispose();
    _tabController?.dispose();
    super.dispose();
  }

  Future<void> _initializeDatabase() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await _dbService.initialize();
      await _loadOrCreateSection();
    } catch (e) {
      setState(() {
        _error = 'Error initializing database: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadOrCreateSection() async {
    try {
      // Try to find existing section
      final sections = await _dbService.getSections();
      _section = sections.firstWhere(
        (s) => s.name == widget.sectionId,
        orElse: () => throw Exception('Section not found'),
      );
    } catch (e) {
      // Create new section if it doesn't exist
      _section = await _dbService.createSection(
        name: widget.sectionId,
        displayName: widget.sectionName,
        description: 'Auto-created section for ${widget.sectionName}',
        color: widget.themeColor.toString(),
      );
    }

    await _loadSubsections();
  }

  Future<void> _loadSubsections() async {
    if (_section == null) return;

    try {
      _subsections = await _dbService.getSubsections(_section!.id);

      if (_subsections.isEmpty) {
        // Create a default subsection
        await _createDefaultSubsection();
      } else {
        _setupTabs();
        await _loadCurrentSubsectionData();
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading subsections: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _createDefaultSubsection() async {
    if (_section == null) return;

    try {
      final defaultColumns = [
        ColumnDefinition(
          name: 'name',
          displayName: 'Name',
          type: ColumnType.text,
          isRequired: true,
        ),
        ColumnDefinition(
          name: 'description',
          displayName: 'Description',
          type: ColumnType.text,
        ),
        ColumnDefinition(
          name: 'manufacturer',
          displayName: 'Manufacturer',
          type: ColumnType.text,
        ),
        ColumnDefinition(
          name: 'model',
          displayName: 'Model',
          type: ColumnType.text,
        ),
        ColumnDefinition(
          name: 'price',
          displayName: 'Price',
          type: ColumnType.currency,
        ),
      ];

      final subsection = await _dbService.createSubsection(
        sectionId: _section!.id,
        name: 'main_data',
        displayName: 'Main Data',
        columns: defaultColumns,
        description: 'Main data table for ${widget.sectionName}',
      );

      _subsections = [subsection];
      _setupTabs();
      await _loadCurrentSubsectionData();
    } catch (e) {
      setState(() {
        _error = 'Error creating default subsection: $e';
        _isLoading = false;
      });
    }
  }

  void _setupTabs() {
    if (_subsections.isNotEmpty) {
      _tabController?.dispose();
      _tabController = TabController(
        length: _subsections.length,
        vsync: this,
      );

      _tabController!.addListener(() {
        if (!_tabController!.indexIsChanging) {
          _loadCurrentSubsectionData();
        }
      });

      _currentSubsection = _subsections.first;
    }
  }

  Future<void> _loadCurrentSubsectionData() async {
    if (_section == null || _currentSubsection == null) return;

    final tabIndex = _tabController?.index ?? 0;
    if (tabIndex < _subsections.length) {
      _currentSubsection = _subsections[tabIndex];
    }

    try {
      final data = await _dbService.getRows(
        sectionName: _section!.name,
        tableName: _currentSubsection!.name,
      );

      setState(() {
        _items = data;
        _filteredItems = List.from(data);
        _columns = _currentSubsection!.columns;

        // Initialize column widths
        _columnWidths.clear();
        for (var column in _columns) {
          _columnWidths[column.name] = _defaultColumnWidth;
        }

        // Initialize row heights
        _rowHeights = List.generate(_items.length, (_) => _defaultRowHeight);

        _isLoading = false;
      });

      // Apply sorting if active
      if (_sortColumn != null) {
        _sortData();
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(_items);
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    setState(() {
      _filteredItems = _items.where((item) {
        // Search in all columns
        for (var column in _columns) {
          final value = item[column.name]?.toString().toLowerCase() ?? '';
          if (value.contains(lowercaseQuery)) {
            return true;
          }
        }
        return false;
      }).toList();
    });
  }

  void _sortData() {
    if (_sortColumn == null) return;

    setState(() {
      _filteredItems.sort((a, b) {
        final aValue = a[_sortColumn]?.toString() ?? '';
        final bValue = b[_sortColumn]?.toString() ?? '';

        // Try to parse as numbers if possible
        final aNum = double.tryParse(aValue);
        final bNum = double.tryParse(bValue);

        if (aNum != null && bNum != null) {
          return _sortAscending ? aNum.compareTo(bNum) : bNum.compareTo(aNum);
        }

        return _sortAscending ? aValue.compareTo(bValue) : bValue.compareTo(aValue);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: widget.themeColor),
            const SizedBox(height: 16),
            Text('Loading ${widget.sectionName}...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red.shade300),
            const SizedBox(height: 16),
            Text(_error!, style: TextStyle(color: Colors.red.shade700)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeDatabase,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Subsection tabs
        if (_subsections.length > 1)
          Container(
            color: Colors.grey.shade100,
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: widget.themeColor,
              unselectedLabelColor: Colors.grey.shade600,
              indicatorColor: widget.themeColor,
              tabs: _subsections.map((subsection) => Tab(
                text: subsection.displayName,
                icon: const Icon(Icons.table_chart, size: 16),
              )).toList(),
            ),
          ),

        // Toolbar
        _buildToolbar(),

        // Search bar
        _buildSearchBar(),

        // Excel grid
        Expanded(
          child: _buildExcelGrid(),
        ),
      ],
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: widget.themeColor.withOpacity(0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          ElevatedButton.icon(
            onPressed: _importFromExcel,
            icon: const Icon(Icons.upload_file, size: 16),
            label: const Text('Import Excel'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.themeColor,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: _exportToExcel,
            icon: const Icon(Icons.download, size: 16),
            label: const Text('Export Excel'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.themeColor,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: _addSubsection,
            icon: const Icon(Icons.add_box, size: 16),
            label: const Text('Add Subsection'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.themeColor,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: _manageColumns,
            icon: const Icon(Icons.settings, size: 16),
            label: const Text('Manage Columns'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.themeColor,
              foregroundColor: Colors.white,
            ),
          ),
          const Spacer(),
          ElevatedButton.icon(
            onPressed: _addRow,
            icon: const Icon(Icons.add, size: 16),
            label: const Text('Add Row'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _filterData('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onChanged: _filterData,
      ),
    );
  }

  Widget _buildExcelGrid() {
    if (_filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.table_chart, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'No data available',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text('Add some data to get started'),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _addRow,
              icon: const Icon(Icons.add),
              label: const Text('Add First Row'),
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return KeyboardListener(
      focusNode: _gridFocusNode,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: () => _gridFocusNode.requestFocus(),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            children: [
              // Header row
              _buildHeaderRow(),

              // Data rows
              Expanded(
                child: _buildDataRows(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderRow() {
    return Container(
      height: _headerHeight,
      decoration: BoxDecoration(
        color: widget.themeColor.withOpacity(0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          // Row header
          Container(
            width: _rowHeaderWidth,
            decoration: BoxDecoration(
              border: Border(right: BorderSide(color: Colors.grey.shade300)),
            ),
            child: const Center(
              child: Text('#', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          ),

          // Column headers
          Expanded(
            child: SingleChildScrollView(
              controller: _horizontalController,
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _columns.map((column) => _buildColumnHeader(column)).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeader(ColumnDefinition column) {
    final isSelected = _selectedCol != null &&
                      _selectedCol! < _columns.length &&
                      _columns[_selectedCol!].name == column.name;

    return GestureDetector(
      onTap: () => _sortByColumn(column.name),
      child: Container(
        width: _columnWidths[column.name] ?? _defaultColumnWidth,
        height: _headerHeight,
        decoration: BoxDecoration(
          color: isSelected ? widget.themeColor.withOpacity(0.2) : null,
          border: Border(right: BorderSide(color: Colors.grey.shade300)),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          children: [
            Expanded(
              child: Text(
                column.displayName,
                style: const TextStyle(fontWeight: FontWeight.bold),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (_sortColumn == column.name)
              Icon(
                _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                size: 16,
                color: widget.themeColor,
              ),
            Icon(
              _getColumnTypeIcon(column.type),
              size: 14,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataRows() {
    return SingleChildScrollView(
      controller: _verticalController,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row numbers
          Column(
            children: List.generate(_filteredItems.length, (index) => _buildRowHeader(index)),
          ),

          // Data cells
          Expanded(
            child: SingleChildScrollView(
              controller: _horizontalController,
              scrollDirection: Axis.horizontal,
              child: Column(
                children: List.generate(_filteredItems.length, (rowIndex) => _buildDataRow(rowIndex)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRowHeader(int rowIndex) {
    final isSelected = _selectedRow == rowIndex;

    return Container(
      width: _rowHeaderWidth,
      height: _rowHeights.isNotEmpty ? _rowHeights[rowIndex] : _defaultRowHeight,
      decoration: BoxDecoration(
        color: isSelected ? widget.themeColor.withOpacity(0.2) : Colors.grey.shade50,
        border: Border(
          right: BorderSide(color: Colors.grey.shade300),
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Center(
        child: Text(
          '${rowIndex + 1}',
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? widget.themeColor : Colors.grey.shade700,
          ),
        ),
      ),
    );
  }

  Widget _buildDataRow(int rowIndex) {
    return Row(
      children: _columns.asMap().entries.map((entry) {
        final colIndex = entry.key;
        final column = entry.value;
        return _buildDataCell(rowIndex, colIndex, column);
      }).toList(),
    );
  }

  Widget _buildDataCell(int rowIndex, int colIndex, ColumnDefinition column) {
    final item = _filteredItems[rowIndex];
    final value = item[column.name]?.toString() ?? '';
    final isSelected = _selectedRow == rowIndex && _selectedCol == colIndex;
    final isInSelection = _isInSelection(rowIndex, colIndex);
    final isEditing = _isEditing && isSelected;

    return GestureDetector(
      onTap: () => _selectCell(rowIndex, colIndex),
      onDoubleTap: () => _startEditing(),
      child: Container(
        width: _columnWidths[column.name] ?? _defaultColumnWidth,
        height: _rowHeights.isNotEmpty ? _rowHeights[rowIndex] : _defaultRowHeight,
        decoration: BoxDecoration(
          color: isSelected
              ? widget.themeColor.withOpacity(0.3)
              : isInSelection
                  ? widget.themeColor.withOpacity(0.1)
                  : Colors.white,
          border: Border(
            right: BorderSide(color: Colors.grey.shade300),
            bottom: BorderSide(color: Colors.grey.shade300),
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: isEditing ? _buildEditingCell() : _buildDisplayCell(value, column),
      ),
    );
  }

  Widget _buildDisplayCell(String value, ColumnDefinition column) {
    return Align(
      alignment: _getCellAlignment(column.type),
      child: Text(
        value,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          color: _getCellTextColor(column.type),
        ),
      ),
    );
  }

  Widget _buildEditingCell() {
    return TextField(
      controller: _editingController,
      focusNode: _editingFocusNode,
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.zero,
      ),
      onSubmitted: (_) => _saveEdit(),
      onEditingComplete: () => _saveEdit(),
    );
  }

  // Cell selection and editing methods
  void _selectCell(int row, int col) {
    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    setState(() {
      _selectedRow = row;
      _selectedCol = col;
      _selectionStartRow = row;
      _selectionStartCol = col;
      _selectionEndRow = row;
      _selectionEndCol = col;
      _isSelecting = false;

      // If we're already editing, save the edit first
      if (_isEditing) {
        _saveEdit();
      }
    });

    _gridFocusNode.requestFocus();
  }

  void _startEditing() {
    if (_selectedRow == null || _selectedCol == null) {
      return;
    }

    final row = _selectedRow!;
    final col = _selectedCol!;

    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    final column = _columns[col];
    final value = _filteredItems[row][column.name]?.toString() ?? '';

    setState(() {
      _isEditing = true;
      _editingController.text = value;
    });

    _editingFocusNode.requestFocus();
    _editingController.selection = TextSelection(
      baseOffset: 0,
      extentOffset: _editingController.text.length,
    );
  }

  bool _isInSelection(int row, int col) {
    if (_selectionStartRow == null || _selectionStartCol == null ||
        _selectionEndRow == null || _selectionEndCol == null) {
      return false;
    }

    final startRow = math.min(_selectionStartRow!, _selectionEndRow!);
    final endRow = math.max(_selectionStartRow!, _selectionEndRow!);
    final startCol = math.min(_selectionStartCol!, _selectionEndCol!);
    final endCol = math.max(_selectionStartCol!, _selectionEndCol!);

    return row >= startRow && row <= endRow && col >= startCol && col <= endCol;
  }

  void _sortByColumn(String columnName) {
    setState(() {
      if (_sortColumn == columnName) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = columnName;
        _sortAscending = true;
      }
    });
    _sortData();
  }

  // Keyboard handling
  void _handleKeyEvent(KeyEvent event) {
    if (event is! KeyDownEvent) return;

    // If editing, handle editing-specific keys
    if (_isEditing) {
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        _cancelEdit();
      } else if (event.logicalKey == LogicalKeyboardKey.enter ||
                event.logicalKey == LogicalKeyboardKey.tab) {
        _saveEdit();
        // Move to next cell
        if (event.logicalKey == LogicalKeyboardKey.tab) {
          _moveSelection(0, HardwareKeyboard.instance.isShiftPressed ? -1 : 1);
        } else if (event.logicalKey == LogicalKeyboardKey.enter) {
          _moveSelection(1, 0);
        }
      }
      return;
    }

    // Handle navigation keys
    if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
      _moveSelection(-1, 0);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
      _moveSelection(1, 0);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
      _moveSelection(0, -1);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
      _moveSelection(0, 1);
    } else if (event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.f2) {
      _startEditing();
    }
  }

  void _moveSelection(int rowDelta, int colDelta) {
    if (_selectedRow == null || _selectedCol == null) {
      _selectCell(0, 0);
      return;
    }

    final newRow = math.max(0, math.min(_filteredItems.length - 1, _selectedRow! + rowDelta));
    final newCol = math.max(0, math.min(_columns.length - 1, _selectedCol! + colDelta));

    _selectCell(newRow, newCol);
  }

  void _cancelEdit() {
    setState(() {
      _isEditing = false;
    });
  }

  // Helper methods
  IconData _getColumnTypeIcon(ColumnType type) {
    switch (type) {
      case ColumnType.text:
        return Icons.text_fields;
      case ColumnType.integer:
        return Icons.numbers;
      case ColumnType.real:
        return Icons.numbers;
      case ColumnType.boolean:
        return Icons.check_box;
      case ColumnType.date:
        return Icons.calendar_today;
      case ColumnType.datetime:
        return Icons.access_time;
      case ColumnType.currency:
        return Icons.attach_money;
      case ColumnType.email:
        return Icons.email;
      case ColumnType.url:
        return Icons.link;
      case ColumnType.phone:
        return Icons.phone;
      case ColumnType.json:
        return Icons.code;
    }
  }

  Alignment _getCellAlignment(ColumnType type) {
    switch (type) {
      case ColumnType.integer:
      case ColumnType.real:
      case ColumnType.currency:
        return Alignment.centerRight;
      case ColumnType.boolean:
        return Alignment.center;
      default:
        return Alignment.centerLeft;
    }
  }

  Color _getCellTextColor(ColumnType type) {
    switch (type) {
      case ColumnType.currency:
        return Colors.green.shade700;
      case ColumnType.email:
        return Colors.blue.shade700;
      case ColumnType.url:
        return Colors.blue.shade700;
      case ColumnType.boolean:
        return Colors.purple.shade700;
      default:
        return Colors.black87;
    }
  }

  // Action methods implementation
  Future<void> _saveEdit() async {
    if (!_isEditing || _selectedRow == null || _selectedCol == null ||
        _section == null || _currentSubsection == null) {
      return;
    }

    final row = _selectedRow!;
    final col = _selectedCol!;

    if (row < 0 || row >= _filteredItems.length || col < 0 || col >= _columns.length) {
      return;
    }

    final column = _columns[col];
    final itemId = _filteredItems[row]['_id'] as int?;
    final newValue = _editingController.text;

    if (itemId == null) return;

    try {
      // Update in database
      await _dbService.updateRow(
        sectionName: _section!.name,
        tableName: _currentSubsection!.name,
        id: itemId,
        data: {column.name: newValue},
      );

      // Update local data
      setState(() {
        _filteredItems[row][column.name] = newValue;

        // Also update in _items
        final index = _items.indexWhere((item) => item['_id'] == itemId);
        if (index >= 0) {
          _items[index][column.name] = newValue;
        }

        _isEditing = false;
      });

      // Re-sort if needed
      if (_sortColumn != null) {
        _sortData();
      }
    } catch (e) {
      _showSnackBar('Error updating cell: $e', isError: true);
      setState(() {
        _isEditing = false;
      });
    }
  }

  Future<void> _addRow() async {
    if (_section == null || _currentSubsection == null) return;

    try {
      // Create a new row with default values
      final newRowData = <String, dynamic>{};
      for (final column in _columns) {
        if (column.name != '_id') {
          newRowData[column.name] = _getDefaultValue(column.type);
        }
      }

      // Insert into database
      final newId = await _dbService.insertRow(
        sectionName: _section!.name,
        tableName: _currentSubsection!.name,
        data: newRowData,
      );

      // Add to local data
      final newItem = {'_id': newId, ...newRowData};
      setState(() {
        _items.add(newItem);
        _filteredItems.add(newItem);
        _rowHeights.add(_defaultRowHeight);
      });

      _showSnackBar('Row added successfully');
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  dynamic _getDefaultValue(ColumnType type) {
    switch (type) {
      case ColumnType.text:
      case ColumnType.email:
      case ColumnType.url:
      case ColumnType.phone:
        return '';
      case ColumnType.integer:
        return 0;
      case ColumnType.real:
      case ColumnType.currency:
        return 0.0;
      case ColumnType.boolean:
        return false;
      case ColumnType.date:
      case ColumnType.datetime:
        return DateTime.now().toIso8601String();
      case ColumnType.json:
        return '{}';
    }
  }

  Future<void> _importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;

        _showSnackBar('Importing Excel file...');

        // Use the existing Excel import service
        final excelService = ExcelImportExportService();
        final operation = await excelService.importExcelAsSection(
          sectionDisplayName: '${widget.sectionName}_Import_${DateTime.now().millisecondsSinceEpoch}',
          filePath: filePath,
          description: 'Imported data for ${widget.sectionName}',
        );

        if (operation.status == OperationStatus.completed) {
          _showSnackBar('Successfully imported ${operation.processedRows} rows');
          await _loadCurrentSubsectionData();
        } else {
          _showSnackBar('Import failed: ${operation.errorMessage}', isError: true);
        }
      }
    } catch (e) {
      _showSnackBar('Error importing Excel: $e', isError: true);
    }
  }

  Future<void> _exportToExcel() async {
    try {
      if (_section == null) return;

      _showSnackBar('Exporting to Excel...');

      // Use the existing Excel export service
      final excelService = ExcelImportExportService();
      final operation = await excelService.exportSectionToExcel(
        sectionId: _section!.id,
      );

      if (operation.status == OperationStatus.completed) {
        _showSnackBar('Successfully exported ${operation.processedRows} rows to Excel');
      } else {
        _showSnackBar('Export failed: ${operation.errorMessage}', isError: true);
      }
    } catch (e) {
      _showSnackBar('Error exporting Excel: $e', isError: true);
    }
  }

  Future<void> _addSubsection() async {
    final nameController = TextEditingController();

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Subsection'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(
            labelText: 'Subsection Name',
            hintText: 'e.g., Main Data, Specifications',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(nameController.text),
            child: const Text('Create'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty && _section != null) {
      try {
        final defaultColumns = [
          ColumnDefinition(
            name: 'name',
            displayName: 'Name',
            type: ColumnType.text,
            isRequired: true,
          ),
          ColumnDefinition(
            name: 'description',
            displayName: 'Description',
            type: ColumnType.text,
          ),
        ];

        final subsection = await _dbService.createSubsection(
          sectionId: _section!.id,
          name: result.toLowerCase().replaceAll(' ', '_'),
          displayName: result,
          columns: defaultColumns,
          description: 'Subsection for $result',
        );

        setState(() {
          _subsections.add(subsection);
          _setupTabs();
        });

        _showSnackBar('Subsection "$result" created successfully');
      } catch (e) {
        _showSnackBar('Error creating subsection: $e', isError: true);
      }
    }
  }

  Future<void> _manageColumns() async {
    if (_currentSubsection == null || _section == null) {
      _showSnackBar('No subsection selected', isError: true);
      return;
    }

    final result = await showDialog<List<ColumnDefinition>>(
      context: context,
      builder: (context) => _ColumnManagementDialog(
        subsection: _currentSubsection!,
        section: _section!,
      ),
    );

    if (result != null) {
      try {
        // Update the subsection columns
        await _dbService.updateSubsectionColumns(
          sectionName: _section!.name,
          subsectionId: _currentSubsection!.id,
          newColumns: result,
        );

        // Reload the subsection data
        await _loadSubsections();
        _showSnackBar('Columns updated successfully');
      } catch (e) {
        _showSnackBar('Error updating columns: $e', isError: true);
      }
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: isError ? 5 : 3),
      ),
    );
  }
}

// Column Management Dialog
class _ColumnManagementDialog extends StatefulWidget {
  final Subsection subsection;
  final Section section;

  const _ColumnManagementDialog({
    required this.subsection,
    required this.section,
  });

  @override
  State<_ColumnManagementDialog> createState() => _ColumnManagementDialogState();
}

class _ColumnManagementDialogState extends State<_ColumnManagementDialog> {
  List<ColumnDefinition> _columns = [];

  @override
  void initState() {
    super.initState();
    _columns = List.from(widget.subsection.columns);
  }

  void _addColumn() {
    setState(() {
      _columns.add(ColumnDefinition(
        name: 'new_column_${_columns.length}',
        displayName: 'New Column ${_columns.length}',
        type: ColumnType.text,
      ));
    });
  }

  void _removeColumn(int index) {
    if (_columns.length <= 1) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot delete the last column'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _columns.removeAt(index);
    });
  }

  void _editColumn(int index) {
    showDialog(
      context: context,
      builder: (context) => _ColumnEditDialog(
        column: _columns[index],
        onSave: (updatedColumn) {
          setState(() {
            _columns[index] = updatedColumn;
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Manage Columns - ${widget.subsection.displayName}'),
      content: SizedBox(
        width: 600,
        height: 500,
        child: Column(
          children: [
            // Header with add button
            Row(
              children: [
                Text(
                  'Columns (${_columns.length})',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _addColumn,
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Add Column'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Columns list
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _columns.length,
                  itemBuilder: (context, index) {
                    final column = _columns[index];
                    final isLastColumn = _columns.length == 1;

                    return ListTile(
                      leading: Icon(
                        _getColumnTypeIcon(column.type),
                        color: Theme.of(context).primaryColor,
                      ),
                      title: Text(
                        column.displayName,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      subtitle: Text(
                        '${column.name} • ${column.type.displayName}${column.isRequired ? ' • Required' : ''}${column.isUnique ? ' • Unique' : ''}',
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit, size: 18),
                            onPressed: () => _editColumn(index),
                            tooltip: 'Edit column',
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.delete,
                              size: 18,
                              color: isLastColumn ? Colors.grey : Colors.red,
                            ),
                            onPressed: isLastColumn ? null : () => _removeColumn(index),
                            tooltip: isLastColumn ? 'Cannot delete last column' : 'Delete column',
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(_columns),
          child: const Text('Save Changes'),
        ),
      ],
    );
  }

  IconData _getColumnTypeIcon(ColumnType type) {
    switch (type) {
      case ColumnType.text:
        return Icons.text_fields;
      case ColumnType.integer:
        return Icons.numbers;
      case ColumnType.real:
        return Icons.numbers;
      case ColumnType.boolean:
        return Icons.check_box;
      case ColumnType.date:
        return Icons.calendar_today;
      case ColumnType.datetime:
        return Icons.access_time;
      case ColumnType.currency:
        return Icons.attach_money;
      case ColumnType.email:
        return Icons.email;
      case ColumnType.url:
        return Icons.link;
      case ColumnType.phone:
        return Icons.phone;
      case ColumnType.json:
        return Icons.code;
    }
  }
}

// Column Edit Dialog
class _ColumnEditDialog extends StatefulWidget {
  final ColumnDefinition column;
  final Function(ColumnDefinition) onSave;

  const _ColumnEditDialog({
    required this.column,
    required this.onSave,
  });

  @override
  State<_ColumnEditDialog> createState() => _ColumnEditDialogState();
}

class _ColumnEditDialogState extends State<_ColumnEditDialog> {
  late TextEditingController _nameController;
  late TextEditingController _displayNameController;
  late TextEditingController _descriptionController;
  late ColumnType _selectedType;
  late bool _isRequired;
  late bool _isUnique;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.column.name);
    _displayNameController = TextEditingController(text: widget.column.displayName);
    _descriptionController = TextEditingController(text: widget.column.description ?? '');
    _selectedType = widget.column.type;
    _isRequired = widget.column.isRequired;
    _isUnique = widget.column.isUnique;

    _displayNameController.addListener(_updateNameFromDisplayName);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _displayNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _updateNameFromDisplayName() {
    final displayName = _displayNameController.text;
    final sanitizedName = displayName
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .replaceAll(RegExp(r'\s+'), '_');
    _nameController.text = sanitizedName;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Column'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'Display Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Internal Name',
                border: OutlineInputBorder(),
              ),
              readOnly: true,
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<ColumnType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Type',
                border: OutlineInputBorder(),
              ),
              items: ColumnType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: const Text('Required'),
              value: _isRequired,
              onChanged: (bool? value) {
                setState(() {
                  _isRequired = value ?? false;
                });
              },
            ),
            CheckboxListTile(
              title: const Text('Unique'),
              value: _isUnique,
              onChanged: (bool? value) {
                setState(() {
                  _isUnique = value ?? false;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final updatedColumn = ColumnDefinition(
              name: _nameController.text.trim(),
              displayName: _displayNameController.text.trim(),
              type: _selectedType,
              description: _descriptionController.text.trim().isEmpty
                  ? null
                  : _descriptionController.text.trim(),
              isRequired: _isRequired,
              isUnique: _isUnique,
            );
            widget.onSave(updatedColumn);
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
