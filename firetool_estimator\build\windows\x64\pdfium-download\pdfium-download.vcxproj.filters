﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\4825291e04def8cd1c042415c21302c7\pdfium-download-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\4825291e04def8cd1c042415c21302c7\pdfium-download-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\4825291e04def8cd1c042415c21302c7\pdfium-download-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\4825291e04def8cd1c042415c21302c7\pdfium-download-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\4825291e04def8cd1c042415c21302c7\pdfium-download-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\4825291e04def8cd1c042415c21302c7\pdfium-download-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\4825291e04def8cd1c042415c21302c7\pdfium-download-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\4825291e04def8cd1c042415c21302c7\pdfium-download-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\1fee7de21b263910e79db2041afcac8a\pdfium-download-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\a72a7616a0f57b9d9118ff3e8b70a8fc\pdfium-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\Firetool\firetool_estimator\build\windows\x64\pdfium-download\CMakeFiles\pdfium-download" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{647E8168-CCA8-31DD-970B-32624B81E755}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
