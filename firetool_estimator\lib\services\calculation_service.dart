
class CalculationService {
  // Fire Alarm System Calculations
  
  /// Calculate the number of smoke detectors needed based on area
  int calculateSmokeDetectors(double areaInSquareFeet) {
    // Typical coverage area for a smoke detector is around 900 sq ft
    // but we'll use a more conservative 800 sq ft for safety
    return (areaInSquareFeet / 800).ceil();
  }
  
  /// Calculate the number of heat detectors needed based on area
  int calculateHeatDetectors(double areaInSquareFeet) {
    // Typical coverage area for a heat detector is around 400 sq ft
    return (areaInSquareFeet / 400).ceil();
  }
  
  /// Calculate the number of pull stations needed based on building parameters
  int calculatePullStations({
    required int numberOfExits,
    required int numberOfFloors,
    bool isHighRise = false,
  }) {
    // At minimum, one pull station per exit
    int pullStations = numberOfExits;
    
    // Add additional pull stations for high-rise buildings
    if (isHighRise) {
      pullStations += numberOfFloors;
    }
    
    return pullStations;
  }
  
  /// Calculate the number of notification devices needed
  int calculateNotificationDevices({
    required double areaInSquareFeet,
    required int numberOfRooms,
    bool isAudibleOnly = false,
  }) {
    // Base calculation on area (one device per 1000 sq ft)
    int devices = (areaInSquareFeet / 1000).ceil();
    
    // Add devices for rooms that need coverage
    devices += (numberOfRooms / 2).ceil();
    
    // If visual notification is also required, we might need more devices
    if (!isAudibleOnly) {
      devices = (devices * 1.5).ceil();
    }
    
    return devices;
  }
  
  /// Calculate the amount of wire needed for fire alarm system
  double calculateWireLength({
    required double buildingLength,
    required double buildingWidth,
    required int numberOfFloors,
    required int deviceCount,
    double complexityFactor = 1.0, // 1.0 = simple, 2.0 = complex
  }) {
    // Base calculation: perimeter of building × number of floors
    double baseWireLength = 2 * (buildingLength + buildingWidth) * numberOfFloors;
    
    // Add wire for devices (average 30ft per device)
    double deviceWireLength = deviceCount * 30.0;
    
    // Apply complexity factor
    return (baseWireLength + deviceWireLength) * complexityFactor;
  }
  
  // Sprinkler System Calculations
  
  /// Calculate the number of sprinkler heads needed
  int calculateSprinklerHeads(double areaInSquareFeet, String hazardLevel) {
    // Coverage area per sprinkler head depends on hazard level
    double coverageArea;
    
    switch (hazardLevel.toLowerCase()) {
      case 'light':
        coverageArea = 225.0; // 15ft × 15ft
        break;
      case 'ordinary':
        coverageArea = 130.0; // approx 11.5ft × 11.5ft
        break;
      case 'high':
        coverageArea = 100.0; // 10ft × 10ft
        break;
      default:
        coverageArea = 130.0; // default to ordinary hazard
    }
    
    return (areaInSquareFeet / coverageArea).ceil();
  }
  
  /// Calculate the pipe sizes and lengths for sprinkler system
  Map<String, double> calculateSprinklerPiping({
    required double areaInSquareFeet,
    required int sprinklerCount,
    required String hazardLevel,
  }) {
    // This is a simplified calculation
    // In reality, hydraulic calculations would be needed
    
    // Estimate main pipe length (building perimeter)
    double buildingPerimeter = 4 * sqrt(areaInSquareFeet);
    
    // Estimate branch lines based on sprinkler count
    double branchLinePiping = sprinklerCount * 8.0; // Average 8ft per sprinkler
    
    // Calculate pipe sizes based on hazard level
    double mainPipeSize;
    double branchPipeSize;
    
    switch (hazardLevel.toLowerCase()) {
      case 'light':
        mainPipeSize = 3.0; // 3 inch
        branchPipeSize = 1.0; // 1 inch
        break;
      case 'ordinary':
        mainPipeSize = 4.0; // 4 inch
        branchPipeSize = 1.25; // 1.25 inch
        break;
      case 'high':
        mainPipeSize = 6.0; // 6 inch
        branchPipeSize = 1.5; // 1.5 inch
        break;
      default:
        mainPipeSize = 4.0;
        branchPipeSize = 1.25;
    }
    
    return {
      'mainPipeLength': buildingPerimeter,
      'branchPipeLength': branchLinePiping,
      'mainPipeSize': mainPipeSize,
      'branchPipeSize': branchPipeSize,
    };
  }
  
  // Fire Suppression System Calculations
  
  /// Calculate the amount of suppression agent needed
  double calculateSuppressionAgent({
    required double volumeInCubicFeet,
    required String agentType,
  }) {
    // Different agents have different requirements
    double agentDensity;
    
    switch (agentType.toLowerCase()) {
      case 'co2':
        agentDensity = 0.06; // 6% concentration
        break;
      case 'fm200':
        agentDensity = 0.08; // 8% concentration
        break;
      case 'novec1230':
        agentDensity = 0.05; // 5% concentration
        break;
      default:
        agentDensity = 0.06;
    }
    
    return volumeInCubicFeet * agentDensity;
  }
  
  // Labor Calculations
  
  /// Calculate installation labor hours
  double calculateInstallationHours({
    required int deviceCount,
    required double wireLength,
    required int sprinklerCount,
    double pipingLength = 0,
    double complexityFactor = 1.0,
  }) {
    // Base labor calculations
    double deviceInstallHours = deviceCount * 0.5; // 30 minutes per device
    double wireInstallHours = wireLength / 100 * 2; // 2 hours per 100ft of wire
    double sprinklerInstallHours = sprinklerCount * 0.75; // 45 minutes per sprinkler
    double pipingInstallHours = pipingLength / 10 * 1; // 1 hour per 10ft of pipe
    
    // Apply complexity factor
    return (deviceInstallHours + wireInstallHours + 
            sprinklerInstallHours + pipingInstallHours) * complexityFactor;
  }
  
  /// Calculate testing and commissioning hours
  double calculateTestingHours({
    required int deviceCount,
    required int sprinklerCount,
    bool isAddressable = true,
  }) {
    // Base testing calculations
    double deviceTestingHours = deviceCount * 0.25; // 15 minutes per device
    double sprinklerTestingHours = sprinklerCount * 0.1; // 6 minutes per sprinkler
    
    // Addressable systems take longer to program and test
    double programmingFactor = isAddressable ? 1.5 : 1.0;
    
    return (deviceTestingHours + sprinklerTestingHours) * programmingFactor;
  }
}

// Helper function for square root (used in pipe calculations)
double sqrt(double value) {
  return value <= 0 ? 0 : value.sqrt();
}

// Extension method to calculate square root
extension on double {
  double sqrt() {
    // Newton's method for square root
    double x = this;
    double y = 1.0;
    double e = 0.000001; // precision
    while (x - y > e) {
      x = (x + y) / 2;
      y = this / x;
    }
    return x;
  }
}
