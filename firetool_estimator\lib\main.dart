import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'constants/app_constants.dart';
import 'services/project_provider.dart';
import 'services/auth_service.dart';
import 'services/local_sql_service.dart';
import 'screens/home_screen.dart';
import 'screens/project_list_screen.dart';
import 'screens/new_project_screen.dart';
import 'screens/project_detail_screen.dart';
import 'screens/login_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/pricing_items_screen.dart';
import 'screens/new_admin_dashboard_screen.dart';
import 'screens/excel_database_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize sqflite_ffi for desktop
  sqfliteFfiInit();
  databaseFactory = databaseFactoryFfi;

  // Initialize local SQL database
  try {
    final localSqlService = LocalSqlService();
    await localSqlService.database;
    debugPrint("Local SQL database initialized successfully");
  } catch (e) {
    debugPrint("Error initializing local SQL database: $e");
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Auth service
        ChangeNotifierProvider(create: (context) => AuthService()),

        // Local SQL service
        Provider(create: (context) => LocalSqlService()),

        // Project provider
        ChangeNotifierProxyProvider<AuthService, ProjectProvider>(
          create: (context) => ProjectProvider(),
          update: (context, auth, previousProjectProvider) =>
            previousProjectProvider!..updateAuth(auth),
        ),
      ],
      child: Consumer<AuthService>(
        builder: (context, authService, _) {
          return MaterialApp(
            title: AppConstants.appName,
            theme: AppConstants.getTheme(),
            initialRoute: '/',
            routes: {
              '/': (context) => const SplashScreen(),
              '/login': (context) => const LoginScreen(),
              '/home': (context) => const HomeScreen(),
              '/projects': (context) => const ProjectListScreen(),
              '/new_project': (context) => const NewProjectScreen(),
              '/project_detail': (context) => const ProjectDetailScreen(),
              '/pricing_items': (context) => const PricingItemsScreen(),
              '/admin': (context) => const NewAdminDashboardScreen(),
              '/database_admin': (context) => const ExcelDatabaseScreen(),
              '/excel_database': (context) => const ExcelDatabaseScreen(),
            },
          );
        },
      ),
    );
  }
}
