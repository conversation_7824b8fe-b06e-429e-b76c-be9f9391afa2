import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../services/sqlite_runtime_schema.dart';
import '../constants/app_constants.dart';

class SQLiteSimpleDemoScreen extends StatefulWidget {
  const SQLiteSimpleDemoScreen({super.key});

  @override
  State<SQLiteSimpleDemoScreen> createState() => _SQLiteSimpleDemoScreenState();
}

class _SQLiteSimpleDemoScreenState extends State<SQLiteSimpleDemoScreen> {
  late Future<Database> _dbFuture;
  List<Map<String, dynamic>> _tableData = [];
  List<String> _columns = [];
  bool _isLoading = false;
  String? _message;
  final String _tableName = 'demo_table';
  
  @override
  void initState() {
    super.initState();
    _dbFuture = _initDatabase();
    _loadData();
  }
  
  Future<Database> _initDatabase() async {
    // Get the database path
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'schema_demo.db');
    
    // Open the database
    return openDatabase(
      path,
      version: 1,
      onCreate: (Database db, int version) async {
        // Create a simple table
        await db.execute('''
          CREATE TABLE $_tableName (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            age INTEGER
          )
        ''');
        
        // Insert some sample data
        await db.insert(_tableName, {'name': 'John Doe', 'age': 30});
        await db.insert(_tableName, {'name': 'Jane Smith', 'age': 25});
      },
    );
  }
  
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _message = null;
    });
    
    try {
      final db = await _dbFuture;
      
      // Get table info
      final tableInfo = await db.rawQuery('PRAGMA table_info($_tableName)');
      
      // Extract column names
      _columns = tableInfo.map((col) => col['name'] as String).toList();
      
      // Get all data
      final data = await db.query(_tableName);
      
      setState(() {
        _tableData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _message = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _addNotesColumn() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final db = await _dbFuture;
      
      // Add the notes column
      final success = await SQLiteRuntimeSchema.addColumn(
        db,
        _tableName,
        'notes',
        'TEXT',
      );
      
      if (success) {
        setState(() {
          _message = 'Notes column added successfully';
        });
        
        // Reload data
        await _loadData();
      } else {
        setState(() {
          _message = 'Failed to add notes column';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _message = 'Error adding column: $e';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _updateNoteForFirstRow() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final db = await _dbFuture;
      
      // Make sure we have at least one row
      if (_tableData.isEmpty) {
        setState(() {
          _message = 'No rows to update';
          _isLoading = false;
        });
        return;
      }
      
      // Get the ID of the first row
      final firstRowId = _tableData.first['id'];
      
      // Update the notes column for the first row
      final rowsUpdated = await SQLiteRuntimeSchema.updateCell(
        db,
        _tableName,
        'notes',
        'This is a sample note for the first row',
        'id = ?',
        [firstRowId],
      );
      
      setState(() {
        _message = 'Updated $rowsUpdated row(s)';
      });
      
      // Reload data
      await _loadData();
    } catch (e) {
      setState(() {
        _message = 'Error updating cell: $e';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _dropAgeColumn() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final db = await _dbFuture;
      
      // Drop the age column
      final success = await SQLiteRuntimeSchema.dropColumn(
        db,
        _tableName,
        'age',
      );
      
      if (success) {
        setState(() {
          _message = 'Age column dropped successfully';
        });
        
        // Reload data
        await _loadData();
      } else {
        setState(() {
          _message = 'Failed to drop age column';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _message = 'Error dropping column: $e';
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SQLite Runtime Schema Demo'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Message
                if (_message != null)
                  Container(
                    padding: const EdgeInsets.all(8),
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.info, color: Colors.blue),
                        const SizedBox(width: 8),
                        Expanded(child: Text(_message!)),
                        IconButton(
                          icon: const Icon(Icons.close, size: 16),
                          onPressed: () => setState(() => _message = null),
                        ),
                      ],
                    ),
                  ),
                
                // Buttons
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      ElevatedButton(
                        onPressed: _addNotesColumn,
                        child: const Text('1. Add Notes Column'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _updateNoteForFirstRow,
                        child: const Text('2. Update Note for First Row'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _dropAgeColumn,
                        child: const Text('3. Drop Age Column'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _loadData,
                        child: const Text('Refresh Data'),
                      ),
                    ],
                  ),
                ),
                
                // Table
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: SingleChildScrollView(
                      child: DataTable(
                        columns: [
                          for (final column in _columns)
                            DataColumn(label: Text(column)),
                        ],
                        rows: [
                          for (final row in _tableData)
                            DataRow(
                              cells: [
                                for (final column in _columns)
                                  DataCell(Text(row[column]?.toString() ?? '')),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
