import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:isar/isar.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart' hide Border, BorderStyle;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as p;
import '../models/flexible_table_models.dart';
import '../services/flexible_database_service.dart';
import 'column_type_dialog.dart';

class FlexibleDataGrid extends StatefulWidget {
  final String tableId;
  final Color themeColor;

  const FlexibleDataGrid({
    super.key,
    required this.tableId,
    this.themeColor = Colors.blue,
  });

  @override
  State<FlexibleDataGrid> createState() => _FlexibleDataGridState();
}

class _FlexibleDataGridState extends State<FlexibleDataGrid> {
  final FlexibleDatabaseService _databaseService = FlexibleDatabaseService();
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final FocusNode _gridFocusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  final Map<String, TextEditingController> _cellControllers = {};
  final _uuid = Uuid();

  // Table data
  FlexibleTable? _table;
  List<FlexibleColumn> _columns = [];
  List<FlexibleRow> _rows = [];
  List<FlexibleRow> _filteredRows = [];
  Map<String, Map<String, FlexibleCell>> _cellsByRowAndColumn = {};

  // UI state
  bool _isLoading = true;
  String? _error;
  final bool _isEditing = false;
  String? _editingCellId;
  final bool _isCardView = false;

  // Selection state
  final Set<String> _selectedCells = {}; // Format: "rowId:columnId"
  String? _selectedRowId;
  String? _selectedColumnId;
  bool _isSelecting = false;
  String? _selectionStartRowId;
  String? _selectionStartColumnId;

  @override
  void initState() {
    super.initState();
    _loadData();

    // Add keyboard listener for arrow navigation
    _gridFocusNode.addListener(() {
      if (_gridFocusNode.hasFocus) {
        // This ensures the grid can receive keyboard events
      }
    });
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _gridFocusNode.dispose();
    _searchController.dispose();

    // Dispose all cell controllers
    for (var controller in _cellControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load table
      final table = await _databaseService.getTableById(widget.tableId);
      if (table == null) {
        throw Exception('Table not found');
      }

      // Load columns
      final columns = await _databaseService.getColumnsForTable(widget.tableId);

      // Sort columns by order
      columns.sort((a, b) => a.order.compareTo(b.order));

      // Load rows
      final rows = await _databaseService.getRowsForTable(widget.tableId);

      // Sort rows by order
      rows.sort((a, b) => a.order.compareTo(b.order));

      // Load cells for all rows and columns
      final cellsByRowAndColumn = <String, Map<String, FlexibleCell>>{};

      for (final row in rows) {
        if (row.rowId == null) continue;

        cellsByRowAndColumn[row.rowId!] = {};

        // Load cells for this row
        final cells = await _databaseService.getCellsForRow(row.rowId!);

        for (final cell in cells) {
          if (cell.column.value?.columnId == null) continue;
          cellsByRowAndColumn[row.rowId!][cell.column.value!.columnId!] = cell;
        }
      }

      setState(() {
        _table = table;
        _columns = columns;
        _rows = rows;
        _filteredRows = List.from(rows);
        _cellsByRowAndColumn = cellsByRowAndColumn;
        _isLoading = false;

        // Initialize controllers for all cells
        _initCellControllers();
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _initCellControllers() {
    // Clear existing controllers
    for (var controller in _cellControllers.values) {
      controller.dispose();
    }
    _cellControllers.clear();

    // Create controllers for each cell
    for (final row in _filteredRows) {
      if (row.rowId == null) continue;

      for (final column in _columns) {
        if (column.columnId == null) continue;

        final cellKey = '${row.rowId}:${column.columnId}';
        final cell = _cellsByRowAndColumn[row.rowId]?[column.columnId];
        String value = '';

        if (cell != null) {
          switch (column.dataType) {
            case ColumnDataType.text:
            case ColumnDataType.email:
            case ColumnDataType.url:
            case ColumnDataType.phone:
              value = cell.textValue ?? '';
              break;
            case ColumnDataType.number:
            case ColumnDataType.currency:
            case ColumnDataType.percentage:
              value = cell.numberValue?.toString() ?? '';
              break;
            case ColumnDataType.date:
            case ColumnDataType.time:
            case ColumnDataType.datetime:
              value = cell.dateValue?.toString() ?? '';
              break;
            case ColumnDataType.boolean:
              value = cell.boolValue == true ? 'Yes' : 'No';
              break;
            case ColumnDataType.dropdown:
            case ColumnDataType.multiselect:
              value = cell.textValue ?? '';
              break;
          }
        }

        _cellControllers[cellKey] = TextEditingController(text: value);
      }
    }
  }

  void _filterData(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredRows = List.from(_rows);
        _initCellControllers();
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    setState(() {
      _filteredRows = _rows.where((row) {
        if (row.rowId == null) return false;

        // Search in all cells for this row
        for (final column in _columns) {
          if (column.columnId == null) continue;

          final cell = _cellsByRowAndColumn[row.rowId]?[column.columnId];
          if (cell == null) continue;

          // Check cell value based on column type
          switch (column.dataType) {
            case ColumnDataType.text:
            case ColumnDataType.email:
            case ColumnDataType.url:
            case ColumnDataType.phone:
            case ColumnDataType.dropdown:
            case ColumnDataType.multiselect:
              final value = cell.textValue?.toLowerCase() ?? '';
              if (value.contains(lowercaseQuery)) return true;
              break;
            case ColumnDataType.number:
            case ColumnDataType.currency:
            case ColumnDataType.percentage:
              final value = cell.numberValue?.toString().toLowerCase() ?? '';
              if (value.contains(lowercaseQuery)) return true;
              break;
            case ColumnDataType.date:
            case ColumnDataType.time:
            case ColumnDataType.datetime:
              final value = cell.dateValue?.toString().toLowerCase() ?? '';
              if (value.contains(lowercaseQuery)) return true;
              break;
            case ColumnDataType.boolean:
              final value = (cell.boolValue == true ? 'yes' : 'no');
              if (value.contains(lowercaseQuery)) return true;
              break;
          }
        }

        return false;
      }).toList();

      _initCellControllers();
    });
  }

  // Cell selection and editing
  void _selectCell(String rowId, String columnId) {
    setState(() {
      _selectedRowId = rowId;
      _selectedColumnId = columnId;

      // Clear selection if not in selection mode
      if (!_isSelecting) {
        _selectedCells.clear();
        _selectedCells.add('$rowId:$columnId');
      }
    });

    // Request focus to the grid
    _gridFocusNode.requestFocus();
  }

  void _startSelecting(String rowId, String columnId) {
    setState(() {
      _selectedRowId = rowId;
      _selectedColumnId = columnId;
      _selectionStartRowId = rowId;
      _selectionStartColumnId = columnId;
      _isSelecting = true;

      // Clear previous selection
      _selectedCells.clear();
      _selectedCells.add('$rowId:$columnId');
    });
  }

  void _updateSelection(String rowId, String columnId) {
    if (!_isSelecting || _selectionStartRowId == null || _selectionStartColumnId == null) {
      return;
    }

    setState(() {
      // Clear previous selection
      _selectedCells.clear();

      // Find indices for start and end rows/columns
      final startRowIndex = _rows.indexWhere((row) => row.rowId == _selectionStartRowId);
      final endRowIndex = _rows.indexWhere((row) => row.rowId == rowId);
      final startColIndex = _columns.indexWhere((col) => col.columnId == _selectionStartColumnId);
      final endColIndex = _columns.indexWhere((col) => col.columnId == columnId);

      if (startRowIndex < 0 || endRowIndex < 0 || startColIndex < 0 || endColIndex < 0) {
        return;
      }

      // Calculate selection rectangle
      final minRowIndex = startRowIndex < endRowIndex ? startRowIndex : endRowIndex;
      final maxRowIndex = startRowIndex < endRowIndex ? endRowIndex : startRowIndex;
      final minColIndex = startColIndex < endColIndex ? startColIndex : endColIndex;
      final maxColIndex = startColIndex < endColIndex ? endColIndex : startColIndex;

      // Add all cells in the rectangle to selection
      for (var r = minRowIndex; r <= maxRowIndex; r++) {
        for (var c = minColIndex; c <= maxColIndex; c++) {
          final rowId = _rows[r].rowId;
          final columnId = _columns[c].columnId;

          if (rowId != null && columnId != null) {
            _selectedCells.add('$rowId:$columnId');
          }
        }
      }
    });
  }

  void _endSelecting() {
    setState(() {
      _isSelecting = false;
    });
  }

  Future<void> _updateCellValue(String rowId, String columnId, String newValue) async {
    final column = _columns.firstWhere((col) => col.columnId == columnId);

    try {
      // Convert value based on column type
      dynamic typedValue;

      switch (column.dataType) {
        case ColumnDataType.text:
        case ColumnDataType.email:
        case ColumnDataType.url:
        case ColumnDataType.phone:
        case ColumnDataType.dropdown:
        case ColumnDataType.multiselect:
          typedValue = newValue;
          break;
        case ColumnDataType.number:
        case ColumnDataType.currency:
        case ColumnDataType.percentage:
          typedValue = newValue.isEmpty ? null : double.tryParse(newValue);
          break;
        case ColumnDataType.date:
        case ColumnDataType.time:
        case ColumnDataType.datetime:
          typedValue = newValue.isEmpty ? null : DateTime.tryParse(newValue);
          break;
        case ColumnDataType.boolean:
          typedValue = newValue.toLowerCase() == 'yes' || newValue.toLowerCase() == 'true';
          break;
      }

      // Update in database
      await _databaseService.setCellValue(rowId, columnId, typedValue);

      // Update local data
      setState(() {
        final cell = _cellsByRowAndColumn[rowId]?[columnId];

        if (cell != null) {
          switch (column.dataType) {
            case ColumnDataType.text:
            case ColumnDataType.email:
            case ColumnDataType.url:
            case ColumnDataType.phone:
            case ColumnDataType.dropdown:
            case ColumnDataType.multiselect:
              cell.textValue = typedValue;
              break;
            case ColumnDataType.number:
            case ColumnDataType.currency:
            case ColumnDataType.percentage:
              cell.numberValue = typedValue;
              break;
            case ColumnDataType.date:
            case ColumnDataType.time:
            case ColumnDataType.datetime:
              cell.dateValue = typedValue;
              break;
            case ColumnDataType.boolean:
              cell.boolValue = typedValue;
              break;
          }
        }
      });
    } catch (e) {
      _showSnackBar('Error updating cell: $e', isError: true);
    }
  }

  Future<void> _clearSelectedCells() async {
    try {
      // Process each selected cell
      for (var cellKey in _selectedCells) {
        final parts = cellKey.split(':');
        final rowId = parts[0];
        final columnId = parts[1];

        final column = _columns.firstWhere((col) => col.columnId == columnId);

        // Clear cell value
        await _databaseService.clearCellValue(rowId, columnId);

        // Update controller
        final controllerKey = '$rowId:$columnId';
        if (_cellControllers.containsKey(controllerKey)) {
          _cellControllers[controllerKey]!.text = '';
        }

        // Update local data
        final cell = _cellsByRowAndColumn[rowId]?[columnId];
        if (cell != null) {
          switch (column.dataType) {
            case ColumnDataType.text:
            case ColumnDataType.email:
            case ColumnDataType.url:
            case ColumnDataType.phone:
            case ColumnDataType.dropdown:
            case ColumnDataType.multiselect:
              cell.textValue = null;
              break;
            case ColumnDataType.number:
            case ColumnDataType.currency:
            case ColumnDataType.percentage:
              cell.numberValue = null;
              break;
            case ColumnDataType.date:
            case ColumnDataType.time:
            case ColumnDataType.datetime:
              cell.dateValue = null;
              break;
            case ColumnDataType.boolean:
              cell.boolValue = null;
              break;
          }
        }
      }

      setState(() {
        // Refresh UI
      });

      _showSnackBar('Cells cleared');
    } catch (e) {
      _showSnackBar('Error clearing cells: $e', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : widget.themeColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _moveSelection(int rowDelta, int colDelta) {
    if (_selectedRowId == null || _selectedColumnId == null) {
      if (_filteredRows.isNotEmpty && _columns.isNotEmpty) {
        _selectCell(_filteredRows.first.rowId!, _columns.first.columnId!);
      }
      return;
    }

    // Find current indices
    final rowIndex = _filteredRows.indexWhere((row) => row.rowId == _selectedRowId);
    final colIndex = _columns.indexWhere((col) => col.columnId == _selectedColumnId);

    if (rowIndex < 0 || colIndex < 0) return;

    // Calculate new indices
    final newRowIndex = (rowIndex + rowDelta).clamp(0, _filteredRows.length - 1);
    final newColIndex = (colIndex + colDelta).clamp(0, _columns.length - 1);

    // Get new IDs
    final newRowId = _filteredRows[newRowIndex].rowId;
    final newColId = _columns[newColIndex].columnId;

    if (newRowId != null && newColId != null) {
      _selectCell(newRowId, newColId);
    }
  }

  // Add new row
  Future<void> _addNewRow() async {
    if (_table == null) return;

    try {
      final row = await _databaseService.addRow(widget.tableId);

      setState(() {
        _rows.add(row);
        _filteredRows.add(row);

        // Initialize controllers for the new row
        for (final column in _columns) {
          if (column.columnId == null) continue;

          final cellKey = '${row.rowId}:${column.columnId}';
          _cellControllers[cellKey] = TextEditingController();

          // Initialize empty cell in the map
          if (row.rowId != null) {
            if (_cellsByRowAndColumn[row.rowId!] == null) {
              _cellsByRowAndColumn[row.rowId!] = {};
            }
          }
        }
      });

      _showSnackBar('New row added');

      // Select the new row
      if (row.rowId != null && _columns.isNotEmpty && _columns.first.columnId != null) {
        _selectCell(row.rowId!, _columns.first.columnId!);
      }
    } catch (e) {
      _showSnackBar('Error adding row: $e', isError: true);
    }
  }

  // Delete row
  Future<void> _deleteRow(String rowId) async {
    try {
      await _databaseService.deleteRow(rowId);

      setState(() {
        _rows.removeWhere((row) => row.rowId == rowId);
        _filteredRows.removeWhere((row) => row.rowId == rowId);
        _cellsByRowAndColumn.remove(rowId);

        // Remove controllers for the deleted row
        final keysToRemove = _cellControllers.keys.where((key) => key.startsWith('$rowId:')).toList();
        for (final key in keysToRemove) {
          _cellControllers[key]?.dispose();
          _cellControllers.remove(key);
        }

        // Reset selection if the selected row was deleted
        if (_selectedRowId == rowId) {
          _selectedRowId = null;
          _selectedColumnId = null;
          _selectedCells.clear();
        }
      });

      _showSnackBar('Row deleted');
    } catch (e) {
      _showSnackBar('Error deleting row: $e', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_table?.name ?? 'Flexible Data Grid'),
        backgroundColor: widget.themeColor,
        actions: [
          // Import/Export menu
          PopupMenuButton<String>(
            tooltip: 'Import/Export',
            onSelected: (value) async {
              switch (value) {
                case 'import':
                  // await _importFromExcel();
                  break;
                case 'export':
                  // await _exportToExcel();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'import',
                child: ListTile(
                  leading: Icon(Icons.upload_file),
                  title: Text('Import from Excel'),
                  dense: true,
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export to Excel'),
                  dense: true,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterData('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: _filterData,
            ),
          ),

          // Data grid
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator(color: widget.themeColor))
                : _error != null
                    ? _buildErrorWidget()
                    : _buildDataGrid(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewRow,
        backgroundColor: widget.themeColor,
        tooltip: 'Add Row',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildDataGrid() {
    return KeyboardListener(
      focusNode: _gridFocusNode,
      onKeyEvent: (keyEvent) {
        if (keyEvent is KeyDownEvent) {
          if (keyEvent.logicalKey == LogicalKeyboardKey.arrowUp) {
            _moveSelection(-1, 0);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.arrowDown) {
            _moveSelection(1, 0);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.arrowLeft) {
            _moveSelection(0, -1);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.arrowRight) {
            _moveSelection(0, 1);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.tab) {
            _moveSelection(0, HardwareKeyboard.instance.isShiftPressed ? -1 : 1);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.enter) {
            _moveSelection(1, 0);
          } else if (keyEvent.logicalKey == LogicalKeyboardKey.delete ||
                    keyEvent.logicalKey == LogicalKeyboardKey.backspace) {
            _clearSelectedCells();
          }
        }
      },
      child: Scrollbar(
        controller: _verticalController,
        thumbVisibility: true,
        child: Scrollbar(
          controller: _horizontalController,
          thumbVisibility: true,
          notificationPredicate: (notification) => notification.depth == 1,
          child: SingleChildScrollView(
            controller: _verticalController,
            child: SingleChildScrollView(
              controller: _horizontalController,
              scrollDirection: Axis.horizontal,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row with column names
                  Row(
                    children: [
                      // Empty corner cell
                      Container(
                        width: 60,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                        child: Center(
                          child: IconButton(
                            icon: const Icon(Icons.add, color: Colors.green),
                            tooltip: 'Add Row',
                            onPressed: _addNewRow,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ),
                      ),

                      // Column headers
                      ..._columns.map((column) => Container(
                        width: 150,
                        height: 40,
                        decoration: BoxDecoration(
                          color: widget.themeColor,
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          column.name ?? 'Unnamed Column',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      )),
                    ],
                  ),

                  // Data rows
                  ..._filteredRows.asMap().entries.map((entry) {
                    final rowIndex = entry.key;
                    final row = entry.value;

                    return Row(
                      children: [
                        // Row header
                        Container(
                          width: 60,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            border: Border.all(color: Colors.grey.shade400),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left: 4),
                                child: Text(
                                  '${rowIndex + 1}',
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                                onPressed: () => row.rowId != null ? _deleteRow(row.rowId!) : null,
                                tooltip: 'Delete Row',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ),

                        // Data cells
                        ..._columns.map((column) {
                          if (row.rowId == null || column.columnId == null) {
                            return const SizedBox.shrink();
                          }

                          final cellKey = '${row.rowId}:${column.columnId}';
                          final isSelected = _selectedCells.contains(cellKey);
                          final isFocused = _selectedRowId == row.rowId && _selectedColumnId == column.columnId;

                          // Ensure we have a controller for this cell
                          if (!_cellControllers.containsKey(cellKey)) {
                            _cellControllers[cellKey] = TextEditingController();
                          }

                          return GestureDetector(
                            onTap: () => _selectCell(row.rowId!, column.columnId!),
                            child: Container(
                              width: 150,
                              height: 40,
                              decoration: BoxDecoration(
                                color: isFocused
                                    ? widget.themeColor.withAlpha(51) // 0.2 * 255 = 51
                                    : isSelected
                                        ? widget.themeColor.withAlpha(26) // 0.1 * 255 = 26
                                        : Colors.white,
                                border: Border.all(
                                  color: isFocused || isSelected
                                      ? widget.themeColor
                                      : Colors.grey.shade400,
                                  width: isFocused ? 2 : 1,
                                ),
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              alignment: Alignment.centerLeft,
                              child: TextField(
                                controller: _cellControllers[cellKey],
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  isDense: true,
                                  contentPadding: EdgeInsets.zero,
                                ),
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                                onChanged: (value) {
                                  _updateCellValue(row.rowId!, column.columnId!, value);
                                },
                                autofocus: isFocused, // Auto-focus when selected
                              ),
                            ),
                          );
                        }),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
