import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/database_service.dart';
import '../widgets/excel_grid_widget.dart';

class ExcelDatabaseScreen extends StatefulWidget {
  final String? initialSystem;

  const ExcelDatabaseScreen({super.key, this.initialSystem});

  @override
  State<ExcelDatabaseScreen> createState() => _ExcelDatabaseScreenState();
}

class _ExcelDatabaseScreenState extends State<ExcelDatabaseScreen> {
  String _selectedTable = 'alarm';
  late DatabaseService _databaseService;

  @override
  void initState() {
    super.initState();
    _databaseService = Provider.of<DatabaseService>(context, listen: false);
    if (widget.initialSystem != null) {
      _selectedTable = widget.initialSystem!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Excel Grid Database'),
        backgroundColor: Colors.blue.shade700,
      ),
      body: Row(
        children: [
          // Left sidebar with table selection
          Container(
            width: 250,
            color: Colors.grey.shade100,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  color: Colors.blue.shade700,
                  width: double.infinity,
                  child: const Text(
                    'Database Tables',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Expanded(
                  child: ListView(
                    children: [
                      _buildTableTile('alarm', 'Fire Alarm Systems', Icons.notifications_active, Colors.red),
                      _buildTableTile('water', 'Water Systems', Icons.water_drop, Colors.blue),
                      _buildTableTile('foam', 'Foam Systems', Icons.bubble_chart, Colors.amber),
                      _buildTableTile('fm200', 'FM200 Systems', Icons.air, Colors.green),
                      _buildTableTile('novec', 'Novec Systems', Icons.air_sharp, Colors.teal),
                      _buildTableTile('co2', 'CO2 Systems', Icons.science, Colors.purple),
                      _buildTableTile('materials', 'Materials', Icons.category, Colors.brown),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Main content area with Excel Grid
          Expanded(
            child: ExcelGridWidget(
              tableName: _selectedTable,
              databaseService: _databaseService,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableTile(String tableId, String title, IconData icon, MaterialColor color) {
    final isSelected = _selectedTable == tableId;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Colors.white : color.shade700,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.black87,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      selectedTileColor: color.shade700,
      onTap: () {
        setState(() {
          _selectedTable = tableId;
        });
      },
    );
  }
}
