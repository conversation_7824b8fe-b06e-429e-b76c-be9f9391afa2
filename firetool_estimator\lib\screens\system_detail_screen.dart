import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/project.dart';
import '../services/project_provider.dart';
import '../widgets/system_pricing_adapter.dart';
import '../widgets/edit_material_dialog.dart';
import 'add_material_screen.dart';

class SystemDetailScreen extends StatefulWidget {
  final String systemId;

  const SystemDetailScreen({super.key, required this.systemId});

  @override
  State<SystemDetailScreen> createState() => _SystemDetailScreenState();
}

class _SystemDetailScreenState extends State<SystemDetailScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final currencyFormatter = NumberFormat.currency(symbol: '\$');

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  SystemEstimate? _getSystem(ProjectProvider projectProvider) {
    if (projectProvider.currentProject == null) {
      return null;
    }

    return projectProvider.currentProject!.systems
        .firstWhere((s) => s.id == widget.systemId, orElse: () => throw Exception('System not found'));
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final system = _getSystem(projectProvider);

        if (system == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('System Details')),
            body: const Center(child: Text('System not found')),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(system.name),
            actions: [
              IconButton(
                icon: const Icon(Icons.save),
                tooltip: 'Save Project',
                onPressed: () async {
                  await projectProvider.saveCurrentProject();
                  if (!mounted) return;

                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(content: Text('Project saved')),
                  );
                },
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              indicatorWeight: 3,
              indicatorSize: TabBarIndicatorSize.tab,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 16,
              ),
              tabs: const [
                Tab(
                  icon: Icon(Icons.inventory),
                  text: 'Materials',
                ),
                Tab(
                  icon: Icon(Icons.engineering),
                  text: 'Services',
                ),
              ],
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              _buildMaterialsTab(system, projectProvider),
              _buildServicesTab(system, projectProvider),
            ],
          ),
          bottomNavigationBar: BottomAppBar(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Only show the total sum
                  Text(
                    'Total:',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'SAR ${NumberFormat("#,##0").format(system.totalCost.roundToDouble())}',
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              switch (_tabController.index) {
                case 0:
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AddMaterialScreen(systemId: system.id),
                    ),
                  );
                  break;
                case 1:
                  // Create a new service directly with a dialog
                  _showAddServiceDialog(context, system, projectProvider);
                  break;
              }
            },
            child: const Icon(Icons.add),
          ),
        );
      },
    );
  }

  Widget _buildMaterialsTab(SystemEstimate system, ProjectProvider projectProvider) {
    // Combine materials and equipment
    final bool hasMaterials = system.materials.isNotEmpty;
    final bool hasEquipment = system.equipment.isNotEmpty;

    if (!hasMaterials && !hasEquipment) {
      return _buildEmptyState('No materials added yet', 'Add Material');
    }

    // Create a combined list of items to display
    final List<Widget> itemCards = [];

    // Add materials
    if (hasMaterials) {
      itemCards.add(
        const Padding(
          padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            'Materials',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        )
      );

      for (final material in system.materials) {
        itemCards.add(_buildMaterialCard(material, system, projectProvider));
      }
    }

    // Add equipment
    if (hasEquipment) {
      itemCards.add(
        const Padding(
          padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            'Equipment',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        )
      );

      for (final equipment in system.equipment) {
        itemCards.add(_buildEquipmentCard(equipment, system, projectProvider));
      }
    }

    return ListView(
      children: itemCards,
    );
  }

  Widget _buildServicesTab(SystemEstimate system, ProjectProvider projectProvider) {
    if (system.services.isEmpty) {
      return _buildEmptyState('No service items added yet', 'Add Service');
    }

    return ListView.builder(
      itemCount: system.services.length,
      itemBuilder: (context, index) {
        final service = system.services[index];
        return _buildServiceCard(service, system, projectProvider);
      },
    );
  }

  Widget _buildEmptyState(String message, String buttonText) {
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.inventory,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: Text(buttonText),
            onPressed: () {
              switch (_tabController.index) {
                case 0:
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AddMaterialScreen(systemId: widget.systemId),
                    ),
                  );
                  break;
                case 1:
                  _showAddServiceDialog(context,
                    projectProvider.currentProject!.systems.firstWhere((s) => s.id == widget.systemId),
                    projectProvider
                  );
                  break;
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMaterialCard(MaterialItem material, SystemEstimate system, ProjectProvider projectProvider) {
    return MaterialPricingCard(
      material: material,
      exchangeRate: projectProvider.currentProject?.exchangeRate ?? 3.75,
      onQuantityChanged: (newQuantity) {
        final updatedMaterial = MaterialItem(
          id: material.id,
          name: material.name,
          category: material.category,
          description: material.description,
          quantity: newQuantity,
          unit: material.unit,
          exWorksUnitCost: material.exWorksUnitCost,
          localUnitCost: material.localUnitCost,
          installationUnitCost: material.installationUnitCost,
          isImported: material.isImported,
          vendor: material.vendor,
          approval: material.approval,
        );
        projectProvider.updateMaterial(system.id, updatedMaterial);
        projectProvider.saveCurrentProject();
      },
      onEdit: () {
        _showEditMaterialDialog(context, material, system, projectProvider);
      },
      onDelete: () {
        _showDeleteItemConfirmation(
          context,
          'material',
          material.name,
          () => projectProvider.removeMaterial(system.id, material.id),
        );
      },
    );
  }

  Widget _buildEquipmentCard(EquipmentItem equipment, SystemEstimate system, ProjectProvider projectProvider) {
    return EquipmentPricingCard(
      equipment: equipment,
      exchangeRate: projectProvider.currentProject?.exchangeRate ?? 3.75,
      onQuantityChanged: (newQuantity) {
        final updatedEquipment = EquipmentItem(
          id: equipment.id,
          name: equipment.name,
          category: equipment.category,
          quantity: newQuantity,
          exWorksUnitCost: equipment.exWorksUnitCost,
          localUnitCost: equipment.localUnitCost,
          installationUnitCost: equipment.installationUnitCost,
          isImported: equipment.isImported,
          vendor: equipment.vendor,
          approval: equipment.approval,
        );
        projectProvider.updateEquipment(system.id, updatedEquipment);
        projectProvider.saveCurrentProject();
      },
      onEdit: () {
        _showEditEquipmentDialog(context, equipment, system, projectProvider);
      },
      onDelete: () {
        _showDeleteItemConfirmation(
          context,
          'equipment',
          equipment.name,
          () => projectProvider.removeEquipment(system.id, equipment.id),
        );
      },
    );
  }

  Widget _buildServiceCard(ServiceItem service, SystemEstimate system, ProjectProvider projectProvider) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    service.description,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: Text(
                    'SAR ${NumberFormat("#,##0").format(service.totalCost.roundToDouble())}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('Category: ${service.category}'),
            Text('${service.quantity} ${service.unit}'),
            Text('Rate: ${NumberFormat("#,##0").format(service.unitRate)} per ${service.unit}'),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('Edit'),
                  onPressed: () {
                    _showEditServiceDialog(context, service, system, projectProvider);
                  },
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                  label: const Text('Delete', style: TextStyle(color: Colors.red)),
                  onPressed: () {
                    _showDeleteItemConfirmation(
                      context,
                      'service item',
                      service.description,
                      () => projectProvider.removeService(system.id, service.id),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showEditMaterialDialog(BuildContext context, MaterialItem material, SystemEstimate system, ProjectProvider projectProvider) {
    // Get the exchange rate from the current project
    final exchangeRate = projectProvider.currentProject?.exchangeRate ?? 3.75; // Default to 3.75 if not available

    showDialog(
      context: context,
      builder: (context) => EditMaterialDialog(
        material: material,
        exchangeRate: exchangeRate,
        onSave: (updatedMaterial) {
          projectProvider.updateMaterial(system.id, updatedMaterial);
          projectProvider.saveCurrentProject();
        },
      ),
    );
  }

  void _showEditEquipmentDialog(BuildContext context, EquipmentItem equipment, SystemEstimate system, ProjectProvider projectProvider) {
    final quantityController = TextEditingController(text: equipment.quantity.toString());
    final exWorksUnitCostController = TextEditingController(text: equipment.exWorksUnitCost.toString());
    final localUnitCostController = TextEditingController(text: equipment.localUnitCost.toString());
    final installationUnitCostController = TextEditingController(text: equipment.installationUnitCost.toString());
    final vendorController = TextEditingController(text: equipment.vendor);
    final approvalController = TextEditingController(text: equipment.approval);

    // Get the exchange rate from the current project
    final exchangeRate = projectProvider.currentProject?.exchangeRate ?? 3.75; // Default to 3.75 if not available

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${equipment.name}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: quantityController,
                decoration: const InputDecoration(labelText: 'Quantity'),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              TextField(
                controller: vendorController,
                decoration: const InputDecoration(labelText: 'Vendor/Manufacturer'),
              ),
              TextField(
                controller: approvalController,
                decoration: const InputDecoration(labelText: 'Approval/Certification'),
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              TextField(
                controller: exWorksUnitCostController,
                decoration: const InputDecoration(labelText: 'Ex-Works Unit Cost (USD)'),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                // No automatic update of local cost
              ),
              TextField(
                controller: localUnitCostController,
                decoration: const InputDecoration(labelText: 'Local Unit Cost (SAR)'),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              TextField(
                controller: installationUnitCostController,
                decoration: const InputDecoration(labelText: 'Installation Unit Cost (SAR)'),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 8),
              Text('Exchange Rate: \$1 = SAR ${exchangeRate.toStringAsFixed(2)}',
                style: const TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
              ),
              const SizedBox(height: 8),
              // Show calculated total unit rate
              FutureBuilder<void>(
                future: Future.delayed(Duration.zero), // Just to trigger the builder
                builder: (context, snapshot) {
                  final exWorksCost = double.tryParse(exWorksUnitCostController.text) ?? 0.0;
                  final localCost = double.tryParse(localUnitCostController.text) ?? 0.0;
                  final installationCost = double.tryParse(installationUnitCostController.text) ?? 0.0;
                  // Convert ex-works cost to SAR using the exchange rate
                  final totalUnitRate = (exWorksCost * exchangeRate) + localCost + installationCost;

                  return Text(
                    'Total Unit Rate: SAR ${NumberFormat("#,##0.00").format(totalUnitRate)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final updatedEquipment = EquipmentItem(
                id: equipment.id,
                name: equipment.name,
                category: equipment.category,
                quantity: double.tryParse(quantityController.text) ?? equipment.quantity,
                exWorksUnitCost: double.tryParse(exWorksUnitCostController.text) ?? equipment.exWorksUnitCost,
                localUnitCost: double.tryParse(localUnitCostController.text) ?? equipment.localUnitCost,
                installationUnitCost: double.tryParse(installationUnitCostController.text) ?? equipment.installationUnitCost,
                isImported: equipment.isImported,
                vendor: vendorController.text,
                approval: approvalController.text,
              );

              projectProvider.updateEquipment(system.id, updatedEquipment);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showEditServiceDialog(BuildContext context, ServiceItem service, SystemEstimate system, ProjectProvider projectProvider) {
    final quantityController = TextEditingController(text: service.quantity.toString());
    final unitRateController = TextEditingController(text: service.unitRate.toString());

    // Service unit options
    final List<String> unitOptions = ['Days', 'Lumpsum'];
    // If the current unit is not in the new options, default to 'Days'
    String selectedUnit = unitOptions.contains(service.unit) ? service.unit : 'Days';

    // Service category options
    final List<String> categoryOptions = [
      'Engineering',
      'Management',
      'Testing',
      'Programming',
      'Commissioning',
      'Documentation',
      'Training',
      'Consultation'
    ];
    String selectedCategory = service.category;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('Edit Service'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Service Type', style: TextStyle(fontSize: 12, color: Colors.grey)),
                DropdownButton<String>(
                  isExpanded: true,
                  value: selectedCategory,
                  items: categoryOptions.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedCategory = value;
                      });
                    }
                  },
                ),

                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: quantityController,
                        decoration: const InputDecoration(labelText: 'Quantity'),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButton<String>(
                        isExpanded: true,
                        value: selectedUnit,
                        items: unitOptions.map((unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedUnit = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),
                TextField(
                  controller: unitRateController,
                  decoration: InputDecoration(
                    labelText: 'Rate per $selectedUnit',
                    prefixText: 'SAR ',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                ),

                const SizedBox(height: 8),
                FutureBuilder<void>(
                  future: Future.delayed(Duration.zero),
                  builder: (context, snapshot) {
                    final quantity = double.tryParse(quantityController.text) ?? 0.0;
                    final rate = double.tryParse(unitRateController.text) ?? 0.0;
                    final total = quantity * rate;

                    return Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Text(
                            'Total Cost:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'SAR ${total.round()}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                final updatedService = ServiceItem(
                  id: service.id,
                  description: selectedCategory,
                  category: selectedCategory,
                  quantity: double.tryParse(quantityController.text) ?? service.quantity,
                  unit: selectedUnit,
                  unitRate: double.tryParse(unitRateController.text) ?? service.unitRate,
                );

                projectProvider.updateService(system.id, updatedService);
                Navigator.pop(context);
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddServiceDialog(BuildContext context, SystemEstimate system, ProjectProvider projectProvider) {
    final quantityController = TextEditingController(text: '1');
    final unitRateController = TextEditingController(text: '0');

    // Service unit options
    final List<String> unitOptions = ['Days', 'Lumpsum'];
    String selectedUnit = 'Days';

    // Service category options
    final List<String> categoryOptions = [
      'Engineering',
      'Management',
      'Testing',
      'Programming',
      'Commissioning',
      'Documentation',
      'Training',
      'Consultation'
    ];
    String selectedCategory = 'Engineering';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Service'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Service Type', style: TextStyle(fontSize: 12, color: Colors.grey)),
                DropdownButton<String>(
                  isExpanded: true,
                  value: selectedCategory,
                  items: categoryOptions.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedCategory = value;
                      });
                    }
                  },
                ),

                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: quantityController,
                        decoration: const InputDecoration(labelText: 'Quantity'),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButton<String>(
                        isExpanded: true,
                        value: selectedUnit,
                        items: unitOptions.map((unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedUnit = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),
                TextField(
                  controller: unitRateController,
                  decoration: InputDecoration(
                    labelText: 'Rate per $selectedUnit',
                    prefixText: 'SAR ',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                ),

                const SizedBox(height: 8),
                FutureBuilder<void>(
                  future: Future.delayed(Duration.zero),
                  builder: (context, snapshot) {
                    final quantity = double.tryParse(quantityController.text) ?? 0.0;
                    final rate = double.tryParse(unitRateController.text) ?? 0.0;
                    final total = quantity * rate;

                    return Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Text(
                            'Total Cost:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'SAR ${total.round()}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                final newService = ServiceItem(
                  description: selectedCategory,
                  category: selectedCategory,
                  quantity: double.tryParse(quantityController.text) ?? 1.0,
                  unit: selectedUnit,
                  unitRate: double.tryParse(unitRateController.text) ?? 0.0,
                );

                projectProvider.addService(system.id, newService);
                Navigator.pop(context);
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteItemConfirmation(BuildContext context, String itemType, String itemName, Function() onDelete) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete $itemType'),
        content: Text('Are you sure you want to delete "$itemName"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onDelete();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('$itemType deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }


}
