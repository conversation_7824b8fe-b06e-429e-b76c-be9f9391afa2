import 'package:flutter/material.dart';
import '../widgets/mock_complete_excel_grid.dart';

class CompleteExcelGridFactory {
  static Widget createScreen(String systemType) {
    switch (systemType) {
      case 'alarm':
        return MockCompleteExcelGrid(
          collectionPath: 'alarm',
          title: 'Fire Alarm Systems',
          themeColor: Colors.red.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'water':
        return MockCompleteExcelGrid(
          collectionPath: 'water',
          title: 'Water Systems',
          themeColor: Colors.blue.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'foam':
        return MockCompleteExcelGrid(
          collectionPath: 'foam',
          title: 'Foam Systems',
          themeColor: Colors.amber.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'fm200':
        return MockCompleteExcelGrid(
          collectionPath: 'fm200',
          title: 'FM200 Systems',
          themeColor: Colors.green.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'novec':
        return MockCompleteExcelGrid(
          collectionPath: 'novec',
          title: 'Novec Systems',
          themeColor: Colors.teal.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'co2':
        return MockCompleteExcelGrid(
          collectionPath: 'co2',
          title: 'CO2 Systems',
          themeColor: Colors.purple.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      case 'materials':
        return MockCompleteExcelGrid(
          collectionPath: 'materials',
          title: 'Materials',
          themeColor: Colors.brown.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
      default:
        return MockCompleteExcelGrid(
          collectionPath: 'alarm',
          title: 'Fire Alarm Systems',
          themeColor: Colors.red.shade700,
          predefinedColumns: [
            'model',
            'description',
            'manufacturer',
            'approval',
            'ex_works_price',
            'local_price',
            'installation_price',
          ],
        );
    }
  }
}
