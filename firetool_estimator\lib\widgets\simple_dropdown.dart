import 'package:flutter/material.dart';

class SimpleDropdown<T> extends StatefulWidget {
  final List<T> items;
  final T? value;
  final String Function(T) displayItemFn;
  final String Function(T)? displayCategoryFn;
  final Function(T) onChanged;
  final String hintText;
  final bool showCategory;

  const SimpleDropdown({
    super.key,
    required this.items,
    required this.displayItemFn,
    required this.onChanged,
    this.value,
    this.displayCategoryFn,
    this.hintText = 'Select an item',
    this.showCategory = false,
  });

  @override
  State<SimpleDropdown<T>> createState() => _SimpleDropdownState<T>();
}

class _SimpleDropdownState<T> extends State<SimpleDropdown<T>> {
  T? _selectedItem;
  bool _isExpanded = false;
  final TextEditingController _searchController = TextEditingController();
  List<T> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.value;
    _filteredItems = List<T>.from(widget.items);
  }

  @override
  void didUpdateWidget(SimpleDropdown<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      setState(() {
        _selectedItem = widget.value;
      });
    }

    if (oldWidget.items != widget.items) {
      setState(() {
        _filteredItems = List<T>.from(widget.items);
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterItems(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredItems = List<T>.from(widget.items);
      } else {
        _filteredItems = widget.items.where((item) {
          final itemText = widget.displayItemFn(item).toLowerCase();
          final searchText = query.toLowerCase();

          // Also search in category if available
          String category = '';
          if (widget.displayCategoryFn != null) {
            category = widget.displayCategoryFn!(item).toLowerCase();
          }

          return itemText.contains(searchText) || category.contains(searchText);
        }).toList();
      }
    });
  }

  void _selectItem(T item) {
    setState(() {
      _selectedItem = item;
      _isExpanded = false;
      _searchController.clear();
      _filteredItems = List<T>.from(widget.items);
    });

    widget.onChanged(item);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        GestureDetector(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
              if (_isExpanded) {
                // Make sure all items are shown when dropdown is opened
                _filteredItems = List<T>.from(widget.items);
              } else {
                _searchController.clear();
                _filteredItems = List<T>.from(widget.items);
              }
            });
          },
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.shade50,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _selectedItem != null
                        ? widget.displayItemFn(_selectedItem as T)
                        : widget.hintText,
                    style: TextStyle(
                      color: _selectedItem != null ? Colors.black : Colors.grey,
                    ),
                  ),
                ),
                Icon(
                  _isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                  color: Colors.grey,
                ),
              ],
            ),
          ),
        ),

        // Dropdown content
        if (_isExpanded)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: const Color.fromRGBO(0, 0, 0, 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Search field
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    onChanged: _filterItems,
                  ),
                ),

                // Items list
                Container(
                  constraints: const BoxConstraints(
                    maxHeight: 250,
                  ),
                  child: _filteredItems.isEmpty
                      ? const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Text('No items found'),
                        )
                      : ListView.builder(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemCount: _filteredItems.length,
                          itemBuilder: (context, index) {
                            final item = _filteredItems[index];
                            // Compare by name equality to ensure selection persists when scrolling
                            bool isSelected = false;
                            if (_selectedItem != null) {
                              // First try direct equality
                              if (_selectedItem == item) {
                                isSelected = true;
                              }
                              // Then try name-based equality as fallback
                              else if (widget.displayItemFn(_selectedItem as T) == widget.displayItemFn(item)) {
                                isSelected = true;
                                // Update the selected item reference to the current item in the list
                                // This ensures we maintain the correct reference when scrolling
                                _selectedItem = item;
                              }
                            }

                            return InkWell(
                              onTap: () => _selectItem(item),
                              child: Container(
                                color: isSelected
                                    ? const Color.fromRGBO(33, 150, 243, 0.1)
                                    : null,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.displayItemFn(item),
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: isSelected
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                      ),
                                    ),
                                    if (widget.showCategory &&
                                        widget.displayCategoryFn != null)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          widget.displayCategoryFn!(item),
                                          style: const TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
