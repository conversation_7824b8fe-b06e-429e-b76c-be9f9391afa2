import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Simple Table Editor',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const SimpleTableEditor(),
    );
  }
}

class SimpleTableEditor extends StatefulWidget {
  const SimpleTableEditor({Key? key}) : super(key: key);

  @override
  State<SimpleTableEditor> createState() => _SimpleTableEditorState();
}

class _SimpleTableEditorState extends State<SimpleTableEditor> {
  Database? _db;
  List<Map<String, dynamic>> _data = [];
  List<Map<String, dynamic>> _columns = [];
  bool _isLoading = true;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _initDatabase();
  }

  @override
  void dispose() {
    _db?.close();
    super.dispose();
  }

  Future<void> _initDatabase() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      // Get database path
      final dbPath = await getDatabasesPath();
      final path = join(dbPath, 'simple_table.db');

      // Delete existing database for testing
      await deleteDatabase(path);

      // Open database
      _db = await openDatabase(
        path,
        version: 1,
        onCreate: (db, version) async {
          // Create a simple table
          await db.execute('''
            CREATE TABLE simple_table (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT,
              value INTEGER
            )
          ''');

          // Insert some test data
          await db.insert('simple_table', {'name': 'Item 1', 'value': 100});
          await db.insert('simple_table', {'name': 'Item 2', 'value': 200});
          await db.insert('simple_table', {'name': 'Item 3', 'value': 300});
        },
      );

      // Load data
      await _loadData();
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadData() async {
    try {
      // Get columns
      final columns = await _db!.rawQuery("PRAGMA table_info(simple_table)");

      // Get data
      final data = await _db!.query('simple_table');

      setState(() {
        _columns = columns;
        _data = data;
        _error = '';
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    }
  }

  Future<void> _addRow() async {
    try {
      // Create a new row with default values
      final Map<String, dynamic> newRow = {
        'name': 'New Item',
        'value': 0,
      };

      // Insert the row
      await _db!.insert('simple_table', newRow);

      // Reload data
      await _loadData();
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    }
  }

  Future<void> _deleteRow(int id) async {
    try {
      // Delete the row
      await _db!.delete(
        'simple_table',
        where: 'id = ?',
        whereArgs: [id],
      );

      // Reload data
      await _loadData();
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    }
  }

  Future<void> _updateCell(int id, String column, String value) async {
    try {
      // Convert value to the right type
      dynamic typedValue = value;
      if (column == 'value') {
        typedValue = int.tryParse(value) ?? 0;
      }

      // Update the cell
      await _db!.update(
        'simple_table',
        {column: typedValue},
        where: 'id = ?',
        whereArgs: [id],
      );

      // Reload data
      await _loadData();
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    }
  }

  Future<void> _addColumn() async {
    try {
      // Add a new column
      await _db!.execute('ALTER TABLE simple_table ADD COLUMN new_column TEXT');

      // Reload data
      await _loadData();
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    }
  }

  void _showEditDialog(int id, String column, dynamic value) {
    final controller = TextEditingController(text: value.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit $column'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(labelText: column),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateCell(id, column, controller.text);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Simple Table Editor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            tooltip: 'Add Column',
            onPressed: _addColumn,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : _error.isNotEmpty
          ? Center(child: Text('Error: $_error'))
          : SingleChildScrollView(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  columns: [
                    const DataColumn(label: Text('Actions')),
                    ..._columns.map((column) =>
                      DataColumn(label: Text(column['name'].toString()))
                    ),
                  ],
                  rows: _data.map((row) => DataRow(
                    cells: [
                      DataCell(
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () => _deleteRow(row['id']),
                        ),
                      ),
                      ..._columns.map((column) {
                        final name = column['name'].toString();
                        return DataCell(
                          Text(row[name]?.toString() ?? ''),
                          onTap: () => _showEditDialog(
                            row['id'],
                            name,
                            row[name],
                          ),
                        );
                      }),
                    ],
                  )).toList(),
                ),
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addRow,
        tooltip: 'Add Row',
        child: const Icon(Icons.add),
      ),
    );
  }
}
