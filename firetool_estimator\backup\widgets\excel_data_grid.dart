import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../constants/app_constants.dart';

class ExcelDataGrid extends StatefulWidget {
  final String collectionPath;

  const ExcelDataGrid({
    super.key,
    required this.collectionPath,
  });

  @override
  State<ExcelDataGrid> createState() => _ExcelDataGridState();
}

class _ExcelDataGridState extends State<ExcelDataGrid> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  List<Map<String, dynamic>> _items = [];
  final List<String> _fixedColumns = [
    'id', 'name', 'description', 'model', 'manufacturer', 'approval',
    'unit', 'exWorksUnitCost', 'localUnitCost', 'installationUnitCost'
  ];
  bool _isLoading = true;
  String? _error;

  // For adding new items
  final Map<String, TextEditingController> _newItemControllers = {};

  // For cell selection and editing
  String? _selectedItemId;
  String? _selectedColumn;
  final TextEditingController _cellEditController = TextEditingController();
  final FocusNode _cellEditFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _loadData();

    // Initialize controllers for new item
    for (var column in _fixedColumns) {
      if (column != 'id') {
        _newItemControllers[column] = TextEditingController();
      }
    }

    // Setup focus listener for cell editing
    _cellEditFocusNode.addListener(() {
      if (!_cellEditFocusNode.hasFocus) {
        _saveSelectedCell();
      }
    });
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _cellEditController.dispose();
    _cellEditFocusNode.dispose();

    // Dispose all text controllers
    for (var controller in _newItemControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .get();

      final items = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      setState(() {
        _items = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  void _selectCell(String itemId, String column) {
    if (column == 'id') return; // Can't edit ID

    setState(() {
      _selectedItemId = itemId;
      _selectedColumn = column;

      // Find the value for the selected cell
      final item = _items.firstWhere((item) => item['id'] == itemId);
      _cellEditController.text = item[column]?.toString() ?? '';
    });

    // Focus the text field
    _cellEditFocusNode.requestFocus();
  }

  void _saveSelectedCell() {
    if (_selectedItemId != null && _selectedColumn != null) {
      final newValue = _cellEditController.text;

      // Find the current value
      final item = _items.firstWhere((item) => item['id'] == _selectedItemId);
      final currentValue = item[_selectedColumn]?.toString() ?? '';

      // Only update if value changed
      if (newValue != currentValue) {
        _updateItem(_selectedItemId!, _selectedColumn!, newValue);
      }

      setState(() {
        _selectedItemId = null;
        _selectedColumn = null;
      });
    }
  }

  Future<void> _addItem() async {
    try {
      final Map<String, dynamic> newItem = {};

      // Collect values from controllers
      for (var entry in _newItemControllers.entries) {
        if (entry.value.text.isNotEmpty) {
          newItem[entry.key] = entry.value.text;
        }
      }

      if (newItem.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please fill at least one field')),
        );
        return;
      }

      // Add timestamp
      newItem['createdAt'] = FieldValue.serverTimestamp();

      // Add to Firestore
      final docRef = await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .add(newItem);

      // Clear controllers
      for (var controller in _newItemControllers.values) {
        controller.clear();
      }

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Item added with ID: ${docRef.id}')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding item: $e')),
      );
    }
  }

  Future<void> _updateItem(String id, String field, String value) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .update({field: value});

      // Update local data
      setState(() {
        final itemIndex = _items.indexWhere((item) => item['id'] == id);
        if (itemIndex >= 0) {
          _items[itemIndex][field] = value;
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Item updated')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating item: $e')),
      );
    }
  }

  Future<void> _deleteItem(String id) async {
    try {
      await FirebaseFirestore.instance
          .collection(widget.collectionPath)
          .doc(id)
          .delete();

      // Update local data
      setState(() {
        _items.removeWhere((item) => item['id'] == id);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Item deleted')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting item: $e')),
      );
    }
  }

  Future<void> _importFromClipboard() async {
    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data == null || data.text == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No data in clipboard')),
        );
        return;
      }

      // Parse clipboard data (assuming tab-separated values from Excel)
      final rows = data.text!.split('\n');
      if (rows.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No rows found in clipboard data')),
        );
        return;
      }

      // Parse header row
      final headers = rows[0].split('\t');

      // Process data rows
      final batch = FirebaseFirestore.instance.batch();
      int count = 0;

      for (int i = 1; i < rows.length; i++) {
        if (rows[i].trim().isEmpty) continue;

        final values = rows[i].split('\t');
        if (values.length != headers.length) continue;

        final Map<String, dynamic> item = {};
        for (int j = 0; j < headers.length; j++) {
          if (headers[j].trim().isNotEmpty && values[j].trim().isNotEmpty) {
            item[headers[j].trim()] = values[j].trim();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = FieldValue.serverTimestamp();
          final docRef = FirebaseFirestore.instance.collection(widget.collectionPath).doc();
          batch.set(docRef, item);
          count++;
        }
      }

      await batch.commit();

      // Reload data
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Imported $count items from clipboard')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error importing from clipboard: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    // Check if user is admin
    if (!authService.isAdmin) {
      return const Center(child: Text('Admin access required'));
    }

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      // Check if it's a permission error
      if (_error!.contains('permission-denied')) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.security, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'Firebase Permission Error',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Your Firebase security rules need to be updated to allow database access.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('Try Again'),
                ),
              ],
            ),
          ),
        );
      }
      return Center(child: Text(_error!, style: const TextStyle(color: Colors.red)));
    }

    return Column(
      children: [
        // Toolbar
        Container(
          padding: const EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              ElevatedButton.icon(
                onPressed: _loadData,
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _importFromClipboard,
                icon: const Icon(Icons.paste),
                label: const Text('Import from Excel'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _addItem,
                icon: const Icon(Icons.add),
                label: const Text('Add New Row'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AppConstants.primaryColor.withAlpha(25),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_items.length} items',
                  style: TextStyle(
                    color: AppConstants.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Help text for new users
        if (_items.isEmpty)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue),
                    SizedBox(width: 8),
                    Text(
                      'Excel-like Data Grid',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'To add a new item:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                const Text('1. Click "Add New Row" button'),
                const Text('2. Click on any cell to edit its value'),
                const SizedBox(height: 8),
                const Text(
                  'To import from Excel:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                const Text('1. Copy data from Excel (including headers)'),
                const Text('2. Click "Import from Excel" button'),
              ],
            ),
          ),

        // Excel-like data grid
        Expanded(
          child: Card(
            margin: const EdgeInsets.all(8.0),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // New item row
                Container(
                  padding: const EdgeInsets.all(8.0),
                  color: Colors.grey.shade100,
                  child: Row(
                    children: [
                      const SizedBox(width: 8),
                      const Text(
                        'New Item:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _fixedColumns
                              .where((col) => col != 'id')
                              .map((column) {
                            return SizedBox(
                              width: 200,
                              child: TextField(
                                controller: _newItemControllers[column],
                                decoration: InputDecoration(
                                  labelText: column,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                ),

                // Table header
                Container(
                  color: const Color(0xFFE3F2FD),
                  child: Row(
                    children: [
                      // Action column
                      Container(
                        width: 80,
                        padding: const EdgeInsets.all(8),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: const Text(
                          'Actions',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      // Data columns
                      ...List.generate(_fixedColumns.length, (index) {
                        final column = _fixedColumns[index];
                        return Container(
                          width: 150,
                          padding: const EdgeInsets.all(8),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Text(
                            column,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                ),

                // Table body
                Expanded(
                  child: Scrollbar(
                    controller: _verticalController,
                    thumbVisibility: true,
                    child: Scrollbar(
                      controller: _horizontalController,
                      thumbVisibility: true,
                      notificationPredicate: (notification) => notification.depth == 1,
                      child: SingleChildScrollView(
                        controller: _verticalController,
                        child: SingleChildScrollView(
                          controller: _horizontalController,
                          scrollDirection: Axis.horizontal,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: _items.map((item) {
                              return Row(
                                children: [
                                  // Action column
                                  Container(
                                    width: 80,
                                    height: 40,
                                    padding: const EdgeInsets.all(4),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey.shade300),
                                      color: Colors.grey.shade50,
                                    ),
                                    child: IconButton(
                                      icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                                      onPressed: () => _deleteItem(item['id']),
                                      tooltip: 'Delete item',
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                    ),
                                  ),
                                  // Data columns
                                  ...List.generate(_fixedColumns.length, (index) {
                                    final column = _fixedColumns[index];
                                    final value = item[column]?.toString() ?? '';
                                    final isSelected = _selectedItemId == item['id'] && _selectedColumn == column;

                                    return GestureDetector(
                                      onTap: () {
                                        if (column != 'id') {
                                          _selectCell(item['id'], column);
                                        }
                                      },
                                      child: Container(
                                        width: 150,
                                        height: 40,
                                        padding: const EdgeInsets.symmetric(horizontal: 8),
                                        alignment: Alignment.centerLeft,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: isSelected
                                                ? AppConstants.primaryColor
                                                : Colors.grey.shade300,
                                            width: isSelected ? 2 : 1,
                                          ),
                                          color: isSelected
                                              ? Colors.blue.shade50
                                              : Colors.white,
                                        ),
                                        child: isSelected
                                            ? TextField(
                                                controller: _cellEditController,
                                                focusNode: _cellEditFocusNode,
                                                decoration: const InputDecoration(
                                                  border: InputBorder.none,
                                                  contentPadding: EdgeInsets.zero,
                                                ),
                                                onSubmitted: (_) => _saveSelectedCell(),
                                              )
                                            : Text(
                                                value,
                                                overflow: TextOverflow.ellipsis,
                                                style: TextStyle(
                                                  color: column == 'id'
                                                      ? Colors.grey
                                                      : Colors.black,
                                                  fontStyle: column == 'id'
                                                      ? FontStyle.italic
                                                      : FontStyle.normal,
                                                ),
                                              ),
                                      ),
                                    );
                                  }),
                                ],
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}