import 'package:hive_flutter/hive_flutter.dart';
import 'package:uuid/uuid.dart';

class DatabaseService {
  static const String _tablesBoxName = 'tables';
  static const String _dataBoxPrefix = 'data_';
  static const String _metadataBoxName = 'metadata';
  
  final Uuid _uuid = const Uuid();
  
  // Initialize the database service
  Future<void> initialize() async {
    await Hive.openBox(_tablesBoxName);
    await Hive.openBox(_metadataBoxName);
  }
  
  // Get all table names
  Future<List<String>> getTableNames() async {
    final tablesBox = Hive.box(_tablesBoxName);
    return tablesBox.keys.cast<String>().toList();
  }
  
  // Create a new table
  Future<void> createTable(String tableName, Map<String, String> columns) async {
    final tablesBox = Hive.box(_tablesBoxName);
    final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
    
    // Store table schema
    await tablesBox.put(tableName, {
      'columns': columns,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });
    
    // Clear any existing data
    await dataBox.clear();
  }
  
  // Delete a table
  Future<void> deleteTable(String tableName) async {
    final tablesBox = Hive.box(_tablesBoxName);
    await tablesBox.delete(tableName);
    
    // Delete the data box
    await Hive.deleteBoxFromDisk('$_dataBoxPrefix$tableName');
  }
  
  // Get table schema
  Future<Map<String, String>?> getTableSchema(String tableName) async {
    final tablesBox = Hive.box(_tablesBoxName);
    final tableInfo = tablesBox.get(tableName);
    if (tableInfo != null) {
      return Map<String, String>.from(tableInfo['columns']);
    }
    return null;
  }
  
  // Add a column to existing table
  Future<void> addColumn(String tableName, String columnName, String columnType) async {
    final tablesBox = Hive.box(_tablesBoxName);
    final tableInfo = tablesBox.get(tableName);
    
    if (tableInfo != null) {
      final columns = Map<String, String>.from(tableInfo['columns']);
      columns[columnName] = columnType;
      
      await tablesBox.put(tableName, {
        'columns': columns,
        'created_at': tableInfo['created_at'],
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      // Add default values to existing rows
      final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
      for (String key in dataBox.keys) {
        final row = Map<String, dynamic>.from(dataBox.get(key));
        if (!row.containsKey(columnName)) {
          row[columnName] = _getDefaultValue(columnType);
          await dataBox.put(key, row);
        }
      }
    }
  }
  
  // Remove a column from table
  Future<void> removeColumn(String tableName, String columnName) async {
    final tablesBox = Hive.box(_tablesBoxName);
    final tableInfo = tablesBox.get(tableName);
    
    if (tableInfo != null) {
      final columns = Map<String, String>.from(tableInfo['columns']);
      columns.remove(columnName);
      
      await tablesBox.put(tableName, {
        'columns': columns,
        'created_at': tableInfo['created_at'],
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      // Remove column from existing rows
      final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
      for (String key in dataBox.keys) {
        final row = Map<String, dynamic>.from(dataBox.get(key));
        row.remove(columnName);
        await dataBox.put(key, row);
      }
    }
  }
  
  // Get all rows from a table
  Future<List<Map<String, dynamic>>> getTableData(String tableName) async {
    final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
    final List<Map<String, dynamic>> rows = [];
    
    for (String key in dataBox.keys) {
      final row = Map<String, dynamic>.from(dataBox.get(key));
      row['_id'] = key; // Add the unique ID
      rows.add(row);
    }
    
    return rows;
  }
  
  // Insert a new row
  Future<String> insertRow(String tableName, Map<String, dynamic> data) async {
    final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
    final id = _uuid.v4();
    
    // Add metadata
    data['_created_at'] = DateTime.now().toIso8601String();
    data['_updated_at'] = DateTime.now().toIso8601String();
    
    await dataBox.put(id, data);
    return id;
  }
  
  // Update a row
  Future<void> updateRow(String tableName, String id, Map<String, dynamic> data) async {
    final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
    final existingData = dataBox.get(id);
    
    if (existingData != null) {
      final updatedData = Map<String, dynamic>.from(existingData);
      updatedData.addAll(data);
      updatedData['_updated_at'] = DateTime.now().toIso8601String();
      
      await dataBox.put(id, updatedData);
    }
  }
  
  // Update a single cell
  Future<void> updateCell(String tableName, String id, String column, dynamic value) async {
    final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
    final existingData = dataBox.get(id);
    
    if (existingData != null) {
      final updatedData = Map<String, dynamic>.from(existingData);
      updatedData[column] = value;
      updatedData['_updated_at'] = DateTime.now().toIso8601String();
      
      await dataBox.put(id, updatedData);
    }
  }
  
  // Delete a row
  Future<void> deleteRow(String tableName, String id) async {
    final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
    await dataBox.delete(id);
  }
  
  // Import data from a list of maps
  Future<void> importData(String tableName, List<Map<String, dynamic>> data) async {
    final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
    
    for (final row in data) {
      final id = _uuid.v4();
      row['_created_at'] = DateTime.now().toIso8601String();
      row['_updated_at'] = DateTime.now().toIso8601String();
      await dataBox.put(id, row);
    }
  }
  
  // Clear all data from a table
  Future<void> clearTable(String tableName) async {
    final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
    await dataBox.clear();
  }
  
  // Get table statistics
  Future<Map<String, dynamic>> getTableStats(String tableName) async {
    final dataBox = await Hive.openBox('$_dataBoxPrefix$tableName');
    final tablesBox = Hive.box(_tablesBoxName);
    final tableInfo = tablesBox.get(tableName);
    
    return {
      'row_count': dataBox.length,
      'column_count': tableInfo != null ? (tableInfo['columns'] as Map).length : 0,
      'created_at': tableInfo?['created_at'],
      'updated_at': tableInfo?['updated_at'],
    };
  }
  
  // Search data in a table
  Future<List<Map<String, dynamic>>> searchTable(String tableName, String query) async {
    final data = await getTableData(tableName);
    final lowercaseQuery = query.toLowerCase();
    
    return data.where((row) {
      return row.values.any((value) => 
        value.toString().toLowerCase().contains(lowercaseQuery)
      );
    }).toList();
  }
  
  // Get default value for a column type
  dynamic _getDefaultValue(String columnType) {
    switch (columnType.toLowerCase()) {
      case 'integer':
      case 'int':
        return 0;
      case 'double':
      case 'real':
      case 'float':
        return 0.0;
      case 'boolean':
      case 'bool':
        return false;
      case 'date':
      case 'datetime':
        return DateTime.now().toIso8601String();
      default:
        return '';
    }
  }
  
  // Close all boxes
  Future<void> close() async {
    await Hive.close();
  }
}
