import 'package:flutter/material.dart';
import '../services/supabase_sync_service.dart';

class SyncStatusWidget extends StatefulWidget {
  const SyncStatusWidget({super.key});

  @override
  State<SyncStatusWidget> createState() => _SyncStatusWidgetState();
}

class _SyncStatusWidgetState extends State<SyncStatusWidget> {
  final _supabaseService = SupabaseSyncService();
  SyncStatus _syncStatus = SyncStatus.notInitialized;

  @override
  void initState() {
    super.initState();
    _checkSyncStatus();
  }

  Future<void> _checkSyncStatus() async {
    try {
      final status = await _supabaseService.getSyncStatus();
      if (mounted) {
        setState(() {
          _syncStatus = status;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _syncStatus = SyncStatus.disconnected;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getStatusColor().withOpacity(0.1),
        border: Border.all(
          color: _getStatusColor().withOpacity(0.3),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _getStatusIcon(),
            color: _getStatusColor(),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getStatusTitle(),
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: _getStatusColor(),
                    fontSize: 14,
                  ),
                ),
                Text(
                  _getStatusDescription(),
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          if (_syncStatus == SyncStatus.disconnected || _syncStatus == SyncStatus.notInitialized)
            TextButton(
              onPressed: _checkSyncStatus,
              child: const Text('Retry'),
            ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (_syncStatus) {
      case SyncStatus.connected:
        return Colors.green;
      case SyncStatus.disconnected:
        return Colors.red;
      case SyncStatus.inProgress:
        return Colors.orange;
      case SyncStatus.completed:
        return Colors.blue;
      case SyncStatus.failed:
        return Colors.red;
      case SyncStatus.notInitialized:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (_syncStatus) {
      case SyncStatus.connected:
        return Icons.cloud_done;
      case SyncStatus.disconnected:
        return Icons.cloud_off;
      case SyncStatus.inProgress:
        return Icons.cloud_sync;
      case SyncStatus.completed:
        return Icons.cloud_done;
      case SyncStatus.failed:
        return Icons.error;
      case SyncStatus.notInitialized:
        return Icons.cloud_queue;
    }
  }

  String _getStatusTitle() {
    switch (_syncStatus) {
      case SyncStatus.connected:
        return 'Supabase Connected';
      case SyncStatus.disconnected:
        return 'Supabase Disconnected';
      case SyncStatus.inProgress:
        return 'Syncing...';
      case SyncStatus.completed:
        return 'Sync Completed';
      case SyncStatus.failed:
        return 'Sync Failed';
      case SyncStatus.notInitialized:
        return 'Supabase Not Configured';
    }
  }

  String _getStatusDescription() {
    switch (_syncStatus) {
      case SyncStatus.connected:
        return 'Ready to sync with cloud database';
      case SyncStatus.disconnected:
        return 'Unable to connect to Supabase. Check your internet connection.';
      case SyncStatus.inProgress:
        return 'Synchronizing data with Supabase...';
      case SyncStatus.completed:
        return 'All data synchronized successfully';
      case SyncStatus.failed:
        return 'Synchronization failed. Please try again.';
      case SyncStatus.notInitialized:
        return 'Configure Supabase credentials to enable cloud sync';
    }
  }
}
