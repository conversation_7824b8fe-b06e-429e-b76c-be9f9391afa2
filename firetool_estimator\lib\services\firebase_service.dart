// Mock implementation of Firebase service
import '../models/project.dart';
import '../models/system_catalog.dart';

class User {
  final String uid;
  final String? email;

  User({required this.uid, this.email});
}

class MaterialItem {
  final String id;
  final String name;
  final String description;
  final double price;

  MaterialItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
    };
  }

  factory MaterialItem.fromMap(Map<String, dynamic> map) {
    return MaterialItem(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
    );
  }
}

class FirebaseService {
  User? _currentUser;
  final Map<String, List<Map<String, dynamic>>> _mockData = {
    'users': [],
    'projects': [],
    'system_catalog': [],
    'materials': [],
  };

  // Constructor
  FirebaseService() {
    _initializeMockData();
  }

  // Initialize mock data
  void _initializeMockData() {
    // Add some mock materials
    _mockData['materials'] = List.generate(
      10,
      (index) => {
        'id': 'material-$index',
        'name': 'Material $index',
        'description': 'Description for material $index',
        'price': (index + 1) * 100.0,
      },
    );

    // Add some mock system catalog items
    _mockData['system_catalog'] = List.generate(
      5,
      (index) => {
        'id': 'system-$index',
        'name': 'System $index',
        'description': 'Description for system $index',
        'type': index % 2 == 0 ? 'Water' : 'Foam',
      },
    );
  }

  // Authentication methods
  Future<User?> signIn(String email, String password) async {
    try {
      // Create a mock user
      _currentUser = User(
        uid: 'mock-uid-${DateTime.now().millisecondsSinceEpoch}',
        email: email,
      );
      return _currentUser;
    } catch (e) {
      print('Error signing in: $e');
      return null;
    }
  }

  Future<User?> signUp(String email, String password) async {
    try {
      // Create a mock user
      _currentUser = User(
        uid: 'mock-uid-${DateTime.now().millisecondsSinceEpoch}',
        email: email,
      );

      // Add to mock data
      _mockData['users'].add({
        'id': _currentUser!.uid,
        'email': email,
        'createdAt': DateTime.now().toString(),
      });

      return _currentUser;
    } catch (e) {
      print('Error signing up: $e');
      return null;
    }
  }

  Future<void> signOut() async {
    _currentUser = null;
  }

  // Get current user
  User? getCurrentUser() {
    return _currentUser;
  }

  // Project methods
  Future<List<Project>> getProjects() async {
    try {
      if (_currentUser == null) return [];

      // Return mock projects
      if (!_mockData.containsKey('projects')) {
        _mockData['projects'] = [];
      }

      return _mockData['projects']!.map((data) {
        return Project.fromMap(Map<String, dynamic>.from(data));
      }).toList();
    } catch (e) {
      print('Error getting projects: $e');
      return [];
    }
  }

  Future<String?> addProject(Project project) async {
    try {
      if (_currentUser == null) return null;

      // Generate a new ID
      final String id = 'project-${DateTime.now().millisecondsSinceEpoch}';

      // Create a copy of the project with the new ID
      final Map<String, dynamic> projectMap = project.toMap();
      projectMap['id'] = id;

      // Add to mock data
      if (!_mockData.containsKey('projects')) {
        _mockData['projects'] = [];
      }

      _mockData['projects']!.add(projectMap);

      return id;
    } catch (e) {
      print('Error adding project: $e');
      return null;
    }
  }

  Future<bool> updateProject(Project project) async {
    try {
      if (_currentUser == null) return false;

      // Find the project in mock data
      if (!_mockData.containsKey('projects')) {
        return false;
      }

      final int index = _mockData['projects']!.indexWhere(
        (p) => p['id'] == project.id
      );

      if (index == -1) {
        return false;
      }

      // Update the project
      _mockData['projects']![index] = project.toMap();

      return true;
    } catch (e) {
      print('Error updating project: $e');
      return false;
    }
  }

  Future<bool> deleteProject(String projectId) async {
    try {
      if (_currentUser == null) return false;

      // Find and remove the project from mock data
      if (!_mockData.containsKey('projects')) {
        return false;
      }

      final int index = _mockData['projects']!.indexWhere(
        (p) => p['id'] == projectId
      );

      if (index == -1) {
        return false;
      }

      // Remove the project
      _mockData['projects']!.removeAt(index);

      return true;
    } catch (e) {
      print('Error deleting project: $e');
      return false;
    }
  }

  // System catalog methods
  Future<List<SystemCatalogItem>> getSystemCatalog() async {
    try {
      // Return mock system catalog items
      if (!_mockData.containsKey('system_catalog')) {
        _mockData['system_catalog'] = [];
      }

      return _mockData['system_catalog']!.map((data) {
        return SystemCatalogItem.fromMap(Map<String, dynamic>.from(data));
      }).toList();
    } catch (e) {
      print('Error getting system catalog: $e');
      return [];
    }
  }

  Future<String?> addSystemCatalogItem(SystemCatalogItem item) async {
    try {
      // Generate a new ID
      final String id = 'system-${DateTime.now().millisecondsSinceEpoch}';

      // Create a copy of the item with the new ID
      final Map<String, dynamic> itemMap = item.toMap();
      itemMap['id'] = id;

      // Add to mock data
      if (!_mockData.containsKey('system_catalog')) {
        _mockData['system_catalog'] = [];
      }

      _mockData['system_catalog']!.add(itemMap);

      return id;
    } catch (e) {
      print('Error adding system catalog item: $e');
      return null;
    }
  }

  // Material methods
  Future<List<MaterialItem>> getMaterials() async {
    try {
      // Return mock materials
      if (!_mockData.containsKey('materials')) {
        _mockData['materials'] = [];
      }

      return _mockData['materials']!.map((data) {
        return MaterialItem.fromMap(Map<String, dynamic>.from(data));
      }).toList();
    } catch (e) {
      print('Error getting materials: $e');
      return [];
    }
  }

  Future<String?> addMaterial(MaterialItem material) async {
    try {
      // Generate a new ID
      final String id = 'material-${DateTime.now().millisecondsSinceEpoch}';

      // Create a copy of the material with the new ID
      final Map<String, dynamic> materialMap = material.toMap();
      materialMap['id'] = id;

      // Add to mock data
      if (!_mockData.containsKey('materials')) {
        _mockData['materials'] = [];
      }

      _mockData['materials']!.add(materialMap);

      return id;
    } catch (e) {
      print('Error adding material: $e');
      return null;
    }
  }

  Future<bool> updateMaterial(MaterialItem material) async {
    try {
      // Find the material in mock data
      if (!_mockData.containsKey('materials')) {
        return false;
      }

      final int index = _mockData['materials']!.indexWhere(
        (m) => m['id'] == material.id
      );

      if (index == -1) {
        return false;
      }

      // Update the material
      _mockData['materials']![index] = material.toMap();

      return true;
    } catch (e) {
      print('Error updating material: $e');
      return false;
    }
  }

  Future<bool> deleteMaterial(String materialId) async {
    try {
      // Find and remove the material from mock data
      if (!_mockData.containsKey('materials')) {
        return false;
      }

      final int index = _mockData['materials']!.indexWhere(
        (m) => m['id'] == materialId
      );

      if (index == -1) {
        return false;
      }

      // Remove the material
      _mockData['materials']!.removeAt(index);

      return true;
    } catch (e) {
      print('Error deleting material: $e');
      return false;
    }
  }
}
