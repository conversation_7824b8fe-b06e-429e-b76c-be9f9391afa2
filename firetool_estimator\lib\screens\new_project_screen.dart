import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../services/project_provider.dart';
import 'project_detail_screen.dart';

class NewProjectScreen extends StatefulWidget {
  const NewProjectScreen({super.key});

  @override
  State<NewProjectScreen> createState() => _NewProjectScreenState();
}

class _NewProjectScreenState extends State<NewProjectScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _clientNameController = TextEditingController();
  final _exchangeRateController = TextEditingController(text: '3.75'); // Default USD to SAR

  bool _isCreating = false;
  String _selectedCurrency = 'SAR'; // Default currency

  // List of available currencies
  final List<String> _currencies = ['SAR', 'USD', 'EUR', 'GBP', 'AED', 'KWD', 'QAR', 'BHD', 'OMR'];

  @override
  void dispose() {
    _nameController.dispose();
    _clientNameController.dispose();
    _exchangeRateController.dispose();
    super.dispose();
  }

  Future<void> _createProject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      await Provider.of<ProjectProvider>(context, listen: false).createProject(
        name: _nameController.text.trim(),
        clientName: _clientNameController.text.trim(),
        currency: _selectedCurrency,
        exchangeRate: double.tryParse(_exchangeRateController.text) ?? 3.75,
      );

      if (!mounted) return;

      // Navigate to project detail screen
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const ProjectDetailScreen(),
        ),
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error creating project: $e')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('New Project'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Project Name',
                  hintText: 'Enter project name',
                  prefixIcon: Icon(Icons.assignment),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a project name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _clientNameController,
                decoration: const InputDecoration(
                  labelText: 'Client Name (Optional)',
                  hintText: 'Enter client name',
                  prefixIcon: Icon(Icons.person),
                ),
                // No validator - client name is optional
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Currency',
                  prefixIcon: Icon(Icons.currency_exchange),
                ),
                value: _selectedCurrency,
                items: _currencies.map((currency) {
                  return DropdownMenuItem<String>(
                    value: currency,
                    child: Text(currency),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedCurrency = value;
                      // Update exchange rate based on selected currency
                      if (value == 'SAR') {
                        _exchangeRateController.text = '3.75';
                      } else if (value == 'AED') {
                        _exchangeRateController.text = '3.67';
                      } else if (value == 'KWD') {
                        _exchangeRateController.text = '0.31';
                      } else if (value == 'QAR') {
                        _exchangeRateController.text = '3.64';
                      } else if (value == 'BHD') {
                        _exchangeRateController.text = '0.38';
                      } else if (value == 'OMR') {
                        _exchangeRateController.text = '0.38';
                      } else if (value == 'EUR') {
                        _exchangeRateController.text = '0.92';
                      } else if (value == 'GBP') {
                        _exchangeRateController.text = '0.79';
                      } else {
                        _exchangeRateController.text = '1.00'; // USD
                      }
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _exchangeRateController,
                decoration: const InputDecoration(
                  labelText: 'Exchange Rate (USD to Local)',
                  hintText: 'Enter exchange rate from USD',
                  prefixIcon: Icon(Icons.currency_exchange),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an exchange rate';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: _isCreating ? null : _createProject,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isCreating
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text('Create Project', style: TextStyle(fontSize: 16)),
              ),
              const SizedBox(height: 16),
              Consumer<ProjectProvider>(
                builder: (context, projectProvider, child) {
                  if (projectProvider.error != null) {
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        projectProvider.error!,
                        style: const TextStyle(color: AppConstants.errorColor),
                        textAlign: TextAlign.center,
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
