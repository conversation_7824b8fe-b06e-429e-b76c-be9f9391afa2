import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart';
import 'dart:io';
import 'sqlite_schema_manager.dart';

/// Example class demonstrating how to use the SQLiteSchemaManager
class SQLiteSchemaExample {
  late Database _db;
  
  /// Initialize the database
  Future<void> initDatabase() async {
    // Initialize FFI for desktop platforms
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }
    
    // Get the database path
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'example.db');
    
    // Open the database
    _db = await openDatabase(
      path,
      version: 1,
      onCreate: (Database db, int version) async {
        // Create a simple table
        await db.execute('''
          CREATE TABLE example (
            id INTEGER PRIMARY KEY,
            name TEXT,
            age INTEGER
          )
        ''');
        
        // Insert some sample data
        await db.insert('example', {'name': '<PERSON>', 'age': 30});
        await db.insert('example', {'name': '<PERSON>', 'age': 25});
      },
    );
  }
  
  /// Example of adding a column
  Future<void> addNotesColumn() async {
    final result = await SQLiteSchemaManager.addColumn(
      _db,
      'example',
      'notes',
      'TEXT',
    );
    
    if (result) {
      debugPrint('Successfully added notes column');
    } else {
      debugPrint('Failed to add notes column');
    }
  }
  
  /// Example of updating a cell
  Future<void> updateNotesForRow() async {
    final rowsUpdated = await SQLiteSchemaManager.updateCell(
      _db,
      'example',
      'notes',
      'This is a sample note',
      'id = ?',
      [1], // Update the row with id = 1
    );
    
    debugPrint('Updated $rowsUpdated row(s)');
  }
  
  /// Example of dropping a column
  Future<void> dropAgeColumn() async {
    final result = await SQLiteSchemaManager.dropColumn(
      _db,
      'example',
      'age',
    );
    
    if (result) {
      debugPrint('Successfully dropped age column');
    } else {
      debugPrint('Failed to drop age column');
    }
  }
  
  /// Example of importing data from a CSV file
  Future<void> importFromCSV(String filePath) async {
    final rowsImported = await SQLiteSchemaManager.importFromCSV(
      _db,
      'example',
      filePath,
      replaceExisting: false, // Append to existing data
    );
    
    debugPrint('Imported $rowsImported row(s) from CSV');
  }
  
  /// Example of exporting data to a CSV file
  Future<void> exportToCSV(String filePath) async {
    final rowsExported = await SQLiteSchemaManager.exportToCSV(
      _db,
      'example',
      filePath,
    );
    
    debugPrint('Exported $rowsExported row(s) to CSV');
  }
  
  /// Example of importing data from an Excel file
  Future<void> importFromExcel(String filePath) async {
    final rowsImported = await SQLiteSchemaManager.importFromExcel(
      _db,
      'example',
      filePath,
      replaceExisting: false, // Append to existing data
    );
    
    debugPrint('Imported $rowsImported row(s) from Excel');
  }
  
  /// Example of exporting data to an Excel file
  Future<void> exportToExcel(String filePath) async {
    final rowsExported = await SQLiteSchemaManager.exportToExcel(
      _db,
      'example',
      filePath,
    );
    
    debugPrint('Exported $rowsExported row(s) to Excel');
  }
  
  /// Example of using the file picker to import data
  Future<void> importFromFilePicker() async {
    final rowsImported = await SQLiteSchemaManager.importFromFile(
      _db,
      'example',
      replaceExisting: false, // Append to existing data
    );
    
    debugPrint('Imported $rowsImported row(s) from selected file');
  }
  
  /// Example of using the file picker to export data
  Future<void> exportToFilePicker() async {
    final rowsExported = await SQLiteSchemaManager.exportToFile(
      _db,
      'example',
    );
    
    debugPrint('Exported $rowsExported row(s) to files');
  }
  
  /// Run all examples
  Future<void> runAllExamples() async {
    await initDatabase();
    
    // Add a notes column
    await addNotesColumn();
    
    // Update the notes for a specific row
    await updateNotesForRow();
    
    // Drop the age column
    await dropAgeColumn();
    
    // Close the database
    await _db.close();
  }
}
