import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../models/project.dart';
import '../services/project_provider.dart';
import '../services/database_service.dart';
import '../widgets/simple_dropdown.dart';

class AddMaterialScreen extends StatefulWidget {
  final String systemId;

  const AddMaterialScreen({super.key, required this.systemId});

  @override
  State<AddMaterialScreen> createState() => _AddMaterialScreenState();
}

class _AddMaterialScreenState extends State<AddMaterialScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _quantityController = TextEditingController();
  final _exWorksUnitCostController = TextEditingController();
  final _localUnitCostController = TextEditingController();
  final _installationUnitCostController = TextEditingController();
  final _vendorController = TextEditingController();
  final _approvalController = TextEditingController();

  String _selectedCategory = '';
  String _selectedUnit = AppConstants.units.first;
  bool _isImported = true;

  // For sample material selection
  List<MaterialItem> _availableSampleMaterials = [];
  final DatabaseService _databaseService = DatabaseService();

  // Exchange rate
  double _exchangeRate = 3.75; // Default

  @override
  void initState() {
    super.initState();
    _initializeCategories();

    // Load materials after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSampleMaterials();
    });

    _getExchangeRate();

    // Set default values
    _quantityController.text = '1';
    _exWorksUnitCostController.text = '0';
    _localUnitCostController.text = '0';
    _installationUnitCostController.text = '0';
  }

  void _initializeCategories() {
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
    final system = projectProvider.currentProject?.systems
        .firstWhere((s) => s.id == widget.systemId);

    if (system != null) {
      final categories = AppConstants.materialCategories[system.type] ?? [];
      if (categories.isNotEmpty) {
        setState(() {
          _selectedCategory = categories.first;
        });
      }
    }
  }

  Future<void> _loadSampleMaterials() async {
    try {
      // Load materials from database
      final materials = await _databaseService.getAllMaterials();

      debugPrint('Loaded ${materials.length} materials from database');

      if (!mounted) return;

      // If no materials in database, add sample materials
      if (materials.isEmpty) {
        debugPrint('No materials found in database, adding sample materials');

        // Add sample materials from AppConstants
        for (var systemType in ['Fire Alarm', 'Water Sprinkler', 'Foam System']) {
          for (var material in AppConstants.sampleMaterialsBySystem[systemType] ?? []) {
            final materialItem = MaterialItem(
              name: material['name'] as String,
              category: material['category'] as String,
              description: material['description'] as String? ?? '',
              quantity: 0,
              unit: material['unit'] as String,
              exWorksUnitCost: material['exWorksUnitCost'] as double,
              localUnitCost: material['localUnitCost'] as double,
              installationUnitCost: material['installationUnitCost'] as double,
              isImported: material['isImported'] as bool? ?? true,
              vendor: material['vendor'] as String? ?? '',
              approval: material['approval'] as String? ?? '',
            );

            await _databaseService.insertMaterial(materialItem);
          }
        }

        // Load materials again
        final updatedMaterials = await _databaseService.getAllMaterials();
        debugPrint('Added sample materials, now have ${updatedMaterials.length} materials');

        if (!mounted) return;

        setState(() {
          _availableSampleMaterials = updatedMaterials;
        });
        return;
      }

      // Filter materials by system type if needed
      final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
      final system = projectProvider.currentProject?.systems
          .firstWhere((s) => s.id == widget.systemId);

      if (system != null) {
        debugPrint('Filtering materials for system type: ${system.type}');

        // Filter materials by system type if needed
        final filteredMaterials = materials.where((m) =>
          m.category.toLowerCase().contains(system.type.toLowerCase()) ||
          system.type.toLowerCase().contains(m.category.toLowerCase())
        ).toList();

        debugPrint('Filtered to ${filteredMaterials.length} materials');

        if (mounted) {
          setState(() {
            _availableSampleMaterials = filteredMaterials;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _availableSampleMaterials = materials;
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading materials: $e');
    }
  }

  void _getExchangeRate() {
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
    if (projectProvider.currentProject != null) {
      setState(() {
        _exchangeRate = projectProvider.currentProject!.exchangeRate;
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _quantityController.dispose();
    _exWorksUnitCostController.dispose();
    _localUnitCostController.dispose();
    _installationUnitCostController.dispose();
    _vendorController.dispose();
    _approvalController.dispose();
    super.dispose();
  }

  void _addMaterial() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final material = MaterialItem(
      name: _nameController.text.trim(),
      category: _selectedCategory,
      description: _descriptionController.text.trim(),
      quantity: double.tryParse(_quantityController.text) ?? 0,
      unit: _selectedUnit,
      exWorksUnitCost: double.tryParse(_exWorksUnitCostController.text) ?? 0,
      localUnitCost: double.tryParse(_localUnitCostController.text) ?? 0,
      installationUnitCost: double.tryParse(_installationUnitCostController.text) ?? 0,
      isImported: _isImported,
      vendor: _vendorController.text.trim(),
      approval: _approvalController.text.trim(),
    );

    Provider.of<ProjectProvider>(context, listen: false)
        .addMaterial(widget.systemId, material);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Material added')),
    );

    Navigator.pop(context);
  }

  void _selectSampleMaterial(MaterialItem? material) {
    if (material == null) return;

    debugPrint('Selected material: ${material.name}');

    // Use Future.delayed to ensure the UI updates after the dropdown closes
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          // Directly populate all fields
          _nameController.text = material.name;
          _selectedCategory = material.category;
          _descriptionController.text = material.description;
          _selectedUnit = material.unit;
          _exWorksUnitCostController.text = material.exWorksUnitCost.toString();
          _localUnitCostController.text = material.localUnitCost.toString();
          _installationUnitCostController.text = material.installationUnitCost.toString();
          _isImported = material.isImported;

          // Set vendor and approval
          _vendorController.text = material.vendor;
          _approvalController.text = material.approval;

          // Set quantity to 1 by default
          _quantityController.text = '1';

          // Log the update for debugging
          debugPrint('Updated fields with material: ${material.name}');
          debugPrint('Description: ${material.description}');
          debugPrint('Unit: ${material.unit}');
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final projectProvider = Provider.of<ProjectProvider>(context);
    final system = projectProvider.currentProject?.systems
        .firstWhere((s) => s.id == widget.systemId);

    if (system == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Add Material')),
        body: const Center(child: Text('System not found')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Material'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              const Text(
                'Select Material',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Model',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey.shade50,
                    ),
                    child: SimpleDropdown<MaterialItem>(
                      items: _availableSampleMaterials,
                      value: _availableSampleMaterials.isNotEmpty && _nameController.text.isNotEmpty
                          ? _availableSampleMaterials.firstWhere(
                              (item) => item.name == _nameController.text,
                              orElse: () => _availableSampleMaterials.first,
                            )
                          : null,
                      displayItemFn: (item) => item.name,
                      displayCategoryFn: (item) => item.category,
                      showCategory: true,
                      hintText: 'Select a model from the catalog',
                      onChanged: _selectSampleMaterial,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Description',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey.shade50,
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          child: const Icon(
                            Icons.description,
                            color: Colors.grey,
                            size: 22,
                          ),
                        ),
                        Expanded(
                          child: TextFormField(
                            controller: _descriptionController,
                            decoration: const InputDecoration(
                              hintText: 'Enter material description',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(vertical: 16),
                            ),
                            maxLines: 2,
                            style: const TextStyle(
                              fontSize: 16,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Quantity',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 50,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey.shade50,
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          child: const Text(
                            '#',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        Expanded(
                          child: TextFormField(
                            controller: _quantityController,
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                              hintText: 'Enter quantity',
                            ),
                            keyboardType: TextInputType.number, // Integer only
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly, // Allow only digits
                            ],
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter a quantity';
                              }
                              if (int.tryParse(value) == null) {
                                return 'Please enter a valid number';
                              }
                              return null;
                            },
                            style: const TextStyle(
                              fontSize: 16,
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            InkWell(
                              onTap: () {
                                final currentValue = int.tryParse(_quantityController.text) ?? 0;
                                if (currentValue > 0) {
                                  setState(() {
                                    _quantityController.text = (currentValue - 1).toString();
                                  });
                                }
                              },
                              child: Container(
                                width: 36,
                                height: 36,
                                margin: const EdgeInsets.only(right: 8),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade300,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.remove,
                                  color: Colors.black54,
                                  size: 20,
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                final currentValue = int.tryParse(_quantityController.text) ?? 0;
                                setState(() {
                                  _quantityController.text = (currentValue + 1).toString();
                                });
                              },
                              child: Container(
                                width: 36,
                                height: 36,
                                margin: const EdgeInsets.only(right: 12),
                                decoration: BoxDecoration(
                                  color: Colors.blue,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.add,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Unit',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          child: const Icon(
                            Icons.straighten,
                            color: Colors.grey,
                          ),
                        ),
                        Expanded(
                          child: SimpleDropdown<String>(
                            items: AppConstants.units,
                            value: _selectedUnit,
                            displayItemFn: (item) => item,
                            hintText: 'Select unit',
                            onChanged: (value) {
                              setState(() {
                                _selectedUnit = value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Vendor/Manufacturer',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          child: const Icon(
                            Icons.business,
                            color: Colors.grey,
                          ),
                        ),
                        Expanded(
                          child: TextFormField(
                            controller: _vendorController,
                            decoration: const InputDecoration(
                              hintText: 'Enter vendor or manufacturer name',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Approval/Certification',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          child: const Icon(
                            Icons.verified,
                            color: Colors.grey,
                          ),
                        ),
                        Expanded(
                          child: TextFormField(
                            controller: _approvalController,
                            decoration: const InputDecoration(
                              hintText: 'Enter approval or certification info',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Ex-Works Unit Cost (USD)',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          child: const Text(
                            '\$',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        Expanded(
                          child: TextFormField(
                            controller: _exWorksUnitCostController,
                            decoration: const InputDecoration(
                              hintText: 'Enter ex-works cost in USD',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter an ex-works unit cost';
                              }
                              if (double.tryParse(value) == null) {
                                return 'Please enter a valid number';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Text(
                  'Exchange Rate: \$1 = SAR ${_exchangeRate.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                ),
              ),
              const SizedBox(height: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Local Unit Cost (SAR)',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          child: const Text(
                            'SAR',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        Expanded(
                          child: TextFormField(
                            controller: _localUnitCostController,
                            decoration: const InputDecoration(
                              hintText: 'Enter additional local cost',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter a local unit cost (or 0 if none)';
                              }
                              if (double.tryParse(value) == null) {
                                return 'Please enter a valid number';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Installation Unit Cost (SAR)',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          child: const Icon(
                            Icons.build,
                            color: Colors.grey,
                          ),
                        ),
                        Expanded(
                          child: TextFormField(
                            controller: _installationUnitCostController,
                            decoration: const InputDecoration(
                              hintText: 'Enter installation cost',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter an installation unit cost (or 0 if none)';
                              }
                              if (double.tryParse(value) == null) {
                                return 'Please enter a valid number';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Imported Item'),
                subtitle: const Text('Toggle if this item is imported'),
                value: _isImported,
                onChanged: (value) {
                  setState(() {
                    _isImported = value;
                  });
                },
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: _addMaterial,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Add Material', style: TextStyle(fontSize: 16)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
