import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../constants/app_constants.dart';

class MockAdminDataGrid extends StatefulWidget {
  final String collectionPath;

  const MockAdminDataGrid({
    super.key,
    required this.collectionPath,
  });

  @override
  State<MockAdminDataGrid> createState() => _MockAdminDataGridState();
}

class _MockAdminDataGridState extends State<MockAdminDataGrid> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  List<Map<String, dynamic>> _items = [];
  List<String> _columns = [];
  bool _isLoading = false;
  String? _error;

  // For adding new items
  final Map<String, TextEditingController> _newItemControllers = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();

    // Dispose all text controllers
    for (var controller in _newItemControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Mock data based on collection path
      final items = _getMockData(widget.collectionPath);

      // Extract all possible columns from all items
      final Set<String> columns = {'id'};
      for (var item in items) {
        columns.addAll(item.keys);
      }

      // Create controllers for new item
      for (var column in columns) {
        if (column != 'id') {
          _newItemControllers[column] = TextEditingController();
        }
      }

      setState(() {
        _items = items;
        _columns = columns.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> _getMockData(String collectionPath) {
    // Return different mock data based on collection path
    switch (collectionPath) {
      case 'users':
        return [
          {'id': '1', 'username': 'admin', 'role': 'admin', 'lastLoginAt': DateTime.now().toString()},
          {'id': '2', 'username': 'user1', 'role': 'user', 'lastLoginAt': DateTime.now().toString()},
        ];
      case 'projects':
        return [
          {'id': '1', 'name': 'Project 1', 'clientName': 'Client A', 'createdAt': DateTime.now().toString()},
          {'id': '2', 'name': 'Project 2', 'clientName': 'Client B', 'createdAt': DateTime.now().toString()},
        ];
      default:
        return [
          {'id': '1', 'name': 'Item 1', 'description': 'Description 1', 'createdAt': DateTime.now().toString()},
          {'id': '2', 'name': 'Item 2', 'description': 'Description 2', 'createdAt': DateTime.now().toString()},
        ];
    }
  }

  Future<void> _addItem() async {
    try {
      final Map<String, dynamic> newItem = {};

      // Collect values from controllers
      for (var entry in _newItemControllers.entries) {
        if (entry.value.text.isNotEmpty) {
          newItem[entry.key] = entry.value.text;
        }
      }

      if (newItem.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please fill at least one field')),
        );
        return;
      }

      // Add timestamp and ID
      newItem['createdAt'] = DateTime.now().toString();
      newItem['id'] = (_items.length + 1).toString();

      // Add to items
      setState(() {
        _items.add(newItem);
      });

      // Clear controllers
      for (var controller in _newItemControllers.values) {
        controller.clear();
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Item added with ID: ${newItem['id']}')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding item: $e')),
      );
    }
  }

  Future<void> _updateItem(String id, String field, String value) async {
    try {
      setState(() {
        final index = _items.indexWhere((item) => item['id'] == id);
        if (index != -1) {
          _items[index][field] = value;
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Item updated')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating item: $e')),
      );
    }
  }

  Future<void> _deleteItem(String id) async {
    try {
      setState(() {
        _items.removeWhere((item) => item['id'] == id);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Item deleted')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting item: $e')),
      );
    }
  }

  Future<void> _importFromClipboard() async {
    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data == null || data.text == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No data in clipboard')),
        );
        return;
      }

      // Parse clipboard data (assuming tab-separated values from Excel)
      final rows = data.text!.split('\n');
      if (rows.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No rows found in clipboard data')),
        );
        return;
      }

      // Parse header row
      final headers = rows[0].split('\t');
      
      // Process data rows
      int count = 0;
      List<Map<String, dynamic>> newItems = [];

      for (int i = 1; i < rows.length; i++) {
        if (rows[i].trim().isEmpty) continue;

        final values = rows[i].split('\t');
        if (values.length != headers.length) continue;

        final Map<String, dynamic> item = {};
        for (int j = 0; j < headers.length; j++) {
          if (headers[j].trim().isNotEmpty && values[j].trim().isNotEmpty) {
            item[headers[j].trim()] = values[j].trim();
          }
        }

        if (item.isNotEmpty) {
          item['createdAt'] = DateTime.now().toString();
          item['id'] = (_items.length + newItems.length + 1).toString();
          newItems.add(item);
          count++;
        }
      }

      setState(() {
        _items.addAll(newItems);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Imported $count items from clipboard')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error importing from clipboard: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    // Check if user is admin
    if (!authService.isAdmin) {
      return const Center(child: Text('Admin access required'));
    }

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(child: Text(_error!, style: const TextStyle(color: Colors.red)));
    }

    return Column(
      children: [
        // Toolbar
        Container(
          padding: const EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              ElevatedButton.icon(
                onPressed: _loadData,
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _importFromClipboard,
                icon: const Icon(Icons.paste),
                label: const Text('Import from Clipboard'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AppConstants.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_items.length} items',
                  style: TextStyle(
                    color: AppConstants.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Data grid
        Expanded(
          child: Card(
            margin: const EdgeInsets.all(8.0),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Scrollbar(
              controller: _verticalController,
              thumbVisibility: true,
              child: Scrollbar(
                controller: _horizontalController,
                thumbVisibility: true,
                notificationPredicate: (notification) => notification.depth == 1,
                child: SingleChildScrollView(
                  controller: _verticalController,
                  child: SingleChildScrollView(
                    controller: _horizontalController,
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      headingRowColor: WidgetStateProperty.all(
                        const Color(0xFFE3F2FD),
                      ),
                      dataRowColor: WidgetStateProperty.all(
                        Colors.white,
                      ),
                      columns: [
                        const DataColumn(
                          label: Text(
                            'Actions',
                            style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
                          ),
                        ),
                        ..._columns.map((column) => DataColumn(
                          label: Text(
                            column,
                            style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
                          ),
                        )),
                      ],
                      rows: [
                        // New item row with better styling
                        DataRow(
                          color: WidgetStateProperty.all(const Color(0xFFF5F5F5)),
                          cells: [
                            DataCell(
                              Tooltip(
                                message: 'Add new item (fill at least one field first)',
                                child: IconButton(
                                  icon: const Icon(Icons.add_circle, color: Colors.green, size: 28),
                                  onPressed: _addItem,
                                ),
                              ),
                            ),
                            ..._columns.map((column) {
                              if (column == 'id') {
                                return const DataCell(Text('(auto)', style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey)));
                              }
                              return DataCell(
                                TextField(
                                  controller: _newItemControllers[column],
                                  decoration: InputDecoration(
                                    hintText: 'Enter ${column.replaceAll('_', ' ')}',
                                    border: InputBorder.none,
                                  ),
                                ),
                              );
                            }),
                          ],
                        ),
                        // Existing items
                        ..._items.map((item) {
                          return DataRow(
                            cells: [
                              DataCell(
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Tooltip(
                                      message: 'Delete item',
                                      child: IconButton(
                                        icon: const Icon(Icons.delete, color: Colors.red),
                                        onPressed: () => _deleteItem(item['id']),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ..._columns.map((column) {
                                final value = item[column]?.toString() ?? '';
                                if (column == 'id') {
                                  return DataCell(
                                    Text(
                                      value,
                                      style: const TextStyle(
                                        color: Colors.grey,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  );
                                }
                                return DataCell(
                                  TextField(
                                    controller: TextEditingController(text: value),
                                    decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Enter ${column.replaceAll('_', ' ')}',
                                      hintStyle: const TextStyle(
                                        fontStyle: FontStyle.italic,
                                        color: Colors.grey,
                                      ),
                                    ),
                                    onSubmitted: (newValue) {
                                      if (column != 'id' && newValue != value) {
                                        _updateItem(item['id'], column, newValue);
                                      }
                                    },
                                  ),
                                );
                              }),
                            ],
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
