import 'package:flutter/material.dart';

class ImprovedDropdown<T> extends StatefulWidget {
  final List<T> items;
  final T? value;
  final String Function(T) displayItemFn;
  final String Function(T)? displayCategoryFn;
  final String Function(T)? displayDescriptionFn;
  final Function(T?) onChanged;
  final String hintText;
  final bool isExpanded;
  final bool Function(T, String)? searchMatcher;
  final Widget? icon;
  final String? labelText;
  final bool enabled;
  final bool showCategory;
  final bool showDescription;

  const ImprovedDropdown({
    super.key,
    required this.items,
    required this.displayItemFn,
    required this.onChanged,
    this.displayCategoryFn,
    this.displayDescriptionFn,
    this.value,
    this.hintText = 'Select an item',
    this.isExpanded = true,
    this.searchMatcher,
    this.icon,
    this.labelText,
    this.enabled = true,
    this.showCategory = false,
    this.showDescription = false,
  });

  @override
  State<ImprovedDropdown<T>> createState() => _ImprovedDropdownState<T>();
}

class _ImprovedDropdownState<T> extends State<ImprovedDropdown<T>> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  List<T> _filteredItems = [];
  T? _selectedItem;

  @override
  void initState() {
    super.initState();
    _filteredItems = List.from(widget.items);
    _selectedItem = widget.value;

    if (_selectedItem != null) {
      _searchController.text = widget.displayItemFn(_selectedItem as T);
    }

    _focusNode.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      _openDropdown();
    } else {
      // Add a small delay to allow item selection to complete
      Future.delayed(const Duration(milliseconds: 200), () {
        if (!_focusNode.hasFocus) {
          _closeDropdown();
        }
      });
    }
  }

  @override
  void didUpdateWidget(ImprovedDropdown<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      _filteredItems = List.from(widget.items);
    }

    if (oldWidget.value != widget.value) {
      _selectedItem = widget.value;
      if (_selectedItem != null) {
        _searchController.text = widget.displayItemFn(_selectedItem as T);
      }
    }
  }

  @override
  void dispose() {
    _closeDropdown();
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _openDropdown() {
    if (!_isOpen) {
      _isOpen = true;
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
    }
  }

  void _closeDropdown() {
    if (_isOpen) {
      _isOpen = false;
      _overlayEntry?.remove();
      _overlayEntry = null;
    }
  }

  void _filterItems(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredItems = List.from(widget.items);
      });
    } else {
      setState(() {
        if (widget.searchMatcher != null) {
          _filteredItems = widget.items
              .where((item) => widget.searchMatcher!(item, query))
              .toList();
        } else {
          _filteredItems = widget.items
              .where((item) {
                final itemName = widget.displayItemFn(item).toLowerCase();
                final searchQuery = query.toLowerCase();

                // Also search in category if available
                String category = '';
                if (widget.displayCategoryFn != null) {
                  category = widget.displayCategoryFn!(item).toLowerCase();
                }

                return itemName.contains(searchQuery) ||
                       category.contains(searchQuery);
              })
              .toList();
        }
      });
    }

    // Update the overlay
    if (_isOpen) {
      _closeDropdown();
      _openDropdown();
    }
  }

  void _selectItem(T item) {
    // First close the dropdown to prevent Flutter material dropdown assertion error
    _closeDropdown();
    _focusNode.unfocus();

    // Then update the state and call onChanged
    setState(() {
      _selectedItem = item;
      _searchController.text = widget.displayItemFn(item);
    });

    // Call onChanged with the selected item after a short delay
    Future.delayed(const Duration(milliseconds: 50), () {
      widget.onChanged(item);
    });
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, size.height + 5),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              constraints: BoxConstraints(
                maxHeight: 300,
                minWidth: size.width,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      onChanged: _filterItems,
                    ),
                  ),
                  Flexible(
                    child: _filteredItems.isEmpty
                        ? const ListTile(
                            title: Text('No items found'),
                            enabled: false,
                          )
                        : ListView.builder(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            itemCount: _filteredItems.length,
                            itemBuilder: (context, index) {
                              final item = _filteredItems[index];
                              final isSelected = _selectedItem == item;

                              return Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: () => _selectItem(item),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          widget.displayItemFn(item),
                                          style: TextStyle(
                                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                            fontSize: 14,
                                          ),
                                        ),
                                        if (_buildSubtitle(item) != null)
                                          Padding(
                                            padding: const EdgeInsets.only(top: 4),
                                            child: _buildSubtitle(item),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget? _buildSubtitle(T item) {
    if (!widget.showCategory && !widget.showDescription) {
      return null;
    }

    String? categoryText;
    String? descriptionText;

    if (widget.showCategory && widget.displayCategoryFn != null) {
      categoryText = widget.displayCategoryFn!(item);
    }

    if (widget.showDescription && widget.displayDescriptionFn != null) {
      descriptionText = widget.displayDescriptionFn!(item);
    }

    if (categoryText != null && descriptionText != null) {
      return Text('$categoryText - $descriptionText');
    } else if (categoryText != null) {
      return Text(categoryText);
    } else if (descriptionText != null) {
      return Text(descriptionText);
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: InkWell(
        onTap: widget.enabled ? () {
          _focusNode.requestFocus();
          _openDropdown();
        } : null,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.transparent),
          ),
          child: TextField(
            controller: _searchController,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: widget.hintText,
              labelText: widget.labelText,
              suffixIcon: widget.icon ?? const Icon(Icons.arrow_drop_down),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            ),
            enabled: widget.enabled,
            readOnly: true,
          ),
        ),
      ),
    );
  }
}
