import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import '../models/flexible_table_models.dart';
import '../services/flexible_database_service.dart';

class DatabaseManagementScreen extends StatefulWidget {
  const DatabaseManagementScreen({super.key});

  @override
  State<DatabaseManagementScreen> createState() => _DatabaseManagementScreenState();
}

class _DatabaseManagementScreenState extends State<DatabaseManagementScreen> {
  final FlexibleDatabaseService _databaseService = FlexibleDatabaseService();
  List<FlexibleDatabase> _databases = [];
  Map<String, List<FlexibleTable>> _tablesByDatabase = {};
  bool _isLoading = true;
  String? _error;
  String? _databasePath;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final databases = await _databaseService.getAllDatabases();
      final databasePath = await _databaseService.getDatabasePath();
      
      // Load tables for each database
      final tablesByDatabase = <String, List<FlexibleTable>>{};
      for (final database in databases) {
        if (database.databaseId != null) {
          final tables = await _databaseService.getTablesForDatabase(database.databaseId!);
          tablesByDatabase[database.databaseId!] = tables;
        }
      }
      
      setState(() {
        _databases = databases;
        _tablesByDatabase = tablesByDatabase;
        _databasePath = databasePath;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteDatabase(FlexibleDatabase database) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: Text('Are you sure you want to delete the database "${database.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (confirmed == true && database.databaseId != null) {
      try {
        await _databaseService.deleteDatabase(database.databaseId!);
        
        setState(() {
          _databases.removeWhere((db) => db.databaseId == database.databaseId);
          _tablesByDatabase.remove(database.databaseId);
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Database "${database.name}" deleted')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting database: $e')),
          );
        }
      }
    }
  }

  Future<void> _deleteTable(FlexibleTable table) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: Text('Are you sure you want to delete the table "${table.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (confirmed == true && table.tableId != null && table.database.value?.databaseId != null) {
      try {
        await _databaseService.deleteTable(table.tableId!);
        
        setState(() {
          final databaseId = table.database.value!.databaseId!;
          _tablesByDatabase[databaseId]?.removeWhere((tbl) => tbl.tableId == table.tableId);
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Table "${table.name}" deleted')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting table: $e')),
          );
        }
      }
    }
  }

  Future<void> _renameDatabase(FlexibleDatabase database) async {
    final TextEditingController nameController = TextEditingController(text: database.name);
    final TextEditingController descriptionController = TextEditingController(text: database.description);
    
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Database'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Database Name',
                hintText: 'Enter a name for the database',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Enter a description',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a database name')),
                );
                return;
              }
              
              Navigator.of(context).pop({
                'name': nameController.text.trim(),
                'description': descriptionController.text.trim(),
              });
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
    
    if (result != null && database.databaseId != null) {
      try {
        await _databaseService.updateDatabase(
          database.databaseId!,
          name: result['name']!,
          description: result['description']!.isNotEmpty ? result['description'] : null,
        );
        
        setState(() {
          final index = _databases.indexWhere((db) => db.databaseId == database.databaseId);
          if (index >= 0) {
            _databases[index].name = result['name'];
            _databases[index].description = result['description'];
          }
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Database "${result['name']}" updated')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error updating database: $e')),
          );
        }
      }
    }
  }

  Future<void> _renameTable(FlexibleTable table) async {
    final TextEditingController nameController = TextEditingController(text: table.name);
    final TextEditingController descriptionController = TextEditingController(text: table.description);
    
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Table'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Table Name',
                hintText: 'Enter a name for the table',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Enter a description',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a table name')),
                );
                return;
              }
              
              Navigator.of(context).pop({
                'name': nameController.text.trim(),
                'description': descriptionController.text.trim(),
              });
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
    
    if (result != null && table.tableId != null) {
      try {
        await _databaseService.updateTable(
          table.tableId!,
          name: result['name']!,
          description: result['description']!.isNotEmpty ? result['description'] : null,
        );
        
        setState(() {
          final databaseId = table.database.value?.databaseId;
          if (databaseId != null) {
            final tableList = _tablesByDatabase[databaseId];
            if (tableList != null) {
              final index = tableList.indexWhere((tbl) => tbl.tableId == table.tableId);
              if (index >= 0) {
                tableList[index].name = result['name'];
                tableList[index].description = result['description'];
              }
            }
          }
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Table "${result['name']}" updated')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error updating table: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Management'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : _buildContent(),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadData,
        tooltip: 'Refresh',
        child: const Icon(Icons.refresh),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_databasePath != null)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Database Storage Location',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(_databasePath!),
                    const SizedBox(height: 8),
                    Text(
                      'All data is stored locally on your device.',
                      style: TextStyle(color: Colors.grey.shade700),
                    ),
                  ],
                ),
              ),
            ),
          ),
        Expanded(
          child: _databases.isEmpty
              ? const Center(child: Text('No databases found'))
              : ListView.builder(
                  itemCount: _databases.length,
                  itemBuilder: (context, index) {
                    final database = _databases[index];
                    final tables = _tablesByDatabase[database.databaseId] ?? [];
                    
                    return _buildDatabaseCard(database, tables);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildDatabaseCard(FlexibleDatabase database, List<FlexibleTable> tables) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            title: Text(
              database.name ?? 'Unnamed Database',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: database.description != null && database.description!.isNotEmpty
                ? Text(database.description!)
                : null,
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  tooltip: 'Edit Database',
                  onPressed: () => _renameDatabase(database),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'Delete Database',
                  onPressed: () => _deleteDatabase(database),
                ),
              ],
            ),
          ),
          const Divider(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              'Tables (${tables.length})',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          if (tables.isEmpty)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('No tables in this database'),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: tables.length,
              itemBuilder: (context, index) {
                final table = tables[index];
                return ListTile(
                  leading: const Icon(Icons.table_chart),
                  title: Text(table.name ?? 'Unnamed Table'),
                  subtitle: table.description != null && table.description!.isNotEmpty
                      ? Text(table.description!)
                      : null,
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit),
                        tooltip: 'Edit Table',
                        onPressed: () => _renameTable(table),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        tooltip: 'Delete Table',
                        onPressed: () => _deleteTable(table),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
    );
  }
}
