import 'package:flutter/material.dart';
import '../services/database_service.dart';

class ExcelGridWidget extends StatefulWidget {
  final String tableName;
  final DatabaseService databaseService;

  const ExcelGridWidget({
    Key? key,
    required this.tableName,
    required this.databaseService,
  }) : super(key: key);

  @override
  State<ExcelGridWidget> createState() => _ExcelGridWidgetState();
}

class _ExcelGridWidgetState extends State<ExcelGridWidget> {
  List<Map<String, dynamic>> _data = [];
  Map<String, String> _schema = {};
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void didUpdateWidget(ExcelGridWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tableName != widget.tableName) {
      _loadData();
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final schema = await widget.databaseService.getTableSchema(widget.tableName);
      final data = await widget.databaseService.getTableData(widget.tableName);

      setState(() {
        _schema = schema ?? {};
        _data = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _addRow() async {
    try {
      // Create a new row with default values
      final Map<String, dynamic> newRow = {};
      for (String column in _schema.keys) {
        if (!column.startsWith('_')) {
          newRow[column] = _getDefaultValue(_schema[column] ?? 'TEXT');
        }
      }

      await widget.databaseService.insertRow(widget.tableName, newRow);
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Row added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding row: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteRow(String id) async {
    try {
      await widget.databaseService.deleteRow(widget.tableName, id);
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Row deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting row: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateCell(String id, String column, String value) async {
    try {
      // Convert value to appropriate type
      dynamic convertedValue = _convertValue(value, _schema[column] ?? 'TEXT');
      
      await widget.databaseService.updateCell(widget.tableName, id, column, convertedValue);
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cell updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating cell: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addColumn() async {
    final nameController = TextEditingController();
    final typeController = TextEditingController(text: 'TEXT');

    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Column'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Column Name',
                hintText: 'Enter column name',
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: typeController.text,
              decoration: const InputDecoration(labelText: 'Column Type'),
              items: const [
                DropdownMenuItem(value: 'TEXT', child: Text('Text')),
                DropdownMenuItem(value: 'INTEGER', child: Text('Integer')),
                DropdownMenuItem(value: 'DOUBLE', child: Text('Double')),
                DropdownMenuItem(value: 'BOOLEAN', child: Text('Boolean')),
                DropdownMenuItem(value: 'DATE', child: Text('Date')),
              ],
              onChanged: (value) {
                typeController.text = value ?? 'TEXT';
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isNotEmpty) {
                Navigator.pop(context, {
                  'name': nameController.text.trim(),
                  'type': typeController.text,
                });
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );

    if (result != null) {
      try {
        await widget.databaseService.addColumn(
          widget.tableName,
          result['name']!,
          result['type']!,
        );
        await _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Column "${result['name']}" added successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error adding column: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showEditDialog(String id, String column, dynamic currentValue) {
    final controller = TextEditingController(text: currentValue?.toString() ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit $column'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: column,
            hintText: 'Enter value',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateCell(id, column, controller.text);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  dynamic _getDefaultValue(String type) {
    switch (type.toUpperCase()) {
      case 'INTEGER':
        return 0;
      case 'DOUBLE':
        return 0.0;
      case 'BOOLEAN':
        return false;
      case 'DATE':
        return DateTime.now().toIso8601String();
      default:
        return '';
    }
  }

  dynamic _convertValue(String value, String type) {
    switch (type.toUpperCase()) {
      case 'INTEGER':
        return int.tryParse(value) ?? 0;
      case 'DOUBLE':
        return double.tryParse(value) ?? 0.0;
      case 'BOOLEAN':
        return value.toLowerCase() == 'true' || value == '1';
      default:
        return value;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red.shade300),
            const SizedBox(height: 16),
            Text('Error: $_error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_schema.isEmpty) {
      return const Center(
        child: Text('No schema found for this table'),
      );
    }

    // Get visible columns (exclude internal columns)
    final visibleColumns = _schema.keys.where((key) => !key.startsWith('_')).toList();

    return Column(
      children: [
        // Toolbar
        Container(
          padding: const EdgeInsets.all(8.0),
          color: Colors.grey.shade100,
          child: Row(
            children: [
              ElevatedButton.icon(
                onPressed: _addRow,
                icon: const Icon(Icons.add),
                label: const Text('Add Row'),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                onPressed: _addColumn,
                icon: const Icon(Icons.view_column),
                label: const Text('Add Column'),
              ),
              const Spacer(),
              Text('${_data.length} rows'),
            ],
          ),
        ),
        // Data Table
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SingleChildScrollView(
              child: DataTable(
                columns: [
                  const DataColumn(label: Text('Actions')),
                  ...visibleColumns.map((column) => DataColumn(
                    label: Text('$column (${_schema[column]})')
                  )),
                ],
                rows: _data.map((row) {
                  final id = row['_id'] as String;
                  return DataRow(
                    cells: [
                      DataCell(
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () => _deleteRow(id),
                        ),
                      ),
                      ...visibleColumns.map((column) => DataCell(
                        Text(row[column]?.toString() ?? ''),
                        onTap: () => _showEditDialog(id, column, row[column]),
                      )),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
